{"name": "AugmentAPI - 智能API管理平台", "short_name": "AugmentAPI", "description": "现代化的API管理和开发平台，提供完整的API生命周期管理解决方案", "start_url": "/", "display": "standalone", "background_color": "#ffffff", "theme_color": "#3b82f6", "orientation": "portrait-primary", "scope": "/", "lang": "zh-CN", "categories": ["productivity", "developer", "utilities"], "icons": [{"src": "/icons/icon-72x72.png", "sizes": "72x72", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-96x96.png", "sizes": "96x96", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-128x128.png", "sizes": "128x128", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-144x144.png", "sizes": "144x144", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-152x152.png", "sizes": "152x152", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-192x192.png", "sizes": "192x192", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-384x384.png", "sizes": "384x384", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-512x512.png", "sizes": "512x512", "type": "image/png", "purpose": "maskable any"}], "screenshots": [{"src": "/screenshots/desktop-1.png", "sizes": "1280x720", "type": "image/png", "form_factor": "wide", "label": "AugmentAPI桌面端主界面"}, {"src": "/screenshots/mobile-1.png", "sizes": "375x812", "type": "image/png", "form_factor": "narrow", "label": "AugmentAPI移动端主界面"}], "shortcuts": [{"name": "API文档", "short_name": "文档", "description": "查看API文档", "url": "/api-docs", "icons": [{"src": "/icons/shortcut-docs.png", "sizes": "96x96"}]}, {"name": "接口测试", "short_name": "测试", "description": "测试API接口", "url": "/external-test", "icons": [{"src": "/icons/shortcut-test.png", "sizes": "96x96"}]}, {"name": "实时聊天", "short_name": "聊天", "description": "加入开发者聊天", "url": "/chat", "icons": [{"src": "/icons/shortcut-chat.png", "sizes": "96x96"}]}], "related_applications": [{"platform": "webapp", "url": "https://augmentapi.com/manifest.json"}], "prefer_related_applications": false, "edge_side_panel": {"preferred_width": 400}, "launch_handler": {"client_mode": "navigate-existing"}}