import React from 'react'
// import { Navigate, useLocation } from 'react-router-dom'
// import { Loader2 } from 'lucide-react'
// import { useAuthStore } from '@/stores/authStore'

interface ProtectedRouteProps {
  children: React.ReactNode
  adminOnly?: boolean
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  adminOnly = false
}) => {
  // 临时注释掉认证逻辑，方便查看页面效果
  // const location = useLocation()
  // const { isAuthenticated, user, checkAuth } = useAuthStore()
  // const [loading, setLoading] = useState(true)

  // useEffect(() => {
  //   const verifyAuth = async () => {
  //     await checkAuth()
  //     setLoading(false)
  //   }
  //   verifyAuth()
  // }, [checkAuth])

  // // 显示加载状态
  // if (loading) {
  //   return (
  //     <div className="min-h-screen flex items-center justify-center">
  //       <div className="text-center">
  //         <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-blue-500" />
  //         <p className="text-gray-600">验证登录状态...</p>
  //       </div>
  //     </div>
  //   )
  // }

  // // 未登录，重定向到登录页
  // if (!isAuthenticated) {
  //   return (
  //     <Navigate
  //       to={`/login?redirect=${encodeURIComponent(location.pathname + location.search)}`}
  //       replace
  //     />
  //   )
  // }

  // // 需要管理员权限但用户不是管理员
  // if (adminOnly && !user?.isAdmin) {
  //   return (
  //     <div className="min-h-screen flex items-center justify-center">
  //       <div className="text-center">
  //         <div className="w-16 h-16 bg-red-100 rounded-full mx-auto mb-4 flex items-center justify-center">
  //           <span className="text-red-500 text-2xl">⚠️</span>
  //         </div>
  //         <h2 className="text-xl font-semibold text-gray-900 mb-2">
  //           访问被拒绝
  //         </h2>
  //         <p className="text-gray-600 mb-4">
  //           您没有权限访问此页面，需要管理员权限。
  //         </p>
  //         <button
  //           onClick={() => window.history.back()}
  //           className="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors"
  //         </button>
  //       </div>
  //     </div>
  //   )
  // }

  return <>{children}</>
}

export default ProtectedRoute
