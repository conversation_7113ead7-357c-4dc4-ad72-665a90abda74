import React from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Users, Crown, Shield, User } from 'lucide-react'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { cn } from '@/lib/utils'
import { useChatStore } from '@/stores/chatStore'
import { useAuthStore } from '@/stores/authStore'

interface OnlineUser {
  id: string
  username: string
  avatar?: string
  isAdmin: boolean
  trustLevel: number
  joinedAt: string
}

interface OnlineUserListProps {
  className?: string
}

const getTrustLevelInfo = (level: number) => {
  const levels = {
    0: { name: '新用户', color: 'bg-gray-100 text-gray-700', icon: User },
    1: { name: '基础', color: 'bg-blue-100 text-blue-700', icon: User },
    2: { name: '成员', color: 'bg-green-100 text-green-700', icon: User },
    3: { name: '常规', color: 'bg-purple-100 text-purple-700', icon: Shield },
    4: { name: '领导', color: 'bg-yellow-100 text-yellow-700', icon: Crown }
  }
  return levels[level as keyof typeof levels] || levels[0]
}

const OnlineUserList: React.FC<OnlineUserListProps> = ({ className }) => {
  const { onlineUsers, onlineCount } = useChatStore()
  const { user: currentUser } = useAuthStore()

  // 模拟在线用户数据（实际应该从Socket获取）
  const mockUsers: OnlineUser[] = [
    {
      id: '1',
      username: 'admin',
      avatar: '',
      isAdmin: true,
      trustLevel: 4,
      joinedAt: new Date().toISOString()
    },
    {
      id: '2',
      username: 'user123',
      avatar: '',
      isAdmin: false,
      trustLevel: 2,
      joinedAt: new Date().toISOString()
    },
    {
      id: '3',
      username: 'developer',
      avatar: '',
      isAdmin: false,
      trustLevel: 3,
      joinedAt: new Date().toISOString()
    }
  ]

  const displayUsers = onlineUsers.length > 0 ? onlineUsers : mockUsers

  // 按权限和等级排序
  const sortedUsers = [...displayUsers].sort((a, b) => {
    // 管理员优先
    if (a.isAdmin && !b.isAdmin) return -1
    if (!a.isAdmin && b.isAdmin) return 1
    
    // 按信任等级排序
    const aLevel = a.trustLevel || 0
    const bLevel = b.trustLevel || 0
    if (aLevel !== bLevel) return bLevel - aLevel
    
    // 按用户名排序
    return a.username.localeCompare(b.username)
  })

  return (
    <Card className={cn("h-full", className)}>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center justify-between text-base">
          <div className="flex items-center space-x-2">
            <Users className="w-4 h-4" />
            <span>在线用户</span>
          </div>
          <Badge variant="secondary" className="text-xs">
            {onlineCount || displayUsers.length}
          </Badge>
        </CardTitle>
      </CardHeader>
      
      <Separator />
      
      <CardContent className="p-0">
        <div className="max-h-96 overflow-y-auto">
          <AnimatePresence>
            {sortedUsers.map((user, index) => {
              const isCurrentUser = currentUser?.id === user.id
              const trustInfo = getTrustLevelInfo(user.trustLevel || 0)
              const TrustIcon = trustInfo.icon

              return (
                <motion.div
                  key={user.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                  transition={{ delay: index * 0.05 }}
                  className={cn(
                    "flex items-center space-x-3 p-3 hover:bg-gray-50 transition-colors cursor-pointer",
                    isCurrentUser && "bg-blue-50 border-l-2 border-blue-500"
                  )}
                >
                  {/* 头像 */}
                  <div className="relative">
                    <Avatar className="w-8 h-8">
                      <AvatarImage src={user.avatar} />
                      <AvatarFallback className="text-xs">
                        {user.username[0]?.toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    
                    {/* 在线状态指示器 */}
                    <div className="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-green-500 border-2 border-white rounded-full" />
                  </div>

                  {/* 用户信息 */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2">
                      <p className={cn(
                        "text-sm font-medium truncate",
                        isCurrentUser ? "text-blue-700" : "text-gray-900"
                      )}>
                        {user.username}
                        {isCurrentUser && (
                          <span className="text-xs text-blue-500 ml-1">(我)</span>
                        )}
                      </p>
                      
                      {/* 管理员标识 */}
                      {user.isAdmin && (
                        <Crown className="w-3 h-3 text-yellow-500" />
                      )}
                    </div>
                    
                    {/* 信任等级 */}
                    <div className="flex items-center space-x-1 mt-0.5">
                      <TrustIcon className="w-3 h-3 text-gray-400" />
                      <Badge 
                        variant="secondary" 
                        className={cn("text-xs px-1.5 py-0", trustInfo.color)}
                      >
                        {trustInfo.name}
                      </Badge>
                    </div>
                  </div>
                </motion.div>
              )
            })}
          </AnimatePresence>
        </div>

        {/* 底部统计 */}
        <div className="p-3 border-t border-gray-100 bg-gray-50">
          <div className="text-xs text-gray-500 text-center">
            共 {onlineCount || displayUsers.length} 人在线
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default OnlineUserList
