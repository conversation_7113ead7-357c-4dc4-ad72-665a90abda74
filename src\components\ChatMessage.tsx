import React from 'react'
import { motion } from 'framer-motion'
import dayjs from 'dayjs'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { cn } from '@/lib/utils'
import type { ChatMessage as ChatMessageType } from '@/types'
import { useAuthStore } from '@/stores/authStore'

interface ChatMessageProps {
  message: ChatMessageType
  isConsecutive?: boolean
  showAvatar?: boolean
}

const ChatMessage: React.FC<ChatMessageProps> = ({
  message,
  isConsecutive = false,
  showAvatar = true
}) => {
  const { user } = useAuthStore()
  const isOwnMessage = user?.id === message.user_id
  const isSystemMessage = message.type === 'system'

  // 系统消息样式
  if (isSystemMessage) {
    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.2 }}
        className="flex justify-center my-4"
      >
        <div className="bg-gray-100 text-gray-600 text-sm px-4 py-2 rounded-full max-w-md text-center">
          {message.content}
        </div>
      </motion.div>
    )
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className={cn(
        "flex gap-3 px-4 py-2 hover:bg-gray-50 transition-colors",
        isOwnMessage && "flex-row-reverse",
        isConsecutive && "mt-1"
      )}
    >
      {/* 头像 */}
      {showAvatar && !isConsecutive && (
        <div className="flex-shrink-0">
          <Avatar className="w-8 h-8">
            <AvatarImage src={message.avatar} />
            <AvatarFallback className="text-xs">
              {message.username[0]?.toUpperCase()}
            </AvatarFallback>
          </Avatar>
        </div>
      )}

      {/* 占位符（连续消息时保持对齐） */}
      {(!showAvatar || isConsecutive) && (
        <div className="w-8 flex-shrink-0" />
      )}

      {/* 消息内容 */}
      <div className={cn(
        "flex-1 min-w-0",
        isOwnMessage && "text-right"
      )}>
        {/* 用户名和时间（非连续消息时显示） */}
        {!isConsecutive && (
          <div className={cn(
            "flex items-center gap-2 mb-1",
            isOwnMessage && "justify-end"
          )}>
            <span className="text-sm font-medium text-gray-900">
              {message.username}
            </span>
            <span className="text-xs text-gray-500">
              {dayjs(message.created_at).format('HH:mm')}
            </span>
            {isOwnMessage && (
              <Badge variant="secondary" className="text-xs">
                我
              </Badge>
            )}
          </div>
        )}

        {/* 消息气泡 */}
        <div className={cn(
          "inline-block max-w-md break-words",
          isOwnMessage 
            ? "bg-blue-500 text-white rounded-l-2xl rounded-tr-2xl rounded-br-md" 
            : "bg-white border border-gray-200 text-gray-900 rounded-r-2xl rounded-tl-2xl rounded-bl-md shadow-sm"
        )}>
          <div className="px-4 py-2">
            {message.type === 'image' ? (
              <img 
                src={message.content} 
                alt="聊天图片" 
                className="max-w-full h-auto rounded-lg"
                loading="lazy"
              />
            ) : (
              <p className="text-sm leading-relaxed whitespace-pre-wrap">
                {message.content}
              </p>
            )}
          </div>
        </div>

        {/* 连续消息的时间戳 */}
        {isConsecutive && (
          <div className={cn(
            "text-xs text-gray-400 mt-1",
            isOwnMessage && "text-right"
          )}>
            {dayjs(message.created_at).format('HH:mm')}
          </div>
        )}
      </div>
    </motion.div>
  )
}

export default ChatMessage
