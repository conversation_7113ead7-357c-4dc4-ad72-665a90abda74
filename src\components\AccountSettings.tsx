import React, { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { motion } from 'framer-motion'
import { 
  Shield, 
  Key, 
  Mail, 
  Bell, 
  Eye, 
  EyeOff, 
  Loader2,
  <PERSON>ert<PERSON>riangle,
  CheckCircle
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Separator } from '@/components/ui/separator'
import { Badge } from '@/components/ui/badge'
import { useAuthStore } from '@/stores/authStore'

// 密码修改表单验证
const passwordSchema = z.object({
  currentPassword: z.string().min(1, '请输入当前密码'),
  newPassword: z.string().min(8, '新密码至少8个字符').max(100, '密码不能超过100个字符'),
  confirmPassword: z.string()
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: "两次输入的密码不一致",
  path: ["confirmPassword"]
})

type PasswordFormData = z.infer<typeof passwordSchema>

// 通知设置
interface NotificationSettings {
  emailNotifications: boolean
  chatNotifications: boolean
  systemNotifications: boolean
  marketingEmails: boolean
}

// 隐私设置
interface PrivacySettings {
  profileVisibility: 'public' | 'private'
  showEmail: boolean
  showOnlineStatus: boolean
  allowDirectMessages: boolean
}

const AccountSettings: React.FC = () => {
  const { user } = useAuthStore()
  const [showCurrentPassword, setShowCurrentPassword] = useState(false)
  const [showNewPassword, setShowNewPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [isChangingPassword, setIsChangingPassword] = useState(false)
  const [isSavingNotifications, setIsSavingNotifications] = useState(false)
  const [isSavingPrivacy, setIsSavingPrivacy] = useState(false)

  // 通知设置状态
  const [notifications, setNotifications] = useState<NotificationSettings>({
    emailNotifications: true,
    chatNotifications: true,
    systemNotifications: true,
    marketingEmails: false
  })

  // 隐私设置状态
  const [privacy, setPrivacy] = useState<PrivacySettings>({
    profileVisibility: 'public',
    showEmail: false,
    showOnlineStatus: true,
    allowDirectMessages: true
  })

  // 密码表单
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset: resetPasswordForm
  } = useForm<PasswordFormData>({
    resolver: zodResolver(passwordSchema)
  })

  // 修改密码
  const handlePasswordChange = async (data: PasswordFormData) => {
    setIsChangingPassword(true)
    try {
      // TODO: 调用API修改密码
      console.log('修改密码:', data)
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      resetPasswordForm()
      alert('密码修改成功！')
    } catch (error) {
      console.error('密码修改失败:', error)
      alert('密码修改失败，请重试')
    } finally {
      setIsChangingPassword(false)
    }
  }

  // 保存通知设置
  const handleSaveNotifications = async () => {
    setIsSavingNotifications(true)
    try {
      // TODO: 调用API保存通知设置
      console.log('保存通知设置:', notifications)
      
      await new Promise(resolve => setTimeout(resolve, 1000))
      alert('通知设置已保存！')
    } catch (error) {
      console.error('保存通知设置失败:', error)
      alert('保存失败，请重试')
    } finally {
      setIsSavingNotifications(false)
    }
  }

  // 保存隐私设置
  const handleSavePrivacy = async () => {
    setIsSavingPrivacy(true)
    try {
      // TODO: 调用API保存隐私设置
      console.log('保存隐私设置:', privacy)
      
      await new Promise(resolve => setTimeout(resolve, 1000))
      alert('隐私设置已保存！')
    } catch (error) {
      console.error('保存隐私设置失败:', error)
      alert('保存失败，请重试')
    } finally {
      setIsSavingPrivacy(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* 账户安全 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Shield className="w-5 h-5" />
            <span>账户安全</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* 账户信息 */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Label className="text-sm font-medium">邮箱地址</Label>
                <p className="text-sm text-gray-600">{user?.email}</p>
              </div>
              <Badge variant="secondary" className="flex items-center space-x-1">
                <CheckCircle className="w-3 h-3" />
                <span>已验证</span>
              </Badge>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label className="text-sm font-medium">登录方式</Label>
                <p className="text-sm text-gray-600">
                  {user?.linuxdo_username ? 'LinuxDo' : 'GitHub'} 账户登录
                </p>
              </div>
              <Badge variant="outline">
                已绑定
              </Badge>
            </div>
          </div>

          <Separator />

          {/* 修改密码 */}
          <div>
            <h4 className="text-sm font-medium mb-4 flex items-center space-x-2">
              <Key className="w-4 h-4" />
              <span>修改密码</span>
            </h4>
            
            <form onSubmit={handleSubmit(handlePasswordChange)} className="space-y-4">
              {/* 当前密码 */}
              <div className="space-y-2">
                <Label htmlFor="currentPassword">当前密码</Label>
                <div className="relative">
                  <Input
                    id="currentPassword"
                    type={showCurrentPassword ? 'text' : 'password'}
                    {...register('currentPassword')}
                    placeholder="输入当前密码"
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-1 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0"
                    onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                  >
                    {showCurrentPassword ? (
                      <EyeOff className="w-4 h-4" />
                    ) : (
                      <Eye className="w-4 h-4" />
                    )}
                  </Button>
                </div>
                {errors.currentPassword && (
                  <p className="text-sm text-red-500">{errors.currentPassword.message}</p>
                )}
              </div>

              {/* 新密码 */}
              <div className="space-y-2">
                <Label htmlFor="newPassword">新密码</Label>
                <div className="relative">
                  <Input
                    id="newPassword"
                    type={showNewPassword ? 'text' : 'password'}
                    {...register('newPassword')}
                    placeholder="输入新密码"
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-1 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0"
                    onClick={() => setShowNewPassword(!showNewPassword)}
                  >
                    {showNewPassword ? (
                      <EyeOff className="w-4 h-4" />
                    ) : (
                      <Eye className="w-4 h-4" />
                    )}
                  </Button>
                </div>
                {errors.newPassword && (
                  <p className="text-sm text-red-500">{errors.newPassword.message}</p>
                )}
              </div>

              {/* 确认密码 */}
              <div className="space-y-2">
                <Label htmlFor="confirmPassword">确认新密码</Label>
                <div className="relative">
                  <Input
                    id="confirmPassword"
                    type={showConfirmPassword ? 'text' : 'password'}
                    {...register('confirmPassword')}
                    placeholder="再次输入新密码"
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-1 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  >
                    {showConfirmPassword ? (
                      <EyeOff className="w-4 h-4" />
                    ) : (
                      <Eye className="w-4 h-4" />
                    )}
                  </Button>
                </div>
                {errors.confirmPassword && (
                  <p className="text-sm text-red-500">{errors.confirmPassword.message}</p>
                )}
              </div>

              <Button
                type="submit"
                disabled={isChangingPassword}
                className="flex items-center space-x-2"
              >
                {isChangingPassword ? (
                  <Loader2 className="w-4 h-4 animate-spin" />
                ) : (
                  <Key className="w-4 h-4" />
                )}
                <span>{isChangingPassword ? '修改中...' : '修改密码'}</span>
              </Button>
            </form>
          </div>
        </CardContent>
      </Card>

      {/* 通知设置 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Bell className="w-5 h-5" />
            <span>通知设置</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Label className="text-sm font-medium">邮件通知</Label>
                <p className="text-sm text-gray-600">接收重要系统邮件通知</p>
              </div>
              <Switch
                checked={notifications.emailNotifications}
                onCheckedChange={(checked) =>
                  setNotifications(prev => ({ ...prev, emailNotifications: checked }))
                }
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label className="text-sm font-medium">聊天通知</Label>
                <p className="text-sm text-gray-600">新消息提醒</p>
              </div>
              <Switch
                checked={notifications.chatNotifications}
                onCheckedChange={(checked) =>
                  setNotifications(prev => ({ ...prev, chatNotifications: checked }))
                }
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label className="text-sm font-medium">系统通知</Label>
                <p className="text-sm text-gray-600">系统维护和更新通知</p>
              </div>
              <Switch
                checked={notifications.systemNotifications}
                onCheckedChange={(checked) =>
                  setNotifications(prev => ({ ...prev, systemNotifications: checked }))
                }
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label className="text-sm font-medium">营销邮件</Label>
                <p className="text-sm text-gray-600">产品更新和推广信息</p>
              </div>
              <Switch
                checked={notifications.marketingEmails}
                onCheckedChange={(checked) =>
                  setNotifications(prev => ({ ...prev, marketingEmails: checked }))
                }
              />
            </div>
          </div>

          <div className="pt-4">
            <Button
              onClick={handleSaveNotifications}
              disabled={isSavingNotifications}
              className="flex items-center space-x-2"
            >
              {isSavingNotifications ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <Bell className="w-4 h-4" />
              )}
              <span>{isSavingNotifications ? '保存中...' : '保存通知设置'}</span>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 隐私设置 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Eye className="w-5 h-5" />
            <span>隐私设置</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Label className="text-sm font-medium">显示邮箱</Label>
                <p className="text-sm text-gray-600">在个人资料中显示邮箱地址</p>
              </div>
              <Switch
                checked={privacy.showEmail}
                onCheckedChange={(checked) =>
                  setPrivacy(prev => ({ ...prev, showEmail: checked }))
                }
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label className="text-sm font-medium">显示在线状态</Label>
                <p className="text-sm text-gray-600">让其他用户看到您的在线状态</p>
              </div>
              <Switch
                checked={privacy.showOnlineStatus}
                onCheckedChange={(checked) =>
                  setPrivacy(prev => ({ ...prev, showOnlineStatus: checked }))
                }
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label className="text-sm font-medium">允许私信</Label>
                <p className="text-sm text-gray-600">允许其他用户向您发送私信</p>
              </div>
              <Switch
                checked={privacy.allowDirectMessages}
                onCheckedChange={(checked) =>
                  setPrivacy(prev => ({ ...prev, allowDirectMessages: checked }))
                }
              />
            </div>
          </div>

          <div className="pt-4">
            <Button
              onClick={handleSavePrivacy}
              disabled={isSavingPrivacy}
              className="flex items-center space-x-2"
            >
              {isSavingPrivacy ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <Eye className="w-4 h-4" />
              )}
              <span>{isSavingPrivacy ? '保存中...' : '保存隐私设置'}</span>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 危险操作 */}
      <Card className="border-red-200">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2 text-red-600">
            <AlertTriangle className="w-5 h-5" />
            <span>危险操作</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <h4 className="text-sm font-medium text-red-600 mb-2">删除账户</h4>
              <p className="text-sm text-gray-600 mb-4">
                删除账户将永久删除您的所有数据，此操作不可恢复。
              </p>
              <Button variant="destructive" size="sm">
                删除账户
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default AccountSettings
