// 移动端优化工具函数

// 检测设备类型
export const isMobile = () => {
  if (typeof window === 'undefined') return false
  return window.innerWidth < 768
}

export const isTablet = () => {
  if (typeof window === 'undefined') return false
  return window.innerWidth >= 768 && window.innerWidth < 1024
}

export const isDesktop = () => {
  if (typeof window === 'undefined') return false
  return window.innerWidth >= 1024
}

// 检测触摸设备
export const isTouchDevice = () => {
  if (typeof window === 'undefined') return false
  return 'ontouchstart' in window || navigator.maxTouchPoints > 0
}

// 检测iOS设备
export const isIOS = () => {
  if (typeof window === 'undefined') return false
  return /iPad|iPhone|iPod/.test(navigator.userAgent)
}

// 检测Android设备
export const isAndroid = () => {
  if (typeof window === 'undefined') return false
  return /Android/.test(navigator.userAgent)
}

// 获取安全区域信息（用于处理刘海屏等）
export const getSafeAreaInsets = () => {
  if (typeof window === 'undefined') return { top: 0, bottom: 0, left: 0, right: 0 }
  
  const style = getComputedStyle(document.documentElement)
  return {
    top: parseInt(style.getPropertyValue('--safe-area-inset-top') || '0'),
    bottom: parseInt(style.getPropertyValue('--safe-area-inset-bottom') || '0'),
    left: parseInt(style.getPropertyValue('--safe-area-inset-left') || '0'),
    right: parseInt(style.getPropertyValue('--safe-area-inset-right') || '0')
  }
}

// 防抖函数（用于优化滚动和resize事件）
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout | null = null
  
  return (...args: Parameters<T>) => {
    if (timeout) clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}

// 节流函数（用于优化高频事件）
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  limit: number
): ((...args: Parameters<T>) => void) => {
  let inThrottle: boolean
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

// 获取视口尺寸
export const getViewportSize = () => {
  if (typeof window === 'undefined') return { width: 0, height: 0 }
  
  return {
    width: window.innerWidth,
    height: window.innerHeight
  }
}

// 检测网络连接状态
export const getNetworkStatus = () => {
  if (typeof navigator === 'undefined' || !('connection' in navigator)) {
    return { online: true, effectiveType: 'unknown' }
  }
  
  const connection = (navigator as any).connection
  return {
    online: navigator.onLine,
    effectiveType: connection?.effectiveType || 'unknown',
    downlink: connection?.downlink || 0,
    rtt: connection?.rtt || 0
  }
}

// 优化图片加载（懒加载）
export const createImageObserver = (callback: (entry: IntersectionObserverEntry) => void) => {
  if (typeof window === 'undefined') return null
  
  const observer = new IntersectionObserver(
    (entries) => {
      entries.forEach(callback)
    },
    {
      rootMargin: '50px 0px',
      threshold: 0.1
    }
  )
  
  return observer
}

// 触摸手势检测
export interface TouchGesture {
  startX: number
  startY: number
  endX: number
  endY: number
  deltaX: number
  deltaY: number
  direction: 'left' | 'right' | 'up' | 'down' | 'none'
  distance: number
}

export const detectSwipeGesture = (
  startTouch: Touch,
  endTouch: Touch,
  minDistance: number = 50
): TouchGesture => {
  const deltaX = endTouch.clientX - startTouch.clientX
  const deltaY = endTouch.clientY - startTouch.clientY
  const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY)
  
  let direction: TouchGesture['direction'] = 'none'
  
  if (distance >= minDistance) {
    if (Math.abs(deltaX) > Math.abs(deltaY)) {
      direction = deltaX > 0 ? 'right' : 'left'
    } else {
      direction = deltaY > 0 ? 'down' : 'up'
    }
  }
  
  return {
    startX: startTouch.clientX,
    startY: startTouch.clientY,
    endX: endTouch.clientX,
    endY: endTouch.clientY,
    deltaX,
    deltaY,
    direction,
    distance
  }
}

// 虚拟键盘检测
export const detectVirtualKeyboard = (callback: (isVisible: boolean) => void) => {
  if (typeof window === 'undefined') return () => {}
  
  let initialViewportHeight = window.innerHeight
  
  const handleResize = () => {
    const currentHeight = window.innerHeight
    const heightDifference = initialViewportHeight - currentHeight
    
    // 如果高度减少超过150px，认为虚拟键盘打开
    const isKeyboardVisible = heightDifference > 150
    callback(isKeyboardVisible)
  }
  
  window.addEventListener('resize', handleResize)
  
  return () => {
    window.removeEventListener('resize', handleResize)
  }
}

// PWA相关功能
export const isPWAInstalled = () => {
  if (typeof window === 'undefined') return false
  return window.matchMedia('(display-mode: standalone)').matches
}

export const canInstallPWA = () => {
  if (typeof window === 'undefined') return false
  return 'serviceWorker' in navigator && 'PushManager' in window
}

// 性能监控
export const measurePerformance = (name: string, fn: () => void | Promise<void>) => {
  if (typeof performance === 'undefined') {
    return fn()
  }
  
  const start = performance.now()
  const result = fn()
  
  if (result instanceof Promise) {
    return result.finally(() => {
      const end = performance.now()
      console.log(`${name} took ${end - start} milliseconds`)
    })
  } else {
    const end = performance.now()
    console.log(`${name} took ${end - start} milliseconds`)
    return result
  }
}

// 内存使用监控
export const getMemoryUsage = () => {
  if (typeof performance === 'undefined' || !('memory' in performance)) {
    return null
  }
  
  const memory = (performance as any).memory
  return {
    used: Math.round(memory.usedJSHeapSize / 1048576), // MB
    total: Math.round(memory.totalJSHeapSize / 1048576), // MB
    limit: Math.round(memory.jsHeapSizeLimit / 1048576) // MB
  }
}
