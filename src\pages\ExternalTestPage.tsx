import React, { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { motion } from 'framer-motion'
import { 
  Play, 
  Copy, 
  Save, 
  Trash2, 
  Plus,
  Settings,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  Code,
  Download
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'

// 请求表单验证
const requestSchema = z.object({
  method: z.enum(['GET', 'POST', 'PUT', 'DELETE', 'PATCH']),
  url: z.string().url('请输入有效的URL'),
  headers: z.string().optional(),
  body: z.string().optional()
})

type RequestFormData = z.infer<typeof requestSchema>

interface TestRequest {
  id: string
  name: string
  method: string
  url: string
  headers?: Record<string, string>
  body?: string
  createdAt: string
}

interface TestResponse {
  status: number
  statusText: string
  headers: Record<string, string>
  data: any
  duration: number
  timestamp: string
}

const ExternalTestPage: React.FC = () => {
  const [requests, setRequests] = useState<TestRequest[]>([])
  const [currentResponse, setCurrentResponse] = useState<TestResponse | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
    reset
  } = useForm<RequestFormData>({
    resolver: zodResolver(requestSchema),
    defaultValues: {
      method: 'GET',
      url: '',
      headers: '',
      body: ''
    }
  })

  const watchedMethod = watch('method')

  // 发送请求
  const handleSendRequest = async (data: RequestFormData) => {
    setIsLoading(true)
    setError(null)
    const startTime = Date.now()

    try {
      // 解析headers
      let headers: Record<string, string> = {}
      if (data.headers) {
        try {
          headers = JSON.parse(data.headers)
        } catch (e) {
          // 尝试解析为简单的key:value格式
          const lines = data.headers.split('\n')
          lines.forEach(line => {
            const [key, ...valueParts] = line.split(':')
            if (key && valueParts.length > 0) {
              headers[key.trim()] = valueParts.join(':').trim()
            }
          })
        }
      }

      // 构建请求配置
      const config: RequestInit = {
        method: data.method,
        headers: {
          'Content-Type': 'application/json',
          ...headers
        }
      }

      // 添加请求体（如果不是GET请求）
      if (data.method !== 'GET' && data.body) {
        config.body = data.body
      }

      // 发送请求
      const response = await fetch(data.url, config)
      const responseData = await response.text()
      const duration = Date.now() - startTime

      // 尝试解析JSON
      let parsedData
      try {
        parsedData = JSON.parse(responseData)
      } catch (e) {
        parsedData = responseData
      }

      // 构建响应对象
      const testResponse: TestResponse = {
        status: response.status,
        statusText: response.statusText,
        headers: Object.fromEntries(response.headers.entries()),
        data: parsedData,
        duration,
        timestamp: new Date().toISOString()
      }

      setCurrentResponse(testResponse)
    } catch (error) {
      console.error('请求失败:', error)
      setError(error instanceof Error ? error.message : '请求失败')
    } finally {
      setIsLoading(false)
    }
  }

  // 保存请求
  const handleSaveRequest = () => {
    const formData = watch()
    const newRequest: TestRequest = {
      id: Date.now().toString(),
      name: `${formData.method} ${new URL(formData.url).pathname}`,
      method: formData.method,
      url: formData.url,
      headers: formData.headers ? JSON.parse(formData.headers) : undefined,
      body: formData.body,
      createdAt: new Date().toISOString()
    }

    setRequests(prev => [newRequest, ...prev])
  }

  // 加载保存的请求
  const handleLoadRequest = (request: TestRequest) => {
    setValue('method', request.method as any)
    setValue('url', request.url)
    setValue('headers', request.headers ? JSON.stringify(request.headers, null, 2) : '')
    setValue('body', request.body || '')
  }

  // 删除保存的请求
  const handleDeleteRequest = (id: string) => {
    setRequests(prev => prev.filter(req => req.id !== id))
  }

  // 复制响应数据
  const handleCopyResponse = async () => {
    if (currentResponse) {
      try {
        await navigator.clipboard.writeText(JSON.stringify(currentResponse.data, null, 2))
        // TODO: 显示复制成功提示
      } catch (error) {
        console.error('复制失败:', error)
      }
    }
  }

  // 获取状态颜色
  const getStatusColor = (status: number) => {
    if (status >= 200 && status < 300) return 'text-green-600 bg-green-50'
    if (status >= 300 && status < 400) return 'text-blue-600 bg-blue-50'
    if (status >= 400 && status < 500) return 'text-orange-600 bg-orange-50'
    return 'text-red-600 bg-red-50'
  }

  // 获取方法颜色
  const getMethodColor = (method: string) => {
    const colors = {
      GET: 'bg-green-100 text-green-700',
      POST: 'bg-blue-100 text-blue-700',
      PUT: 'bg-orange-100 text-orange-700',
      DELETE: 'bg-red-100 text-red-700',
      PATCH: 'bg-purple-100 text-purple-700'
    }
    return colors[method as keyof typeof colors] || 'bg-gray-100 text-gray-700'
  }

  return (
    <div className="p-6 max-w-7xl mx-auto">
      {/* 页面标题 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="mb-8"
      >
        <div className="flex items-center space-x-3 mb-4">
          <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-600 rounded-lg flex items-center justify-center">
            <Settings className="w-5 h-5 text-white" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">外部接口测试</h1>
            <p className="text-gray-600">测试和调试外部API接口</p>
          </div>
        </div>
      </motion.div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 请求配置区域 */}
        <div className="lg:col-span-2 space-y-6">
          {/* 请求表单 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>请求配置</span>
                <div className="flex space-x-2">
                  <Button
                    onClick={handleSaveRequest}
                    variant="outline"
                    size="sm"
                    className="flex items-center space-x-2"
                  >
                    <Save className="w-4 h-4" />
                    <span>保存</span>
                  </Button>
                  <Button
                    onClick={() => reset()}
                    variant="outline"
                    size="sm"
                    className="flex items-center space-x-2"
                  >
                    <Trash2 className="w-4 h-4" />
                    <span>清空</span>
                  </Button>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit(handleSendRequest)} className="space-y-4">
                {/* URL和方法 */}
                <div className="flex space-x-2">
                  <div className="w-32">
                    <Select 
                      value={watchedMethod} 
                      onValueChange={(value) => setValue('method', value as any)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="GET">GET</SelectItem>
                        <SelectItem value="POST">POST</SelectItem>
                        <SelectItem value="PUT">PUT</SelectItem>
                        <SelectItem value="DELETE">DELETE</SelectItem>
                        <SelectItem value="PATCH">PATCH</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="flex-1">
                    <Input
                      {...register('url')}
                      placeholder="输入API URL，例如：https://api.example.com/users"
                    />
                    {errors.url && (
                      <p className="text-sm text-red-500 mt-1">{errors.url.message}</p>
                    )}
                  </div>
                  <Button
                    type="submit"
                    disabled={isLoading}
                    className="flex items-center space-x-2"
                  >
                    {isLoading ? (
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                    ) : (
                      <Play className="w-4 h-4" />
                    )}
                    <span>{isLoading ? '发送中...' : '发送'}</span>
                  </Button>
                </div>

                {/* Headers */}
                <div className="space-y-2">
                  <Label htmlFor="headers">请求头 (JSON格式或key:value格式)</Label>
                  <Textarea
                    id="headers"
                    {...register('headers')}
                    placeholder={`JSON格式：
{
  "Authorization": "Bearer token",
  "Content-Type": "application/json"
}

或简单格式：
Authorization: Bearer token
Content-Type: application/json`}
                    rows={4}
                  />
                </div>

                {/* Body */}
                {watchedMethod !== 'GET' && (
                  <div className="space-y-2">
                    <Label htmlFor="body">请求体 (JSON格式)</Label>
                    <Textarea
                      id="body"
                      {...register('body')}
                      placeholder={`{
  "name": "John Doe",
  "email": "<EMAIL>"
}`}
                      rows={6}
                    />
                  </div>
                )}
              </form>
            </CardContent>
          </Card>

          {/* 响应区域 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>响应结果</span>
                {currentResponse && (
                  <div className="flex items-center space-x-2">
                    <Badge className={getStatusColor(currentResponse.status)}>
                      {currentResponse.status} {currentResponse.statusText}
                    </Badge>
                    <Badge variant="outline" className="flex items-center space-x-1">
                      <Clock className="w-3 h-3" />
                      <span>{currentResponse.duration}ms</span>
                    </Badge>
                    <Button
                      onClick={handleCopyResponse}
                      variant="ghost"
                      size="sm"
                    >
                      <Copy className="w-4 h-4" />
                    </Button>
                  </div>
                )}
              </CardTitle>
            </CardHeader>
            <CardContent>
              {error ? (
                <div className="flex items-center space-x-2 text-red-600 bg-red-50 p-4 rounded-lg">
                  <XCircle className="w-5 h-5" />
                  <span>{error}</span>
                </div>
              ) : currentResponse ? (
                <Tabs defaultValue="body" className="w-full">
                  <TabsList className="grid w-full grid-cols-3">
                    <TabsTrigger value="body">响应体</TabsTrigger>
                    <TabsTrigger value="headers">响应头</TabsTrigger>
                    <TabsTrigger value="info">请求信息</TabsTrigger>
                  </TabsList>
                  
                  <TabsContent value="body" className="mt-4">
                    <div className="bg-gray-50 rounded-lg p-4">
                      <pre className="text-sm overflow-x-auto">
                        <code>
                          {typeof currentResponse.data === 'string' 
                            ? currentResponse.data 
                            : JSON.stringify(currentResponse.data, null, 2)
                          }
                        </code>
                      </pre>
                    </div>
                  </TabsContent>
                  
                  <TabsContent value="headers" className="mt-4">
                    <div className="bg-gray-50 rounded-lg p-4">
                      <pre className="text-sm overflow-x-auto">
                        <code>
                          {JSON.stringify(currentResponse.headers, null, 2)}
                        </code>
                      </pre>
                    </div>
                  </TabsContent>
                  
                  <TabsContent value="info" className="mt-4">
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">状态码:</span>
                        <Badge className={getStatusColor(currentResponse.status)}>
                          {currentResponse.status} {currentResponse.statusText}
                        </Badge>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">响应时间:</span>
                        <span className="text-sm font-mono">{currentResponse.duration}ms</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">时间戳:</span>
                        <span className="text-sm font-mono">
                          {new Date(currentResponse.timestamp).toLocaleString()}
                        </span>
                      </div>
                    </div>
                  </TabsContent>
                </Tabs>
              ) : (
                <div className="text-center py-12">
                  <Code className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                  <p className="text-gray-500">发送请求后，响应结果将显示在这里</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* 保存的请求列表 */}
        <div>
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Save className="w-5 h-5" />
                <span>保存的请求</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {requests.length === 0 ? (
                <div className="text-center py-8">
                  <Save className="w-12 h-12 text-gray-300 mx-auto mb-3" />
                  <p className="text-gray-500 text-sm">暂无保存的请求</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {requests.map((request) => (
                    <div
                      key={request.id}
                      className="border border-gray-200 rounded-lg p-3 hover:bg-gray-50 transition-colors"
                    >
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          <Badge className={`text-xs ${getMethodColor(request.method)}`}>
                            {request.method}
                          </Badge>
                          <span className="text-sm font-medium truncate">
                            {request.name}
                          </span>
                        </div>
                        <Button
                          onClick={() => handleDeleteRequest(request.id)}
                          variant="ghost"
                          size="sm"
                          className="text-red-500 hover:text-red-700"
                        >
                          <Trash2 className="w-3 h-3" />
                        </Button>
                      </div>
                      <p className="text-xs text-gray-500 mb-2 truncate">
                        {request.url}
                      </p>
                      <Button
                        onClick={() => handleLoadRequest(request)}
                        variant="outline"
                        size="sm"
                        className="w-full text-xs"
                      >
                        加载请求
                      </Button>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>

          {/* 快速模板 */}
          <Card className="mt-6">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Plus className="w-5 h-5" />
                <span>快速模板</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <Button
                  onClick={() => {
                    setValue('method', 'GET')
                    setValue('url', 'https://jsonplaceholder.typicode.com/posts')
                    setValue('headers', '')
                    setValue('body', '')
                  }}
                  variant="outline"
                  size="sm"
                  className="w-full justify-start text-xs"
                >
                  GET 示例
                </Button>
                <Button
                  onClick={() => {
                    setValue('method', 'POST')
                    setValue('url', 'https://jsonplaceholder.typicode.com/posts')
                    setValue('headers', JSON.stringify({
                      'Content-Type': 'application/json'
                    }, null, 2))
                    setValue('body', JSON.stringify({
                      title: 'foo',
                      body: 'bar',
                      userId: 1
                    }, null, 2))
                  }}
                  variant="outline"
                  size="sm"
                  className="w-full justify-start text-xs"
                >
                  POST 示例
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}

export default ExternalTestPage
