import { authApi } from './auth'
import type { 
  ApiResponse, 
  SiteStats, 
  Announcement, 
  SponsorRecord,
  DownloadInfo 
} from '@/types'

// 统计相关API
export const statsApi = {
  // 获取网站统计数据
  getSiteStats: async (): Promise<SiteStats> => {
    const response = await authApi.get<ApiResponse<SiteStats>>('/statistics')
    return response.data.data || {
      onlineUsers: 0,
      users: { total: 0, todayNew: 0, yesterdayNew: 0 }
    }
  }
}

// 公告相关API
export const announcementApi = {
  // 获取公告列表
  getAnnouncements: async (): Promise<Announcement[]> => {
    const response = await authApi.get<ApiResponse<Announcement[]>>('/announcements')
    return response.data.data || []
  },

  // 标记公告为已读
  markAsRead: async (announcementId: string): Promise<void> => {
    await authApi.post(`/announcements/${announcementId}/read`)
  }
}

// 赞助相关API
export const sponsorApi = {
  // 获取赞助排行榜
  getSponsorRanking: async (): Promise<SponsorRecord[]> => {
    const response = await authApi.get<ApiResponse<SponsorRecord[]>>('/sponsors/ranking')
    return response.data.data || []
  },

  // 提交赞助记录
  submitSponsor: async (amount: number): Promise<void> => {
    await authApi.post('/sponsors', { amount })
  }
}

// 下载相关API
export const downloadApi = {
  // 获取下载权限信息
  getDownloadInfo: async (): Promise<DownloadInfo> => {
    const response = await authApi.get<ApiResponse<DownloadInfo>>('/downloads/info')
    return response.data.data || {
      checked: false,
      hasPermission: false,
      files: []
    }
  },

  // 下载文件
  downloadFile: async (fileId: string): Promise<Blob> => {
    const response = await authApi.get(`/downloads/${fileId}`, {
      responseType: 'blob'
    })
    return response.data
  }
}

// 用户管理API（管理员）
export const adminApi = {
  // 获取用户列表
  getUsers: async (page = 1, limit = 20): Promise<{
    users: any[]
    total: number
    page: number
    limit: number
  }> => {
    const response = await authApi.get('/admin/users', {
      params: { page, limit }
    })
    return response.data.data
  },

  // 更新用户状态
  updateUserStatus: async (userId: string, status: string): Promise<void> => {
    await authApi.patch(`/admin/users/${userId}/status`, { status })
  },

  // 删除用户
  deleteUser: async (userId: string): Promise<void> => {
    await authApi.delete(`/admin/users/${userId}`)
  },

  // 创建公告
  createAnnouncement: async (announcement: Omit<Announcement, 'id' | 'created_at' | 'updated_at'>): Promise<void> => {
    await authApi.post('/admin/announcements', announcement)
  },

  // 更新公告
  updateAnnouncement: async (id: string, announcement: Partial<Announcement>): Promise<void> => {
    await authApi.patch(`/admin/announcements/${id}`, announcement)
  },

  // 删除公告
  deleteAnnouncement: async (id: string): Promise<void> => {
    await authApi.delete(`/admin/announcements/${id}`)
  }
}
