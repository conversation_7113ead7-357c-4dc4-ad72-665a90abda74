(function () {
  try {
    var search = window.location.search || '';
    var hostname = window.location.hostname || '';

    // 检查调试模式：URL参数、localStorage或本地开发环境
    var debug = /(?:^|[?&])debug=(1|true)\b/i.test(search) ||
                localStorage.getItem('__DEBUG__') === '1' ||
                hostname === 'localhost' ||
                hostname === '127.0.0.1';

    // 生产环境强制禁用（除非明确开启调试）
    var isProduction = hostname !== 'localhost' && hostname !== '127.0.0.1';
    var forceDisable = isProduction && !(/(?:^|[?&])debug=(1|true)\b/i.test(search));

    if (!debug || forceDisable) {
      var noop = function () {};
      var methods = ['log', 'debug', 'info', 'warn', 'error', 'trace', 'table', 'group', 'groupEnd', 'time', 'timeEnd'];

      for (var i = 0; i < methods.length; i++) {
        try {
          if (typeof console !== 'undefined' && console[methods[i]]) {
            console[methods[i]] = noop;
          }
        } catch (e) {}
      }

      // 额外禁用可能的调试方法
      try {
        if (typeof console !== 'undefined') {
          console.clear = noop;
          console.dir = noop;
          console.dirxml = noop;
          console.count = noop;
          console.countReset = noop;
          console.assert = noop;
        }
      } catch (e) {}

      // 在生产环境下显示提示（仅一次）
      if (isProduction && !sessionStorage.getItem('console_disabled_notice')) {
        sessionStorage.setItem('console_disabled_notice', '1');
        setTimeout(function() {
          try {
            var originalLog = Function.prototype.bind.call(console.log, console);
            originalLog('🔒 控制台调试已在生产环境中禁用。如需调试，请在URL后添加 ?debug=1');
          } catch (e) {}
        }, 1000);
      }
    }
  } catch (e) {}
})();

