import { io, Socket } from 'socket.io-client'
import { authTokens } from './auth'
import type { ChatMessage } from '@/types'

class SocketManager {
  private socket: Socket | null = null
  private reconnectAttempts = 0
  private maxReconnectAttempts = 5
  private reconnectDelay = 1000

  // 事件监听器
  private listeners: Map<string, Set<Function>> = new Map()

  constructor() {
    this.connect()
  }

  // 连接Socket.IO服务器
  connect() {
    if (this.socket?.connected) {
      return
    }

    const token = authTokens.get()
    if (!token) {
      console.warn('No auth token found, cannot connect to socket')
      return
    }

    this.socket = io('/', {
      auth: {
        token
      },
      transports: ['websocket', 'polling'],
      timeout: 10000,
      forceNew: true
    })

    this.setupEventHandlers()
  }

  // 设置事件处理器
  private setupEventHandlers() {
    if (!this.socket) return

    // 连接成功
    this.socket.on('connect', () => {
      console.log('Socket connected:', this.socket?.id)
      this.reconnectAttempts = 0
      this.emit('connected', this.socket?.id)
    })

    // 连接断开
    this.socket.on('disconnect', (reason) => {
      console.log('Socket disconnected:', reason)
      this.emit('disconnected', reason)
      
      // 自动重连
      if (reason === 'io server disconnect') {
        // 服务器主动断开，需要手动重连
        this.reconnect()
      }
    })

    // 连接错误
    this.socket.on('connect_error', (error) => {
      console.error('Socket connection error:', error)
      this.emit('error', error)
      this.reconnect()
    })

    // 认证错误
    this.socket.on('auth_error', (error) => {
      console.error('Socket auth error:', error)
      this.emit('auth_error', error)
    })

    // 聊天消息
    this.socket.on('chat_message', (message: ChatMessage) => {
      this.emit('chat_message', message)
    })

    // 用户加入/离开
    this.socket.on('user_joined', (data) => {
      this.emit('user_joined', data)
    })

    this.socket.on('user_left', (data) => {
      this.emit('user_left', data)
    })

    // 在线用户更新
    this.socket.on('online_users_update', (count: number) => {
      this.emit('online_users_update', count)
    })

    // 系统消息
    this.socket.on('system_message', (message) => {
      this.emit('system_message', message)
    })
  }

  // 重连逻辑
  private reconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('Max reconnection attempts reached')
      this.emit('max_reconnect_attempts_reached')
      return
    }

    this.reconnectAttempts++
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1)
    
    console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts}) in ${delay}ms`)
    
    setTimeout(() => {
      this.connect()
    }, delay)
  }

  // 发送聊天消息
  sendMessage(content: string) {
    if (!this.socket?.connected) {
      throw new Error('Socket not connected')
    }

    this.socket.emit('chat_message', { content })
  }

  // 加入聊天室
  joinRoom(roomId: string) {
    if (!this.socket?.connected) {
      throw new Error('Socket not connected')
    }

    this.socket.emit('join_room', { roomId })
  }

  // 离开聊天室
  leaveRoom(roomId: string) {
    if (!this.socket?.connected) {
      throw new Error('Socket not connected')
    }

    this.socket.emit('leave_room', { roomId })
  }

  // 获取在线用户列表
  getOnlineUsers() {
    if (!this.socket?.connected) {
      throw new Error('Socket not connected')
    }

    return new Promise((resolve) => {
      this.socket!.emit('get_online_users', (users: any[]) => {
        resolve(users)
      })
    })
  }

  // 事件监听
  on(event: string, callback: Function) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, new Set())
    }
    this.listeners.get(event)!.add(callback)
  }

  // 移除事件监听
  off(event: string, callback?: Function) {
    if (!this.listeners.has(event)) return

    if (callback) {
      this.listeners.get(event)!.delete(callback)
    } else {
      this.listeners.get(event)!.clear()
    }
  }

  // 触发事件
  private emit(event: string, ...args: any[]) {
    if (!this.listeners.has(event)) return

    this.listeners.get(event)!.forEach(callback => {
      try {
        callback(...args)
      } catch (error) {
        console.error(`Error in socket event handler for ${event}:`, error)
      }
    })
  }

  // 断开连接
  disconnect() {
    if (this.socket) {
      this.socket.disconnect()
      this.socket = null
    }
    this.listeners.clear()
  }

  // 获取连接状态
  get connected() {
    return this.socket?.connected || false
  }

  // 获取Socket ID
  get id() {
    return this.socket?.id
  }
}

// 单例模式
export const socketManager = new SocketManager()

// 便捷的Hook接口
export const useSocket = () => {
  return {
    socket: socketManager,
    connected: socketManager.connected,
    id: socketManager.id
  }
}
