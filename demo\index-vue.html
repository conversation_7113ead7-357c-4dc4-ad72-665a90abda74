<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AugmentAPI - 首页</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>🧸</text></svg>">
    
    <!-- 引入Element Plus CSS - 本地资源 -->
    <link rel="stylesheet" href="/lib/element-plus/index.min.css">

    <!-- 引入Vue 3 - 本地资源 -->
    <script src="/lib/vue/vue.global.min.js"></script>
    <!-- 引入Element Plus - 本地资源 -->
    <script src="/lib/element-plus/index.full.min.js"></script>
    <!-- 引入Element Plus图标 - 本地资源 -->
    <script src="/lib/element-plus/icons.iife.min.js"></script>
    <!-- 引入Socket.IO客户端 - 本地资源 -->
    <script src="/lib/socket.io/socket.io.min.js"></script>

    <!-- 认证工具函数 -->
    <script src="/js/auth-utils.js"></script>
    <!-- 静默控制台（生产环境隐藏console输出；加 ?debug=1 显示） -->
    <script src="/js/quiet-console.js"></script>

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: #f5f7fa;
        }

        /* 下载面板动画 */
        .download-panel {
            animation: slideInUp 0.6s cubic-bezier(0.16, 1, 0.3, 1);
        }

        .download-card {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            transform: translateY(0);
        }

        .download-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 40px rgba(64, 158, 255, 0.15) !important;
        }

        /* 侧边栏下载区域样式 */
        .sidebar-download-area {
            position: absolute;
            bottom: 20px;
            left: 0;
            right: 0;
            padding: 0 16px;
        }

        .download-fab {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 48px;
            background: linear-gradient(135deg, #409EFF 0%, #67C23A 100%);
            border-radius: 24px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
            gap: 8px;
        }

        .download-fab:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(64, 158, 255, 0.4);
        }

        .download-icon {
            font-size: 18px;
        }

        .download-text {
            color: white;
            font-weight: 500;
            font-size: 14px;
        }

        .download-list {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            border: 1px solid #E4E7ED;
            overflow: hidden;
        }

        .download-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 16px;
            background: #F5F7FA;
            cursor: pointer;
            border-bottom: 1px solid #E4E7ED;
        }

        .download-title {
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 500;
            color: #303133;
        }

        .collapse-btn {
            font-size: 12px;
            color: #909399;
        }

        .permission-badge {
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 8px;
            background: linear-gradient(135deg, #67C23A 0%, #409EFF 100%);
            color: white;
            font-weight: 500;
        }

        .download-items {
            max-height: 200px;
            overflow-y: auto;
        }

        .download-item-mini {
            display: flex;
            align-items: center;
            padding: 8px 16px;
            cursor: pointer;
            transition: background 0.2s ease;
            gap: 8px;
        }

        .download-item-mini:hover {
            background: #F5F7FA;
        }

        .file-icon {
            font-size: 14px;
            width: 20px;
            text-align: center;
        }

        .file-name {
            flex: 1;
            font-size: 12px;
            color: #606266;
        }

        .download-btn-mini {
            font-size: 12px;
            color: #409EFF;
        }

        .download-locked {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 48px;
            background: #F5F5F5;
            border-radius: 24px;
            gap: 8px;
            color: #909399;
        }

        .lock-icon {
            font-size: 16px;
        }

        .lock-text {
            font-size: 12px;
        }

        /* 侧边栏收起时的样式调整 */
        .sidebar.collapsed .download-fab {
            width: 48px;
            height: 48px;
            border-radius: 50%;
        }

        .sidebar.collapsed .download-list {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .sidebar.collapsed .download-locked {
            width: 48px;
            height: 48px;
            border-radius: 50%;
        }

        /* 移动端适配 */
        @media (max-width: 768px) {
            .download-panel {
                left: 10px !important;
                right: 10px;
                max-width: calc(100vw - 20px);
            }

            .download-card {
                width: 100% !important;
                max-width: none !important;
            }

            .sidebar-download-area {
                display: none; /* 移动端隐藏侧边栏下载区域 */
            }
        }

        /* 移动端下载面板 */
        @media (max-width: 768px) {
            .mobile-download-panel {
                position: fixed;
                right: 20px;
                bottom: 20px;
                z-index: 1001;
            }

            .mobile-download-fab {
                width: 56px;
                height: 56px;
                border-radius: 50%;
                background: linear-gradient(135deg, #409EFF 0%, #67C23A 100%);
                display: flex;
                align-items: center;
                justify-content: center;
                box-shadow: 0 4px 16px rgba(64, 158, 255, 0.4);
                cursor: pointer;
                font-size: 20px;
            }
        }

        /* 权限对话框样式 */
        .permission-dialog .el-message-box__content {
            padding: 20px !important;
        }

        .permission-dialog .el-message-box__header {
            padding: 20px 20px 10px 20px !important;
        }

        .download-item {
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .download-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }

        .download-button {
            transition: all 0.3s ease;
        }

        .download-button:hover {
            animation: pulse 0.6s ease-in-out;
        }
        
        .layout-container {
            height: 100vh;
            display: flex;
        }
        
        .sidebar {
            background: #fff;
            box-shadow: 2px 0 6px rgba(0,21,41,.35);
            transition: width 0.3s;
            overflow: hidden;
            position: relative;
            display: flex;
            flex-direction: column;
            width: 250px;
        }

        .menu-container {
            flex: 1;
            overflow-y: auto;
            padding-bottom: 100px; /* 为下载区域留出空间 */
        }
        
        .sidebar.collapsed {
            width: 64px !important;
        }

        .sidebar.collapsed .user-info {
            justify-content: center;
            padding: 0;
        }

        .sidebar.collapsed .user-details {
            display: none;
        }

        .sidebar.collapsed .user-avatar {
            margin-right: 0;
            flex-shrink: 0;
        }

        .sidebar.collapsed .el-menu-item span,
        .sidebar.collapsed .el-sub-menu .el-sub-menu__title span {
            display: none;
        }

        .sidebar.collapsed .el-sub-menu .el-sub-menu__icon-arrow {
            display: none;
        }

        .sidebar.expanded {
            width: 250px !important;
        }
        
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .content-header {
            background: #fff;
            padding: 16px 24px;
            border-bottom: 1px solid #e8eaec;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .content-body {
            flex: 1;
            padding: 24px;
            overflow-y: auto;
            background: #f5f7fa;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            padding: 16px;
            border-bottom: 1px solid #e8eaec;
        }
        
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin-right: 12px;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #409eff;
            color: white;
            font-weight: bold;
        }

        .user-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        /* 当有头像图片时，移除背景色 */
        .user-avatar:has(img) {
            background: transparent;
        }
        
        .user-details {
            flex: 1;
            min-width: 0;
        }
        
        .user-name {
            font-weight: 600;
            color: #303133;
            font-size: 14px;
            margin-bottom: 4px;
            display: flex;
            align-items: center;
        }
        
        .user-email {
            color: #909399;
            font-size: 12px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .trust-level {
            margin-left: 8px;
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 10px;
            font-weight: 600;
        }
        
        .trust-level-0 { background: #f4f4f5; color: #909399; }
        .trust-level-1 { background: #ecf5ff; color: #409eff; }
        .trust-level-2 { background: #f0f9ff; color: #67c23a; }
        .trust-level-3 { background: #fdf6ec; color: #e6a23c; }
        .trust-level-4 { background: #fef0f0; color: #f56c6c; }
        
        .sidebar-toggle {
            position: absolute;
            top: 16px;
            right: -12px;
            width: 24px;
            height: 24px;
            background: #409eff;
            border: none;
            border-radius: 50%;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            z-index: 1000;
        }
        
        .sidebar-toggle:hover {
            background: #337ecc;
        }
        
        .welcome-card {
            background: linear-gradient(135deg, #ffffff 0%, #f8fbff 80%, #e6f3ff 100%);
            color: #303133;
            border-radius: 12px;
            padding: 40px;
            margin-bottom: 24px;
            text-align: center;
            border: 1px solid #e3e6ea;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        
        .welcome-card h1 {
            font-size: 2.5em;
            margin-bottom: 16px;
            color: #2d3748;
        }

        .welcome-card p {
            font-size: 1.2em;
            color: #4a5568;
            margin-bottom: 8px;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 24px;
            margin-bottom: 24px;
        }
        
        .feature-card {
            background: #fff;
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            text-align: center;
            transition: transform 0.2s;
        }
        
        .feature-card:hover {
            transform: translateY(-4px);
        }
        
        .feature-icon {
            font-size: 3em;
            margin-bottom: 16px;
        }
        
        .feature-title {
            font-size: 1.3em;
            font-weight: 600;
            color: #303133;
            margin-bottom: 12px;
        }
        
        .feature-desc {
            color: #606266;
            line-height: 1.6;
        }

        .auth-card {
            background: #fff;
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 24px;
        }

        .auth-title {
            font-size: 1.5em;
            color: #303133;
            margin-bottom: 24px;
            text-align: center;
            font-weight: 600;
        }

        .auth-step {
            margin-bottom: 32px;
            padding: 20px;
            border: 1px solid #e8eaec;
            border-radius: 8px;
            background: #fafbfc;
        }

        .auth-step h3 {
            color: #303133;
            margin-bottom: 12px;
            font-size: 1.2em;
            display: flex;
            align-items: center;
        }

        .step-number {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 24px;
            height: 24px;
            background: #409eff;
            color: white;
            border-radius: 50%;
            font-size: 14px;
            font-weight: bold;
            margin-right: 8px;
        }

        .auth-step p {
            color: #606266;
            margin-bottom: 16px;
            line-height: 1.6;
        }

        .auth-result {
            margin-top: 16px;
        }

        .auth-url-display {
            margin: 12px 0;
        }

        .auth-actions {
            display: flex;
            gap: 12px;
            margin-top: 12px;
            flex-wrap: wrap;
        }

        .auth-error {
            margin-top: 16px;
        }

        .token-result {
            margin-top: 16px;
        }

        .token-display {
            margin: 12px 0;
        }

        .token-display label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #303133;
        }
        
        /* 移动端适配 */
        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                left: 0;
                top: 0;
                height: 100vh;
                z-index: 1000;
                transform: translateX(-100%);
                transition: transform 0.3s;
            }
            
            .sidebar.mobile-open {
                transform: translateX(0);
            }
            
            .main-content {
                width: 100%;
            }
            
            .content-body {
                padding: 16px;
            }
            
            .welcome-card {
                padding: 24px;
            }
            
            .welcome-card h1 {
                font-size: 2em;
            }

            /* 公告弹窗移动端适配 */
            .announcement-dialog .el-dialog {
                margin: 20px auto !important;
                max-height: calc(100vh - 40px) !important;
                width: 90vw !important;
                max-width: 400px !important;
            }

            .announcement-dialog .el-dialog__header {
                padding: 16px 20px 12px !important;
            }

            .announcement-dialog .el-dialog__body {
                padding: 12px 20px 20px !important;
                max-height: calc(100vh - 140px);
                overflow-y: auto;
            }

            .announcement-dialog .el-dialog__title {
                font-size: 16px !important;
            }

            .announcement-content {
                max-height: calc(100vh - 200px);
                font-size: 13px !important;
                line-height: 1.6;
            }

            .announcement-title {
                flex-wrap: wrap;
                gap: 4px;
            }

            .announcement-title span {
                font-size: 14px !important;
            }

            .announcement-meta {
                font-size: 11px !important;
            }
        }

        /* 公告弹窗样式 */
        .announcement-dialog .el-dialog {
            border-radius: 12px;
            overflow: hidden;
            max-width: 500px;
            width: 500px !important;
        }

        .announcement-dialog .el-dialog__header {
            background: linear-gradient(135deg, #f0f9ff 0%, #ffffff 100%);
            border-bottom: 1px solid #e4e7ed;
            padding: 20px 24px 16px;
        }

        .announcement-dialog .el-dialog__title {
            color: #1890ff;
            font-weight: 600;
        }

        .announcement-dialog-content {
            padding: 8px 0;
        }

        .announcement-header {
            margin-bottom: 16px;
        }

        .announcement-title {
            display: flex;
            align-items: center;
            color: #303133;
            flex-wrap: wrap;
            gap: 8px;
        }

        .announcement-content {
            color: #606266;
            font-size: 14px;
            max-height: 400px;
            overflow-y: auto;
            line-height: 1.8;
            white-space: normal;
            word-wrap: break-word;
            word-break: break-all;
            overflow-wrap: break-word;
        }

        .announcement-meta {
            margin-top: 8px;
            color: #909399;
            font-size: 12px;
        }

        /* 赞助按钮样式 */
        .sponsor-btn {
            background: linear-gradient(135deg, #409EFF, #ffffff);
            border: none;
            border-radius: 20px;
            padding: 8px 20px;
            font-weight: 600;
            color: #409EFF;
            box-shadow: 0 4px 15px rgba(64, 158, 255, 0.3);
            transition: all 0.3s ease;
        }

        .sponsor-btn:hover {
            background: linear-gradient(135deg, #66b1ff, #f0f9ff);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(64, 158, 255, 0.4);
            color: #409EFF;
        }

        .sponsor-btn:active {
            transform: translateY(0);
        }

        /* 公告铃铛按钮样式 */
        .bell-button {
            position: relative;
            padding: 8px 12px !important;
            border-radius: 50% !important;
            font-size: 18px !important;
            transition: all 0.3s ease !important;
            border: none !important;
        }

        .bell-button:hover {
            background-color: rgba(64, 158, 255, 0.1) !important;
            transform: scale(1.1);
        }

        .has-unread {
            position: relative;
            animation: bell-shake 2s infinite;
            color: #409eff !important;
        }

        .has-unread::after {
            content: '';
            position: absolute;
            top: 2px;
            right: 2px;
            width: 8px;
            height: 8px;
            background: #f56c6c;
            border-radius: 50%;
            border: 2px solid #fff;
            z-index: 1;
        }

        @keyframes bell-shake {
            0%, 50%, 100% { transform: rotate(0deg); }
            10%, 30% { transform: rotate(-10deg); }
            20%, 40% { transform: rotate(10deg); }
        }

        /* 赞助弹窗样式 */
        .sponsor-dialog .el-dialog__header {
            background: linear-gradient(135deg, #f0f9ff 0%, #ffffff 100%);
            color: #1890ff;
            border-radius: 0;
            padding: 20px 24px;
        }

        .sponsor-dialog .el-dialog__title {
            color: #1890ff;
            font-size: 18px;
            font-weight: 600;
        }

        .sponsor-dialog .el-dialog__headerbtn .el-dialog__close {
            color: #1890ff;
            font-size: 18px;
        }

        .sponsor-dialog .el-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            width: 100vw;
            height: 100vh;
            z-index: 2000;
        }

        .sponsor-dialog .el-dialog {
            border-radius: 0;
            margin: 0;
            width: 500px;
            max-height: 80vh;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        .sponsor-dialog .el-dialog__body {
            padding: 24px;
        }

        /* 赞助排行榜样式 */
        .sponsor-list {
            margin-top: 16px;
        }

        .sponsor-item {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            margin-bottom: 8px;
            background: #fff;
            border-radius: 8px;
            border: 1px solid #f0f0f0;
            transition: all 0.3s ease;
        }

        .sponsor-item:hover {
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transform: translateY(-1px);
        }

        .sponsor-item.rank-1 {
            background: linear-gradient(135deg, #fff9e6 0%, #fff3d3 100%);
            border-color: #ffd700;
        }

        .sponsor-item.rank-2 {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-color: #c0c0c0;
        }

        .sponsor-item.rank-3 {
            background: linear-gradient(135deg, #fff5f0 0%, #ffe8d6 100%);
            border-color: #cd7f32;
        }

        .sponsor-rank {
            width: 40px;
            text-align: center;
            margin-right: 12px;
        }

        .rank-icon {
            font-size: 20px;
        }

        .rank-number {
            font-size: 16px;
            font-weight: 600;
            color: #666;
        }

        .sponsor-avatar {
            width: 40px;
            height: 40px;
            margin-right: 12px;
        }

        .sponsor-avatar img {
            width: 100%;
            height: 100%;
            border-radius: 50%;
            object-fit: cover;
            border: 2px solid #f0f0f0;
        }

        .sponsor-info {
            flex: 1;
        }

        .sponsor-name {
            font-size: 14px;
            font-weight: 600;
            color: #303133;
            margin-bottom: 4px;
        }

        .sponsor-amount {
            font-size: 16px;
            font-weight: 700;
            color: #e6a23c;
        }

        .sponsor-meta {
            text-align: right;
        }

        .sponsor-count {
            font-size: 12px;
            color: #909399;
        }

        .empty-state {
            text-align: center;
            padding: 40px 20px;
        }

        /* 分页样式 */
        .sponsor-pagination {
            margin-top: 20px;
            text-align: center;
        }

        /* 赞助弹窗样式 */
        .sponsor-dialog-content {
            padding: 8px 0;
        }

        .amount-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 12px;
        }

        .amount-btn {
            min-width: 80px;
        }

        .qr-code-container {
            display: inline-block;
            padding: 16px;
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        /* 移动端适配 */
        @media (max-width: 768px) {
            .sponsor-item {
                padding: 10px 12px;
            }

            .sponsor-avatar {
                width: 32px;
                height: 32px;
                margin-right: 8px;
            }

            .sponsor-name {
                font-size: 13px;
            }

            .sponsor-amount {
                font-size: 14px;
            }

            .amount-buttons {
                justify-content: center;
            }

            .amount-btn {
                min-width: 70px;
                font-size: 12px;
            }
        }

        /* 统计卡片样式 */
        .stat-card {
            border: none;
            border-radius: 12px;
            transition: all 0.3s ease;
            margin-bottom: 16px;
        }

        .stat-card:hover {
            transform: translateY(-2px);
        }

        .stat-content {
            display: flex;
            align-items: center;
            padding: 8px 0;
        }

        .stat-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 16px;
            color: white;
        }

        .stat-icon.emoji-icon {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%) !important;
            border: 2px solid #e2e8f0;
            font-size: 24px;
            color: initial;
        }

        .stat-info {
            flex: 1;
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #303133;
            line-height: 1;
            margin-bottom: 4px;
        }

        .stat-label {
            font-size: 14px;
            color: #909399;
            line-height: 1;
        }

        /* 不同卡片的主题色 - 已替换为emoji图标 */
        /*
        .online-card .stat-icon {
            background: linear-gradient(135deg, #67C23A, #85CE61);
        }

        .total-card .stat-icon {
            background: linear-gradient(135deg, #409EFF, #79BBFF);
        }

        .today-card .stat-icon {
            background: linear-gradient(135deg, #E6A23C, #EEBE77);
        }

        .yesterday-card .stat-icon {
            background: linear-gradient(135deg, #F56C6C, #F89898);
        }
        */

        /* 移动端统计卡片适配 */
        @media (max-width: 768px) {
            .stat-number {
                font-size: 20px;
            }

            .stat-label {
                font-size: 12px;
            }

            .stat-icon {
                width: 40px;
                height: 40px;
                margin-right: 12px;
            }
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="layout-container">
            <!-- 侧边栏 -->
            <div class="sidebar" :class="{ 
                collapsed: isCollapsed, 
                expanded: !isCollapsed,
                'mobile-open': mobileMenuOpen 
            }">
                <button class="sidebar-toggle" @click="toggleSidebar">
                    {{ isCollapsed ? '→' : '←' }}
                </button>
                
                <!-- 用户信息 -->
                <div class="user-info" v-if="currentUser">
                    <div class="user-avatar">
                        <img v-if="currentUser.linuxdo_avatar" :src="currentUser.linuxdo_avatar" :alt="currentUser.linuxdo_username">
                        <span v-else>{{ currentUser.email ? currentUser.email.charAt(0).toUpperCase() : '?' }}</span>
                    </div>
                    <div class="user-details" v-show="!isCollapsed">
                        <div class="user-name">
                            {{ currentUser.linuxdo_username || currentUser.email.split('@')[0] }}
                            <span v-if="currentUser.linuxdo_trust_level !== null" 
                                  :class="`trust-level trust-level-${currentUser.linuxdo_trust_level}`">
                                {{ getTrustLevelName(currentUser.linuxdo_trust_level) }}
                            </span>
                        </div>
                        <div class="user-email">{{ currentUser.email }}</div>
                    </div>
                </div>
                
                <!-- 导航菜单 -->
                <div class="menu-container">
                    <el-menu
                        :default-active="activeMenu"
                        :collapse="isCollapsed"
                        :unique-opened="true"
                        background-color="#fff"
                        text-color="#303133"
                        active-text-color="#409eff">
                    
                    <el-menu-item index="home" @click="navigateTo('/')">
                        <el-icon><i class="el-icon-house"></i></el-icon>
                        <span>首页</span>
                    </el-menu-item>
                    
                    <el-menu-item index="profile" @click="navigateTo('/profile')">
                        <el-icon><i class="el-icon-user"></i></el-icon>
                        <span>个人中心</span>
                    </el-menu-item>

                    <el-menu-item index="chat" @click="navigateTo('/chat')">
                        <el-icon><i class="el-icon-chat-dot-round"></i></el-icon>
                        <span>实时聊天</span>
                    </el-menu-item>

                    <el-sub-menu index="api">
                        <template #title>
                            <el-icon><i class="el-icon-document"></i></el-icon>
                            <span>API管理</span>
                        </template>
                        <el-menu-item index="api-docs" @click="navigateTo('/api-docs')">
                            <el-icon><i class="el-icon-reading"></i></el-icon>
                            <span>API文档</span>
                        </el-menu-item>
                        <el-menu-item index="external-test" @click="navigateTo('/external-test')">
                            <el-icon><i class="el-icon-connection"></i></el-icon>
                            <span>外部接口测试</span>
                        </el-menu-item>
                    </el-sub-menu>

                    <el-menu-item index="docs" @click="navigateTo('/docs')">
                        <el-icon><i class="el-icon-folder"></i></el-icon>
                        <span>文档中心</span>
                    </el-menu-item>

                    <!-- 管理后台菜单项（仅管理员可见） -->
                    <el-menu-item v-if="currentUser && currentUser.isAdmin" index="admin" @click="navigateTo('/admin')">
                        <el-icon><i class="el-icon-setting"></i></el-icon>
                        <span>管理后台</span>
                    </el-menu-item>

                    <el-menu-item index="logout" @click="handleLogout">
                        <el-icon><i class="el-icon-switch-button"></i></el-icon>
                        <span>退出登录</span>
                    </el-menu-item>
                    </el-menu>
                </div>

                <!-- 下载工具区域 - 侧边栏底部 -->
                <div v-if="downloadInfo.checked" class="sidebar-download-area">
                    <!-- 收起状态的圆形按钮 -->
                    <div v-if="downloadPanelCollapsed && downloadInfo.hasPermission && downloadInfo.files && downloadInfo.files.length > 0"
                         class="download-fab"
                         @click="downloadPanelCollapsed = false"
                         :title="getPermissionBadgeText()">
                        <span class="download-icon">📥</span>
                        <span v-show="!isCollapsed" class="download-text">开发工具</span>
                    </div>

                    <!-- 展开状态的下载列表 -->
                    <div v-else-if="downloadInfo.hasPermission && downloadInfo.files && downloadInfo.files.length > 0"
                         class="download-list">
                        <div class="download-header" @click="downloadPanelCollapsed = true">
                            <span class="download-title">
                                <span class="download-icon">📥</span>
                                <span v-show="!isCollapsed">开发工具</span>
                            </span>
                            <span v-show="!isCollapsed" class="permission-badge">
                                {{ getPermissionBadgeText() }}
                            </span>
                        </div>

                        <div v-show="!downloadPanelCollapsed && !isCollapsed" class="download-items">
                            <div v-for="file in downloadInfo.files.filter(f => f.exists)"
                                 :key="file.id"
                                 class="download-item-mini"
                                 @click="handleDownload(file)">
                                <span class="file-icon">{{ file.id === 'token-extractor' ? '🔧' : '🧩' }}</span>
                                <span class="file-name">{{ file.description }}</span>
                                <span class="download-btn-mini">⬇️</span>
                            </div>
                        </div>
                    </div>

                    <!-- 无权限提示 -->
                    <div v-else class="download-locked" @click="showPermissionDialog">
                        <span class="lock-icon">🔒</span>
                        <span v-show="!isCollapsed" class="lock-text">
                            {{ downloadInfo.permissionReason || '需要Token认证或赞助' }}
                        </span>
                    </div>
                </div>
            </div>
            
            <!-- 主内容区 -->
            <div class="main-content">
                <!-- 内容头部 -->
                <div class="content-header">
                    <div>
                        <el-button v-if="isMobile" @click="toggleMobileSidebar" icon="el-icon-menu" circle></el-button>
                        <span style="font-size: 18px; font-weight: 600; margin-left: 8px;">首页</span>
                    </div>
                    <div>
                        <el-button @click="showAnnouncementDialog" :class="{ 'has-unread': hasUnreadAnnouncements, 'bell-button': true }" text>
                            🔔
                        </el-button>
                    </div>
                </div>
                
                <!-- 内容主体 -->
                <div class="content-body">
                    <!-- 欢迎卡片 -->
                    <div class="welcome-card">
                        <h1>🚀 AugmentAPI</h1>
                        <p>智能编程助手API管理平台</p>
                        <p v-if="currentUser">欢迎回来，{{ currentUser.linuxdo_username || currentUser.email.split('@')[0] }}！</p>
                    </div>

                    <!-- 系统公告弹窗 -->
                    <el-dialog
                        v-model="announcementDialogVisible"
                        width="500px"
                        :close-on-click-modal="false"
                        :close-on-press-escape="false"
                        :show-close="true"
                        class="announcement-dialog"
                        @close="handleAnnouncementClose">

                        <template #header>
                            <span style="font-size: 18px; font-weight: 600;">系统公告</span>
                        </template>

                        <div v-if="currentAnnouncement" class="announcement-dialog-content">
                            <div class="announcement-header">
                                <div class="announcement-title">
                                    <el-tag v-if="currentAnnouncement.is_pinned" type="warning" size="small" style="margin-right: 8px;">置顶</el-tag>
                                    <el-tag :type="getAnnouncementTypeColor(currentAnnouncement.type)" size="small" style="margin-right: 8px;">
                                        {{ getAnnouncementTypeText(currentAnnouncement.type) }}
                                    </el-tag>
                                    <span style="font-size: 16px; font-weight: 600;">{{ currentAnnouncement.title }}</span>
                                </div>
                                <div class="announcement-meta" style="margin-top: 8px; color: #909399; font-size: 12px;">
                                    发布时间：{{ formatDate(currentAnnouncement.created_at) }}
                                    <span v-if="currentAnnouncement.author_name" style="margin-left: 16px;">
                                        发布人：{{ currentAnnouncement.author_name }}
                                    </span>
                                </div>
                                <el-divider />
                                
                            </div>
                            <div class="announcement-content" style="line-height: 1.8; white-space: pre-wrap;">
                                    {{ currentAnnouncement.content }}
                                </div>
                        </div>

                        <template #footer>
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div style="color: #909399; font-size: 12px;">
                                    {{ currentAnnouncementIndex + 1 }} / {{ announcements.length }}
                                </div>
                                <div>
                                    <el-button
                                        v-if="currentAnnouncementIndex > 0"
                                        @click="showPreviousAnnouncement">
                                        上一条
                                    </el-button>
                                    <el-button
                                        v-if="currentAnnouncementIndex < announcements.length - 1"
                                        type="primary"
                                        @click="showNextAnnouncement">
                                        下一条
                                    </el-button>
                                    <el-button
                                        v-if="currentAnnouncementIndex === announcements.length - 1"
                                        type="primary"
                                        @click="markAnnouncementAsRead">
                                        知道了
                                    </el-button>
                                </div>
                            </div>
                        </template>
                    </el-dialog>

                    <!-- 赞助弹窗 -->
                    <el-dialog
                        v-model="sponsorDialogVisible"
                        title="💝 赞助支持"
                        width="500px"
                        :close-on-click-modal="false"
                        class="sponsor-dialog">

                        <div class="sponsor-dialog-content">
                            <!-- 感谢文字 -->
                            <div style="text-align: center; margin-bottom: 24px; padding: 20px; background: linear-gradient(135deg, #e6f4ff 0%, #ffffff 100%); border-radius: 12px; color: #1890ff;">
                                <h3 style="margin: 0 0 8px 0; font-size: 20px; color: #1890ff;">感谢您的支持！</h3>
                                <p style="margin: 0; font-size: 14px; color: #1890ff;">您的赞助是我持续更新和维护的动力</p>
                            </div>

                            <!-- 微信二维码 -->
                            <div style="text-align: center; margin-bottom: 24px;">
                                <div style="margin-bottom: 16px;">
                                    <div style="display: inline-block; padding: 8px 16px; background: linear-gradient(135deg, #e6f4ff 0%, #ffffff 100%); border-radius: 20px; color: #1890ff; font-size: 14px; font-weight: 600;">
                                        <el-icon style="margin-right: 6px;"><ChatDotRound /></el-icon>
                                        微信扫码赞助
                                    </div>
                                </div>
                                <div style="display: inline-block; padding: 16px; background: white; border-radius: 12px; box-shadow: 0 4px 20px rgba(0,0,0,0.1);">
                                    <img src="/img/wx.png" alt="微信赞助二维码"
                                         style="width: 200px; height: 200px; border-radius: 8px; display: block;">
                                </div>
                                <div style="margin-top: 12px; font-size: 13px; color: #909399;">
                                    <el-icon style="margin-right: 4px;"><InfoFilled /></el-icon>
                                    使用微信扫描上方二维码进行赞助
                                </div>
                            </div>

                            <!-- 赞助信息表单 -->
                            <div class="sponsor-form-section">
                                <!-- 赞助金额 -->
                                <div style="margin-bottom: 16px;">
                                    <div style="margin-bottom: 8px; font-weight: 600;">赞助金额：</div>
                                    <el-input
                                        v-model="sponsorAmount"
                                        placeholder="请输入赞助金额"
                                        type="number"
                                        :min="0.01"
                                        :max="9999"
                                        size="large">
                                        <template #prepend>¥</template>
                                    </el-input>
                                </div>

                                <!-- 赞助人 -->
                                <div style="margin-bottom: 16px;">
                                    <div style="margin-bottom: 8px; font-weight: 600;">赞助人：</div>
                                    <el-input
                                        :value="currentUser.linuxdo_username"
                                        readonly
                                        size="large"
                                        style="background-color: #f5f7fa;">
                                        <template #prepend>
                                            <img :src="currentUser.linuxdo_avatar" :alt="currentUser.linuxdo_username"
                                                 style="width: 20px; height: 20px; border-radius: 50%;">
                                        </template>
                                    </el-input>
                                </div>

                                <div style="font-size: 12px; color: #909399; text-align: center;">
                                    您的赞助将激励我持续更新功能，感谢您的支持！
                                </div>
                            </div>
                        </div>

                        <template #footer>
                            <div style="text-align: center;">
                                <el-button @click="sponsorDialogVisible = false">取消</el-button>
                                <el-button
                                    type="primary"
                                    @click="confirmSponsor"
                                    :disabled="!sponsorAmount || parseFloat(sponsorAmount) <= 0"
                                    :loading="sponsorSubmitting">
                                    {{ sponsorSubmitting ? '提交中...' : '确认赞助' }}
                                </el-button>
                            </div>
                        </template>
                    </el-dialog>

                    <!-- 网站统计卡片 -->
                    <div class="auth-card">
                        <div class="auth-title">
                            <el-icon><DataAnalysis /></el-icon>
                            网站统计
                        </div>

                        <!-- 统计数据网格 -->
                        <el-row :gutter="16" style="margin-bottom: 20px;">
                            <el-col :xs="12" :sm="6" :md="6" :lg="6">
                                <el-card shadow="hover" class="stat-card online-card">
                                    <div class="stat-content">
                                        <div class="stat-icon emoji-icon">
                                            🐧
                                        </div>
                                        <div class="stat-info">
                                            <div class="stat-number">{{ siteStats.onlineUsers }}</div>
                                            <div class="stat-label">在线人数</div>
                                        </div>
                                    </div>
                                </el-card>
                            </el-col>
                            <el-col :xs="12" :sm="6" :md="6" :lg="6">
                                <el-card shadow="hover" class="stat-card total-card">
                                    <div class="stat-content">
                                        <div class="stat-icon emoji-icon">
                                            🖐
                                        </div>
                                        <div class="stat-info">
                                            <div class="stat-number">{{ siteStats.users.total }}</div>
                                            <div class="stat-label">总用户</div>
                                        </div>
                                    </div>
                                </el-card>
                            </el-col>
                            <el-col :xs="12" :sm="6" :md="6" :lg="6">
                                <el-card shadow="hover" class="stat-card today-card">
                                    <div class="stat-content">
                                        <div class="stat-icon emoji-icon">
                                            ✨
                                        </div>
                                        <div class="stat-info">
                                            <div class="stat-number">{{ siteStats.users.todayNew }}</div>
                                            <div class="stat-label">今日新增</div>
                                        </div>
                                    </div>
                                </el-card>
                            </el-col>
                            <el-col :xs="12" :sm="6" :md="6" :lg="6">
                                <el-card shadow="hover" class="stat-card yesterday-card">
                                    <div class="stat-content">
                                        <div class="stat-icon emoji-icon">
                                            📈
                                        </div>
                                        <div class="stat-info">
                                            <div class="stat-number">{{ siteStats.users.yesterdayNew }}</div>
                                            <div class="stat-label">昨日新增</div>
                                        </div>
                                    </div>
                                </el-card>
                            </el-col>
                        </el-row>

                        <div style="text-align: center;">
                            <el-button type="primary" @click="refreshStatistics" :loading="statsLoading" :icon="Refresh">
                                {{ statsLoading ? '刷新中...' : '刷新统计' }}
                            </el-button>
                            <el-text type="info" size="small" style="margin-left: 12px;">
                                数据每30秒自动更新
                            </el-text>
                        </div>
                    </div>

                    <!-- 赞助排行榜 -->
                    <div class="auth-card" style="margin-top: 20px;">
                        <div class="auth-title" style="position: relative;">
                            <div style="display: flex; align-items: center;">
                                <el-icon><Trophy /></el-icon>
                                赞助排行榜
                                <span style="font-size: 12px; color: #909399; margin-left: 8px;">感谢以下用户的赞助支持</span>
                            </div>
                            <el-button type="primary" @click="showSponsorDialog" class="sponsor-btn" style="position: absolute; top: 0; right: 0;">
                                <el-icon style="margin-right: 6px;"><Gift /></el-icon>
                                赞助支持
                            </el-button>
                        </div>

                        <div v-if="sponsorRanking.length > 0" class="sponsor-list">
                            <div
                                v-for="sponsor in sponsorRanking"
                                :key="sponsor.user_uuid"
                                class="sponsor-item"
                                :class="`rank-${sponsor.rank}`">
                                <div class="sponsor-rank">
                                    <span v-if="sponsor.rank === 1" class="rank-icon gold">🥇</span>
                                    <span v-else-if="sponsor.rank === 2" class="rank-icon silver">🥈</span>
                                    <span v-else-if="sponsor.rank === 3" class="rank-icon bronze">🥉</span>
                                    <span v-else class="rank-number">{{ sponsor.rank }}</span>
                                </div>
                                <div class="sponsor-avatar">
                                    <img :src="sponsor.avatar" :alt="sponsor.username" @error="handleImageError">
                                </div>
                                <div class="sponsor-info">
                                    <div class="sponsor-name">{{ sponsor.username }}</div>
                                    <div class="sponsor-amount">¥{{ sponsor.total_amount }}</div>
                                </div>
                                <div class="sponsor-meta">
                                    <div class="sponsor-count">{{ sponsor.sponsor_count }}次</div>
                                </div>
                            </div>
                        </div>

                        <div v-else class="empty-state">
                            <el-icon style="font-size: 48px; color: #ddd;"><Gift /></el-icon>
                            <p style="color: #909399; margin-top: 16px;">暂无赞助记录</p>
                        </div>



                        <!-- 分页组件 -->
                        <div v-if="sponsorPagination.total > sponsorPagination.pageSize" class="sponsor-pagination">
                            <el-pagination
                                v-model:current-page="sponsorPagination.currentPage"
                                :page-size="sponsorPagination.pageSize"
                                :total="sponsorPagination.total"
                                layout="prev, pager, next"
                                @current-change="handleSponsorPageChange"
                                small
                                background>
                            </el-pagination>
                        </div>

                    </div>

                    <!-- Augment OAuth 授权卡片 -->
                    <div class="auth-card">
                        <div class="auth-title">
                            🔐 Augment OAuth 授权
                        </div>

                        <!-- 步骤1：获取授权链接 -->
                        <div class="auth-step">
                            <h3><span class="step-number">1</span>获取授权链接</h3>
                            <p>点击下面的按钮获取 Augment 授权链接：</p>
                            <el-button
                                type="primary"
                                @click="getAuthUrl"
                                :loading="authLoading"
                                :disabled="authLoading">
                                {{ authLoading ? '获取中...' : (authUrlGenerated ? '授权链接已生成' : '获取授权链接') }}
                            </el-button>

                            <!-- 授权链接结果 -->
                            <div v-if="authUrl" class="auth-result">
                                <el-alert
                                    title="✅ 授权链接生成成功"
                                    type="success"
                                    :closable="false"
                                    show-icon>
                                </el-alert>
                                <div class="auth-url-display">
                                    <el-input
                                        v-model="authUrl"
                                        readonly
                                        type="textarea"
                                        :rows="3">
                                    </el-input>
                                </div>
                                <div class="auth-actions">
                                    <el-button type="primary" @click="openAuthUrl">
                                        🚀 在新窗口中打开
                                    </el-button>
                                    <el-button type="success" @click="copyAuthUrl">
                                        📋 复制链接
                                    </el-button>
                                </div>
                            </div>

                            <!-- 错误信息 -->
                            <div v-if="authError" class="auth-error">
                                <el-alert
                                    :title="'❌ 错误: ' + authError"
                                    type="error"
                                    :closable="false"
                                    show-icon>
                                </el-alert>
                            </div>
                        </div>

                        <!-- 步骤2：完成授权 -->
                        <div class="auth-step">
                            <h3><span class="step-number">2</span>完成授权</h3>
                            <p>在新窗口中完成授权后，将返回的授权响应粘贴到下面的文本框中：</p>
                            <el-input
                                v-model="authResponse"
                                type="textarea"
                                :rows="4"
                                placeholder='粘贴授权响应，格式如：{"code":"xxx","state":"xxx","tenant_url":"xxx"}'>
                            </el-input>
                            <div style="margin-top: 12px;">
                                <el-button
                                    type="primary"
                                    @click="submitAuthCode"
                                    :loading="submitLoading"
                                    :disabled="submitLoading || !authResponse.trim()">
                                    {{ submitLoading ? '提交中...' : (tokenGenerated ? '授权成功' : '提交授权码') }}
                                </el-button>
                            </div>

                            <!-- Token结果 -->
                            <div v-if="accessToken" class="token-result">
                                <el-alert
                                    title="🎉 授权成功！"
                                    type="success"
                                    :closable="false"
                                    show-icon>
                                </el-alert>
                                <div class="token-display">
                                    <label><strong>访问令牌：</strong></label>
                                    <el-input
                                        v-model="accessToken"
                                        readonly
                                        type="textarea"
                                        :rows="3">
                                    </el-input>
                                </div>
                                <div class="token-display" v-if="tenantUrl">
                                    <label><strong>租户URL：</strong></label>
                                    <el-input
                                        v-model="tenantUrl"
                                        readonly>
                                    </el-input>
                                </div>
                            </div>

                            <!-- 提交错误信息 -->
                            <div v-if="submitError" class="auth-error">
                                <el-alert
                                    :title="'❌ 错误: ' + submitError"
                                    type="error"
                                    :closable="false"
                                    show-icon>
                                </el-alert>
                            </div>
                        </div>
                    </div>

                    <!-- 功能卡片 -->
                    <div class="feature-grid">
                        <div class="feature-card" @click="navigateTo('/profile')">
                            <div class="feature-icon">👤</div>
                            <div class="feature-title">个人中心</div>
                            <div class="feature-desc">管理您的账户信息和认证Token</div>
                        </div>

                        <div class="feature-card" @click="navigateTo('/chat')">
                            <div class="feature-icon">💬</div>
                            <div class="feature-title">实时聊天</div>
                            <div class="feature-desc">与其他用户实时交流，申请Token</div>
                        </div>

                        <div class="feature-card" @click="navigateTo('/api-docs')">
                            <div class="feature-icon">📚</div>
                            <div class="feature-title">API文档</div>
                            <div class="feature-desc">查看完整的API接口文档和使用说明</div>
                        </div>

                        <div class="feature-card" @click="navigateTo('/external-test')">
                            <div class="feature-icon">🧪</div>
                            <div class="feature-title">接口测试</div>
                            <div class="feature-desc">测试外部API接口的功能和性能</div>
                        </div>

                        <div class="feature-card" @click="navigateTo('/docs')">
                            <div class="feature-icon">📚</div>
                            <div class="feature-title">文档中心</div>
                            <div class="feature-desc">查看使用指南、API文档和常见问题</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 移动端遮罩 -->
        <div v-if="isMobile && mobileMenuOpen"
             style="position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.5); z-index: 999;"
             @click="closeMobileSidebar"></div>

        <!-- 移动端下载面板 -->
        <div v-if="isMobile && downloadInfo.checked && downloadInfo.hasPermission && downloadInfo.files && downloadInfo.files.length > 0"
             class="mobile-download-panel">
            <div class="mobile-download-fab" @click="showMobileDownloadDialog">
                📥
            </div>
        </div>

    </div>

    <!-- CDN资源已由服务端自动注入到head部分 -->

    <script>
        const { createApp, ref, reactive, onMounted, computed } = Vue;
        const { User, UserFilled, Plus, Star, DataAnalysis, Refresh, Bell, Gift, Trophy, ChatDotRound, InfoFilled } = ElementPlusIconsVue;

        createApp({
            setup() {
                // 响应式数据
                const isCollapsed = ref(false);
                const mobileMenuOpen = ref(false);
                const currentUser = ref(null);
                const activeMenu = ref('home');

                // 授权相关数据
                const authLoading = ref(false);
                const authUrl = ref('');
                const authUrlGenerated = ref(false);
                const authError = ref('');
                const authResponse = ref('');
                const submitLoading = ref(false);
                const accessToken = ref('');
                const tenantUrl = ref('');
                const tokenGenerated = ref(false);
                const submitError = ref('');

                // 网站统计数据
                const statsLoading = ref(false);
                const siteStats = reactive({
                    onlineUsers: 0,
                    users: {
                        total: 0,
                        todayNew: 0,
                        yesterdayNew: 0
                    }
                });

                // 公告数据
                const announcements = ref([]);
                const announcementDialogVisible = ref(false);
                const currentAnnouncement = ref(null);
                const currentAnnouncementIndex = ref(0);
                const hasUnreadAnnouncements = ref(false);

                // 赞助数据
                const sponsorRanking = ref([]);
                const sponsorStats = reactive({
                    total_sponsors: 0,
                    total_amount: 0,
                    avg_amount: 0
                });

                // 分页数据
                const sponsorPagination = reactive({
                    currentPage: 1,
                    pageSize: 10,
                    total: 0
                });

                // 赞助弹窗数据
                const sponsorDialogVisible = ref(false);
                const sponsorAmount = ref('');
                const sponsorSubmitting = ref(false);

                // 下载相关数据
                const downloadInfo = ref({
                    hasPermission: false,
                    permissionReason: '',
                    userInfo: {},
                    files: [],
                    checked: false
                });
                const downloadPanelCollapsed = ref(false);

                // Socket.IO连接
                let socket = null;

                // 计算属性
                const isMobile = computed(() => {
                    return window.innerWidth <= 768;
                });

                // 方法
                const toggleSidebar = () => {
                    isCollapsed.value = !isCollapsed.value;
                };

                const toggleMobileSidebar = () => {
                    mobileMenuOpen.value = !mobileMenuOpen.value;
                };

                const closeMobileSidebar = () => {
                    mobileMenuOpen.value = false;
                };

                const navigateTo = (path) => {
                    window.location.href = path;
                };

                const refreshPage = () => {
                    window.location.reload();
                };

                const showAnnouncementDialog = () => {
                    if (announcements.value.length > 0) {
                        // 优先显示第一条未读公告，如果没有未读公告则显示第一条公告
                        const unreadAnnouncements = announcements.value.filter(announcement =>
                            !isAnnouncementRead(announcement.uuid)
                        );

                        if (unreadAnnouncements.length > 0) {
                            // 找到第一条未读公告在所有公告中的索引
                            const firstUnreadIndex = announcements.value.findIndex(announcement =>
                                announcement.uuid === unreadAnnouncements[0].uuid
                            );
                            currentAnnouncementIndex.value = firstUnreadIndex;
                            currentAnnouncement.value = announcements.value[firstUnreadIndex];
                        } else {
                            // 没有未读公告，显示第一条公告
                            currentAnnouncementIndex.value = 0;
                            currentAnnouncement.value = announcements.value[0];
                        }

                        announcementDialogVisible.value = true;
                        hasUnreadAnnouncements.value = false;
                    } else {
                        ElementPlus.ElMessage.info('暂无公告');
                    }
                };

                const handleLogout = async () => {
                    try {
                        const response = await fetch('/api/logout', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            }
                        });

                        if (response.ok) {
                            localStorage.removeItem('auth_token');
                            window.location.href = '/login';
                        } else {
                            ElementPlus.ElMessage.error('登出失败，请重试');
                        }
                    } catch (error) {
                        console.error('登出错误:', error);
                        ElementPlus.ElMessage.error('网络错误，请重试');
                    }
                };

                const getTrustLevelName = (level) => {
                    const levelNames = {
                        0: '新用户',
                        1: '基础',
                        2: '成员',
                        3: '常规',
                        4: '领导'
                    };
                    return levelNames[level] || '未知';
                };

                // 使用统一的认证工具
                const { getAuthToken, authenticatedFetch, clearAuthToken } = window.AuthUtils;

                const checkLoginStatus = async () => {
                    try {
                        const token = getAuthToken();
                        if (!token) {
                            window.location.href = '/login';
                            return false;
                        }

                        const response = await authenticatedFetch('/api/user');

                        if (response.ok) {
                            const result = await response.json();
                            currentUser.value = result.user;
                            return true;
                        }
                    } catch (error) {
                        if (error.message === '认证失败') {
                            return false;
                        }
                        return false;
                    }
                };

                // 授权相关方法
                const getAuthUrl = async () => {
                    if (authLoading.value) return;

                    authLoading.value = true;
                    authError.value = '';

                    try {
                        const response = await fetch('/auth');
                        const data = await response.json();

                        if (data.authorize_url) {
                            authUrl.value = data.authorize_url;
                            authUrlGenerated.value = true;
                            ElementPlus.ElMessage.success('授权链接生成成功');
                        } else {
                            throw new Error('获取授权链接失败');
                        }
                    } catch (error) {
                        authError.value = error.message;
                        ElementPlus.ElMessage.error('获取授权链接失败: ' + error.message);
                    } finally {
                        authLoading.value = false;
                    }
                };

                const openAuthUrl = () => {
                    if (authUrl.value) {
                        window.open(authUrl.value, '_blank');
                    }
                };

                const copyAuthUrl = async () => {
                    try {
                        await navigator.clipboard.writeText(authUrl.value);
                        ElementPlus.ElMessage.success('授权链接已复制到剪贴板');
                    } catch (error) {
                        console.error('复制失败:', error);
                        ElementPlus.ElMessage.error('复制失败');
                    }
                };

                const submitAuthCode = async () => {
                    if (submitLoading.value) return;

                    const response = authResponse.value.trim();
                    if (!response) {
                        ElementPlus.ElMessage.warning('请输入授权响应');
                        return;
                    }

                    submitLoading.value = true;
                    submitError.value = '';

                    try {
                        // 验证JSON格式
                        const authData = JSON.parse(response);

                        if (!authData.code || !authData.state || !authData.tenant_url) {
                            throw new Error('授权响应格式不正确，缺少必要字段');
                        }

                        const apiResponse = await fetch('/callback', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: response
                        });

                        const result = await apiResponse.json();

                        if (result.status === 'success') {
                            accessToken.value = result.token;
                            tenantUrl.value = authData.tenant_url || '未知';
                            tokenGenerated.value = true;
                            ElementPlus.ElMessage.success('授权成功！');
                        } else {
                            throw new Error(result.error || '授权失败');
                        }
                    } catch (error) {
                        submitError.value = error.message;
                        ElementPlus.ElMessage.error('授权失败: ' + error.message);
                    } finally {
                        submitLoading.value = false;
                    }
                };

                // 初始化Socket.IO连接
                const initSocket = () => {
                    if (typeof io === 'undefined') {
                        console.warn('Socket.IO未加载');
                        return;
                    }

                    socket = io();

                    socket.on('connect', () => {
                        console.log('Socket.IO连接成功');

                        // 如果用户已登录，发送认证信息（可选）
                        if (currentUser.value) {
                            socket.emit('authenticate', {
                                userId: currentUser.value.uuid,
                                userEmail: currentUser.value.email
                            });
                        }
                        // 注意：即使未登录也会被统计为在线用户
                    });

                    socket.on('disconnect', () => {
                        console.log('Socket.IO连接断开');
                    });

                    // 监听统计数据更新
                    socket.on('statistics', (data) => {
                        console.log('收到统计数据更新:', data);
                        siteStats.onlineUsers = data.onlineUsers || 0;
                        siteStats.users.total = data.users.total || 0;
                        siteStats.users.todayNew = data.users.todayNew || 0;
                        siteStats.users.yesterdayNew = data.users.yesterdayNew || 0;
                    });

                    // 定期发送心跳（保持连接活跃）
                    setInterval(() => {
                        if (socket && socket.connected) {
                            socket.emit('heartbeat');
                        }
                    }, 30000); // 每30秒发送一次心跳
                };

                // 加载统计数据（备用方案）
                const loadStatistics = async () => {
                    if (statsLoading.value) return;

                    statsLoading.value = true;

                    try {
                        const response = await fetch('/api/statistics');

                        if (response.ok) {
                            const result = await response.json();

                            if (result.success) {
                                // 更新统计数据（除了在线人数，由Socket.IO实时更新）
                                siteStats.users.total = result.data.users.total || 0;
                                siteStats.users.todayNew = result.data.users.todayNew || 0;
                                siteStats.users.yesterdayNew = result.data.users.yesterdayNew || 0;
                            }
                        }
                    } catch (error) {
                        console.error('加载统计数据失败:', error);
                    } finally {
                        statsLoading.value = false;
                    }
                };

                // 刷新统计数据
                const refreshStatistics = async () => {
                    await loadStatistics();
                    ElementPlus.ElMessage.success('统计数据已刷新');
                };

                // 加载赞助排行榜
                const loadSponsorRanking = async (page = 1) => {
                    try {
                        const offset = (page - 1) * sponsorPagination.pageSize;
                        const response = await fetch(`/api/sponsors/ranking?limit=${sponsorPagination.pageSize}&offset=${offset}`);
                        if (response.ok) {
                            const result = await response.json();
                            if (result.success) {
                                sponsorRanking.value = result.data.map((sponsor, index) => ({
                                    ...sponsor,
                                    rank: offset + index + 1
                                }));
                                sponsorPagination.total = parseInt(result.total) || 0;
                                sponsorPagination.currentPage = page;
                            }
                        }
                    } catch (error) {
                        console.error('加载赞助排行榜失败:', error);
                    }
                };

                // 加载赞助统计
                const loadSponsorStats = async () => {
                    try {
                        const response = await fetch('/api/sponsors/stats');
                        if (response.ok) {
                            const result = await response.json();
                            if (result.success) {
                                Object.assign(sponsorStats, result.data);
                            }
                        }
                    } catch (error) {
                        console.error('加载赞助统计失败:', error);
                    }
                };

                // 显示赞助弹窗
                const showSponsorDialog = () => {
                    sponsorDialogVisible.value = true;
                    sponsorAmount.value = '';
                };

                // 处理分页变化
                const handleSponsorPageChange = (page) => {
                    loadSponsorRanking(page);
                };



                // 确认赞助（新的简化版本）
                const confirmSponsor = async () => {
                    try {
                        sponsorSubmitting.value = true;

                        const amount = parseFloat(sponsorAmount.value);
                        if (!amount || amount <= 0) {
                            ElementPlus.ElMessage.warning('请输入有效的赞助金额');
                            return;
                        }

                        // 调用后端接口记录赞助
                        const response = await authenticatedFetch('/api/sponsors', {
                            method: 'POST',
                            body: JSON.stringify({
                                user_uuid: currentUser.value.uuid,
                                amount: amount
                            })
                        });

                        if (response.ok) {
                            const result = await response.json();
                            if (result.success) {
                                ElementPlus.ElMessage.success('赞助记录已提交，感谢您的支持！');
                                sponsorDialogVisible.value = false;
                                sponsorAmount.value = '';
                                // 刷新赞助排行榜
                                await loadSponsorRanking();
                                await loadSponsorStats();
                            } else {
                                ElementPlus.ElMessage.error(result.message || '记录赞助失败');
                            }
                        } else {
                            ElementPlus.ElMessage.error('记录赞助失败');
                        }
                    } catch (error) {
                        console.error('确认赞助失败:', error);
                        ElementPlus.ElMessage.error('操作失败，请重试');
                    } finally {
                        sponsorSubmitting.value = false;
                    }
                };

                // 检查下载权限
                const checkDownloadPermission = async () => {
                    try {
                        const response = await authenticatedFetch('/api/download/info');
                        if (response.ok) {
                            const result = await response.json();
                            downloadInfo.value = {
                                hasPermission: result.hasPermission,
                                permissionReason: result.permissionReason,
                                userInfo: result.userInfo || {},
                                files: result.files || [],
                                checked: true
                            };
                        }
                    } catch (error) {
                        downloadInfo.value.checked = true;
                    }
                };

                // 处理下载
                const handleDownload = async (file) => {
                    try {
                        if (!downloadInfo.value.hasPermission) {
                            ElementPlus.ElMessage.warning('您没有下载权限');
                            return;
                        }

                        if (!file.downloadUrl) {
                            ElementPlus.ElMessage.warning('文件不可下载');
                            return;
                        }

                        // 创建下载链接
                        const link = document.createElement('a');
                        link.href = file.downloadUrl;
                        link.download = file.filename;
                        document.body.appendChild(link);
                        link.click();
                        document.body.removeChild(link);

                        ElementPlus.ElMessage.success(`${file.description}下载开始`);
                    } catch (error) {
                        ElementPlus.ElMessage.error('下载失败，请重试');
                    }
                };

                // 移动端下载对话框
                const showMobileDownloadDialog = () => {
                    const files = downloadInfo.value.files.filter(f => f.exists);
                    if (files.length === 1) {
                        // 只有一个文件，直接下载
                        handleDownload(files[0]);
                    } else {
                        // 多个文件，显示选择对话框
                        ElementPlus.ElMessageBox.confirm(
                            '请选择要下载的文件：',
                            '下载工具',
                            {
                                confirmButtonText: '确定',
                                cancelButtonText: '取消',
                                type: 'info',
                                customClass: 'mobile-download-dialog'
                            }
                        ).then(() => {
                            // 这里可以扩展为文件选择对话框
                            handleDownload(files[0]);
                        }).catch(() => {
                            // 用户取消
                        });
                    }
                };

                // 获取权限徽章文本
                const getPermissionBadgeText = () => {
                    if (!downloadInfo.value.hasPermission) return '';

                    // 检查是否有Token认证
                    if (currentUser.value?.auth_token) {
                        return '🔑 Token认证';
                    }

                    // 检查是否是赞助用户（这里需要根据实际的赞助用户判断逻辑）
                    // 假设通过某个字段或API来判断
                    if (downloadInfo.value.permissionReason?.includes('赞助')) {
                        return '💎 赞助用户';
                    }

                    // 默认显示
                    return '✅ 已授权';
                };

                // 显示权限说明对话框
                const showPermissionDialog = () => {
                    ElementPlus.ElMessageBox.alert(
                        `<div style="text-align: left; line-height: 1.6;">
                            <h4 style="margin: 0 0 12px 0; color: #409EFF;">🔓 获取下载权限</h4>
                            <p style="margin: 8px 0;"><strong>方式一：Token认证</strong></p>
                            <p style="margin: 4px 0 12px 20px; color: #666;">• 完成Augment OAuth授权获取>=3个 Token </p>

                            <p style="margin: 8px 0;"><strong>方式二：赞助支持</strong></p>
                            <p style="margin: 4px 0 12px 20px; color: #666;">• 通过微信赞助支持项目发展</p>

                            <div style="background: #f0f9ff; padding: 12px; border-radius: 8px; border-left: 4px solid #409EFF;">
                                <p style="margin: 0; font-size: 13px; color: #1890ff;">
                                    💡 <strong>提示：</strong>获取权限后可下载Token提取工具和VSCode插件
                                </p>
                            </div>
                        </div>`,
                        '下载权限说明',
                        {
                            confirmButtonText: '我知道了',
                            dangerouslyUseHTMLString: true,
                            customClass: 'permission-dialog'
                        }
                    );
                };

                // 公告阅读记录相关常量
                const ANNOUNCEMENT_READ_DURATION = 30 * 60 * 1000; // 30分钟（毫秒）
                const ANNOUNCEMENT_READ_KEY = 'announcement_reads';

                // 获取公告阅读记录
                const getAnnouncementReads = () => {
                    try {
                        const reads = localStorage.getItem(ANNOUNCEMENT_READ_KEY);
                        return reads ? JSON.parse(reads) : {};
                    } catch (error) {
                        console.error('获取公告阅读记录失败:', error);
                        return {};
                    }
                };

                // 保存公告阅读记录
                const saveAnnouncementRead = (announcementUuid) => {
                    try {
                        const reads = getAnnouncementReads();
                        reads[announcementUuid] = Date.now();
                        localStorage.setItem(ANNOUNCEMENT_READ_KEY, JSON.stringify(reads));
                    } catch (error) {
                        console.error('保存公告阅读记录失败:', error);
                    }
                };

                // 检查公告是否已读（在指定时间内）
                const isAnnouncementRead = (announcementUuid) => {
                    const reads = getAnnouncementReads();
                    const readTime = reads[announcementUuid];
                    if (!readTime) return false;

                    const now = Date.now();
                    return (now - readTime) < ANNOUNCEMENT_READ_DURATION;
                };

                // 清理过期的阅读记录
                const cleanExpiredReads = () => {
                    try {
                        const reads = getAnnouncementReads();
                        const now = Date.now();
                        const cleanedReads = {};

                        for (const [uuid, readTime] of Object.entries(reads)) {
                            if ((now - readTime) < ANNOUNCEMENT_READ_DURATION) {
                                cleanedReads[uuid] = readTime;
                            }
                        }

                        localStorage.setItem(ANNOUNCEMENT_READ_KEY, JSON.stringify(cleanedReads));
                    } catch (error) {
                        console.error('清理过期阅读记录失败:', error);
                    }
                };

                // 加载活跃公告
                const loadAnnouncements = async () => {
                    try {
                        const response = await fetch('/api/announcements/active');
                        if (response.ok) {
                            const data = await response.json();
                            announcements.value = data.data || [];

                            // 清理过期的阅读记录
                            cleanExpiredReads();

                            // 过滤出未读的公告
                            const unreadAnnouncements = announcements.value.filter(announcement =>
                                !isAnnouncementRead(announcement.uuid)
                            );

                            // 如果有未读公告，设置铃铛提醒
                            if (unreadAnnouncements.length > 0) {
                                hasUnreadAnnouncements.value = true;
                                // 不自动弹出，等用户点击铃铛
                                // showAnnouncement(0);
                            } else {
                                hasUnreadAnnouncements.value = false;
                            }
                        }
                    } catch (error) {
                        console.error('加载公告失败:', error);
                    }
                };

                // 显示指定索引的公告
                const showAnnouncement = (index) => {
                    if (index >= 0 && index < announcements.value.length) {
                        currentAnnouncementIndex.value = index;
                        currentAnnouncement.value = announcements.value[index];
                        announcementDialogVisible.value = true;
                    }
                };

                // 显示下一条公告
                const showNextAnnouncement = () => {
                    const nextIndex = currentAnnouncementIndex.value + 1;
                    if (nextIndex < announcements.value.length) {
                        showAnnouncement(nextIndex);
                    }
                };

                // 显示上一条公告
                const showPreviousAnnouncement = () => {
                    const prevIndex = currentAnnouncementIndex.value - 1;
                    if (prevIndex >= 0) {
                        showAnnouncement(prevIndex);
                    }
                };

                // 处理公告弹窗关闭
                const handleAnnouncementClose = () => {
                    // 记录当前公告为已读
                    if (currentAnnouncement.value) {
                        saveAnnouncementRead(currentAnnouncement.value.uuid);
                    }

                    announcementDialogVisible.value = false;
                    currentAnnouncement.value = null;
                    currentAnnouncementIndex.value = 0;
                };

                // 标记公告为已读并关闭弹窗
                const markAnnouncementAsRead = () => {
                    if (currentAnnouncement.value) {
                        saveAnnouncementRead(currentAnnouncement.value.uuid);
                        console.log(`公告 "${currentAnnouncement.value.title}" 已标记为已读，${ANNOUNCEMENT_READ_DURATION / (60 * 1000)}分钟内不会再显示`);
                    }
                    announcementDialogVisible.value = false;
                    currentAnnouncement.value = null;
                    currentAnnouncementIndex.value = 0;

                    // 检查是否还有未读公告
                    const unreadCount = announcements.value.filter(announcement =>
                        !isAnnouncementRead(announcement.uuid)
                    ).length;

                    if (unreadCount === 0) {
                        hasUnreadAnnouncements.value = false;
                    }
                };



                // 公告相关方法
                const getAnnouncementTypeColor = (type) => {
                    const colors = {
                        info: '',
                        warning: 'warning',
                        success: 'success',
                        error: 'danger'
                    };
                    return colors[type] || '';
                };

                const getAnnouncementTypeText = (type) => {
                    const texts = {
                        info: '信息',
                        warning: '警告',
                        success: '成功',
                        error: '错误'
                    };
                    return texts[type] || type;
                };

                const formatDate = (dateString) => {
                    return new Date(dateString).toLocaleDateString('zh-CN');
                };

                // 生命周期
                onMounted(async () => {
                    await checkLoginStatus();

                    // 检查下载权限
                    await checkDownloadPermission();

                    // 加载活跃公告
                    await loadAnnouncements();

                    // 初始化Socket.IO连接
                    initSocket();

                    // 加载初始统计数据
                    await loadStatistics();

                    // 加载赞助数据
                    await loadSponsorRanking();
                    await loadSponsorStats();

                    // 监听窗口大小变化
                    window.addEventListener('resize', () => {
                        if (!isMobile.value && mobileMenuOpen.value) {
                            mobileMenuOpen.value = false;
                        }
                    });
                });

                return {
                    isCollapsed,
                    mobileMenuOpen,
                    currentUser,
                    activeMenu,
                    isMobile,
                    toggleSidebar,
                    toggleMobileSidebar,
                    closeMobileSidebar,
                    navigateTo,
                    refreshPage,
                    handleLogout,
                    getTrustLevelName,
                    // 统计相关
                    siteStats,
                    statsLoading,
                    loadStatistics,
                    refreshStatistics,
                    // 公告相关
                    announcements,
                    announcementDialogVisible,
                    currentAnnouncement,
                    currentAnnouncementIndex,
                    hasUnreadAnnouncements,
                    loadAnnouncements,
                    showAnnouncement,
                    showAnnouncementDialog,
                    showNextAnnouncement,
                    showPreviousAnnouncement,
                    handleAnnouncementClose,
                    markAnnouncementAsRead,
                    getAnnouncementTypeColor,
                    getAnnouncementTypeText,
                    formatDate,
                    // 赞助相关
                    sponsorRanking,
                    sponsorStats,
                    sponsorPagination,
                    loadSponsorRanking,
                    loadSponsorStats,
                    handleSponsorPageChange,
                    // 赞助弹窗
                    sponsorDialogVisible,
                    sponsorAmount,
                    sponsorSubmitting,
                    showSponsorDialog,
                    confirmSponsor,
                    // 下载相关
                    downloadInfo,
                    downloadPanelCollapsed,
                    checkDownloadPermission,
                    handleDownload,
                    showMobileDownloadDialog,
                    getPermissionBadgeText,
                    showPermissionDialog,
                    // 授权相关
                    authLoading,
                    authUrl,
                    authUrlGenerated,
                    authError,
                    authResponse,
                    submitLoading,
                    accessToken,
                    tenantUrl,
                    tokenGenerated,
                    submitError,
                    getAuthUrl,
                    openAuthUrl,
                    copyAuthUrl,
                    submitAuthCode,
                    // 图标组件
                    User,
                    UserFilled,
                    Plus,
                    Star,
                    DataAnalysis,
                    Refresh,
                    Bell
                };
            }
        }).use(ElementPlus).mount('#app');
    </script>
</body>
</html>
