import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  Download, 
  Search, 
  Filter,
  File,
  FileText,
  Code,
  Image,
  Archive,
  Smartphone,
  Monitor,
  Globe,
  Star,
  Calendar,
  User,
  ExternalLink,
  CheckCircle
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'

interface DownloadItem {
  id: string
  name: string
  description: string
  version: string
  category: 'sdk' | 'tool' | 'document' | 'template' | 'mobile'
  platform: string[]
  size: string
  downloadCount: number
  rating: number
  releaseDate: string
  author: string
  downloadUrl: string
  featured?: boolean
  changelog?: string
}

interface DownloadCategory {
  id: string
  name: string
  description: string
  icon: React.ComponentType<any>
  items: DownloadItem[]
}

const DownloadsPage: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [selectedPlatform, setSelectedPlatform] = useState<string>('all')
  const [sortBy, setSortBy] = useState<string>('popular')
  const [downloadCategories, setDownloadCategories] = useState<DownloadCategory[]>([])
  const [loading, setLoading] = useState(true)

  // 模拟下载数据
  const mockDownloadCategories: DownloadCategory[] = [
    {
      id: 'sdk',
      name: 'SDK开发包',
      description: '各种编程语言的SDK和开发工具包',
      icon: Code,
      items: [
        {
          id: 'js-sdk',
          name: 'JavaScript SDK',
          description: '适用于Node.js和浏览器环境的JavaScript SDK，提供完整的API封装',
          version: 'v2.1.0',
          category: 'sdk',
          platform: ['Web', 'Node.js'],
          size: '156 KB',
          downloadCount: 15420,
          rating: 4.8,
          releaseDate: '2024-01-15',
          author: 'AugmentAPI团队',
          downloadUrl: '/downloads/augment-js-sdk-v2.1.0.zip',
          featured: true,
          changelog: '- 新增批量操作支持\n- 优化错误处理机制\n- 修复已知问题'
        },
        {
          id: 'python-sdk',
          name: 'Python SDK',
          description: '功能完整的Python SDK，支持异步操作和类型提示',
          version: 'v1.8.2',
          category: 'sdk',
          platform: ['Python'],
          size: '89 KB',
          downloadCount: 8930,
          rating: 4.7,
          releaseDate: '2024-01-12',
          author: 'AugmentAPI团队',
          downloadUrl: '/downloads/augment-python-sdk-v1.8.2.tar.gz'
        },
        {
          id: 'java-sdk',
          name: 'Java SDK',
          description: '企业级Java SDK，支持Spring Boot集成',
          version: 'v3.0.1',
          category: 'sdk',
          platform: ['Java'],
          size: '2.3 MB',
          downloadCount: 5670,
          rating: 4.6,
          releaseDate: '2024-01-10',
          author: 'AugmentAPI团队',
          downloadUrl: '/downloads/augment-java-sdk-v3.0.1.jar'
        }
      ]
    },
    {
      id: 'tools',
      name: '开发工具',
      description: '提高开发效率的实用工具和插件',
      icon: Monitor,
      items: [
        {
          id: 'postman-collection',
          name: 'Postman集合',
          description: '包含所有API接口的Postman集合文件，方便API测试',
          version: 'v1.0',
          category: 'tool',
          platform: ['Postman'],
          size: '45 KB',
          downloadCount: 12340,
          rating: 4.9,
          releaseDate: '2024-01-14',
          author: 'API团队',
          downloadUrl: '/downloads/augment-api-postman-collection.json',
          featured: true
        },
        {
          id: 'vscode-extension',
          name: 'VS Code扩展',
          description: 'AugmentAPI的VS Code扩展，提供代码补全和API文档查看',
          version: 'v0.5.2',
          category: 'tool',
          platform: ['VS Code'],
          size: '1.2 MB',
          downloadCount: 3450,
          rating: 4.5,
          releaseDate: '2024-01-08',
          author: '工具团队',
          downloadUrl: 'https://marketplace.visualstudio.com/items?itemName=augmentapi.vscode'
        }
      ]
    },
    {
      id: 'documents',
      name: '文档资料',
      description: '技术文档、白皮书和使用指南',
      icon: FileText,
      items: [
        {
          id: 'api-reference-pdf',
          name: 'API参考手册 (PDF)',
          description: '完整的API参考文档PDF版本，适合离线查看',
          version: 'v2024.1',
          category: 'document',
          platform: ['PDF'],
          size: '8.5 MB',
          downloadCount: 6780,
          rating: 4.7,
          releaseDate: '2024-01-15',
          author: '文档团队',
          downloadUrl: '/downloads/augment-api-reference-2024.1.pdf'
        },
        {
          id: 'integration-guide',
          name: '集成指南',
          description: '详细的集成指南和最佳实践文档',
          version: 'v1.2',
          category: 'document',
          platform: ['PDF'],
          size: '3.2 MB',
          downloadCount: 4560,
          rating: 4.8,
          releaseDate: '2024-01-11',
          author: '技术团队',
          downloadUrl: '/downloads/augment-integration-guide-v1.2.pdf'
        }
      ]
    },
    {
      id: 'templates',
      name: '项目模板',
      description: '快速开始的项目模板和示例代码',
      icon: Archive,
      items: [
        {
          id: 'react-template',
          name: 'React项目模板',
          description: '基于React和TypeScript的完整项目模板',
          version: 'v1.0.0',
          category: 'template',
          platform: ['React', 'TypeScript'],
          size: '450 KB',
          downloadCount: 2340,
          rating: 4.6,
          releaseDate: '2024-01-09',
          author: '前端团队',
          downloadUrl: '/downloads/augment-react-template-v1.0.0.zip'
        },
        {
          id: 'nodejs-template',
          name: 'Node.js项目模板',
          description: '基于Express和TypeScript的后端项目模板',
          version: 'v1.1.0',
          category: 'template',
          platform: ['Node.js', 'Express'],
          size: '320 KB',
          downloadCount: 1890,
          rating: 4.4,
          releaseDate: '2024-01-07',
          author: '后端团队',
          downloadUrl: '/downloads/augment-nodejs-template-v1.1.0.zip'
        }
      ]
    },
    {
      id: 'mobile',
      name: '移动应用',
      description: '移动端应用和工具',
      icon: Smartphone,
      items: [
        {
          id: 'ios-app',
          name: 'AugmentAPI iOS应用',
          description: 'iOS版本的AugmentAPI客户端应用',
          version: 'v1.2.0',
          category: 'mobile',
          platform: ['iOS'],
          size: '25.6 MB',
          downloadCount: 890,
          rating: 4.3,
          releaseDate: '2024-01-06',
          author: '移动团队',
          downloadUrl: 'https://apps.apple.com/app/augmentapi'
        },
        {
          id: 'android-app',
          name: 'AugmentAPI Android应用',
          description: 'Android版本的AugmentAPI客户端应用',
          version: 'v1.1.5',
          category: 'mobile',
          platform: ['Android'],
          size: '18.3 MB',
          downloadCount: 1240,
          rating: 4.2,
          releaseDate: '2024-01-05',
          author: '移动团队',
          downloadUrl: 'https://play.google.com/store/apps/details?id=com.augmentapi.app'
        }
      ]
    }
  ]

  // 加载下载数据
  useEffect(() => {
    const loadDownloads = async () => {
      setLoading(true)
      try {
        // TODO: 调用API获取下载数据
        await new Promise(resolve => setTimeout(resolve, 1000))
        setDownloadCategories(mockDownloadCategories)
      } catch (error) {
        console.error('加载下载数据失败:', error)
      } finally {
        setLoading(false)
      }
    }

    loadDownloads()
  }, [])

  // 过滤和排序下载项
  const filteredItems = downloadCategories.flatMap(category => 
    category.items.filter(item => {
      const matchesSearch = 
        item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.description.toLowerCase().includes(searchTerm.toLowerCase())
      
      const matchesCategory = selectedCategory === 'all' || item.category === selectedCategory
      const matchesPlatform = selectedPlatform === 'all' || item.platform.includes(selectedPlatform)
      
      return matchesSearch && matchesCategory && matchesPlatform
    }).map(item => ({ ...item, categoryName: category.name }))
  ).sort((a, b) => {
    switch (sortBy) {
      case 'popular':
        return b.downloadCount - a.downloadCount
      case 'newest':
        return new Date(b.releaseDate).getTime() - new Date(a.releaseDate).getTime()
      case 'rating':
        return b.rating - a.rating
      case 'name':
        return a.name.localeCompare(b.name)
      default:
        return 0
    }
  })

  // 获取分类图标
  const getCategoryIcon = (category: string) => {
    const icons = {
      sdk: Code,
      tool: Monitor,
      document: FileText,
      template: Archive,
      mobile: Smartphone
    }
    return icons[category as keyof typeof icons] || File
  }

  // 处理下载
  const handleDownload = (item: DownloadItem) => {
    // 模拟下载
    if (item.downloadUrl.startsWith('http')) {
      window.open(item.downloadUrl, '_blank')
    } else {
      // 模拟文件下载
      console.log(`下载文件: ${item.name}`)
      alert(`开始下载 ${item.name}`)
    }
  }

  // 获取所有平台
  const allPlatforms = Array.from(new Set(
    downloadCategories.flatMap(cat => 
      cat.items.flatMap(item => item.platform)
    )
  ))

  return (
    <div className="p-6 max-w-7xl mx-auto">
      {/* 页面标题 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="mb-8"
      >
        <div className="flex items-center space-x-3 mb-4">
          <div className="w-10 h-10 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center">
            <Download className="w-5 h-5 text-white" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">下载中心</h1>
            <p className="text-gray-600">SDK、工具、文档和模板下载</p>
          </div>
        </div>

        {/* 统计信息 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-3">
                <Code className="w-8 h-8 text-blue-500" />
                <div>
                  <p className="text-2xl font-bold text-gray-900">
                    {downloadCategories.reduce((total, cat) => total + cat.items.length, 0)}
                  </p>
                  <p className="text-sm text-gray-600">可下载项目</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-3">
                <Download className="w-8 h-8 text-green-500" />
                <div>
                  <p className="text-2xl font-bold text-gray-900">
                    {downloadCategories.reduce((total, cat) => 
                      total + cat.items.reduce((sum, item) => sum + item.downloadCount, 0), 0
                    ).toLocaleString()}
                  </p>
                  <p className="text-sm text-gray-600">总下载次数</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-3">
                <Star className="w-8 h-8 text-yellow-500" />
                <div>
                  <p className="text-2xl font-bold text-gray-900">4.6</p>
                  <p className="text-sm text-gray-600">平均评分</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-3">
                <Globe className="w-8 h-8 text-purple-500" />
                <div>
                  <p className="text-2xl font-bold text-gray-900">{allPlatforms.length}</p>
                  <p className="text-sm text-gray-600">支持平台</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </motion.div>

      {/* 搜索和过滤 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="mb-6"
      >
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    placeholder="搜索下载项目..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              
              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="选择分类" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部分类</SelectItem>
                  <SelectItem value="sdk">SDK开发包</SelectItem>
                  <SelectItem value="tool">开发工具</SelectItem>
                  <SelectItem value="document">文档资料</SelectItem>
                  <SelectItem value="template">项目模板</SelectItem>
                  <SelectItem value="mobile">移动应用</SelectItem>
                </SelectContent>
              </Select>

              <Select value={selectedPlatform} onValueChange={setSelectedPlatform}>
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="平台" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部平台</SelectItem>
                  {allPlatforms.map(platform => (
                    <SelectItem key={platform} value={platform}>
                      {platform}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="排序" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="popular">最受欢迎</SelectItem>
                  <SelectItem value="newest">最新发布</SelectItem>
                  <SelectItem value="rating">评分最高</SelectItem>
                  <SelectItem value="name">名称排序</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* 下载列表 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
        className="space-y-4"
      >
        {loading ? (
          Array.from({ length: 6 }).map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="animate-pulse space-y-4">
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-gray-200 rounded-lg" />
                    <div className="flex-1 space-y-2">
                      <div className="h-4 bg-gray-200 rounded w-3/4" />
                      <div className="h-3 bg-gray-200 rounded w-1/2" />
                    </div>
                    <div className="h-10 bg-gray-200 rounded w-24" />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        ) : filteredItems.length === 0 ? (
          <Card>
            <CardContent className="p-12 text-center">
              <Download className="w-12 h-12 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                没有找到匹配的下载项目
              </h3>
              <p className="text-gray-600">
                请尝试调整搜索条件或过滤器
              </p>
            </CardContent>
          </Card>
        ) : (
          filteredItems.map((item) => {
            const CategoryIcon = getCategoryIcon(item.category)
            
            return (
              <Card key={item.id} className="hover:shadow-md transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-start space-x-4">
                    {/* 图标 */}
                    <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center flex-shrink-0">
                      <CategoryIcon className="w-6 h-6 text-gray-600" />
                    </div>

                    {/* 内容 */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex items-center space-x-3">
                          <h3 className="text-lg font-semibold text-gray-900">
                            {item.name}
                          </h3>
                          {item.featured && (
                            <Badge className="bg-yellow-100 text-yellow-700">
                              推荐
                            </Badge>
                          )}
                          <Badge variant="outline" className="text-xs">
                            {item.version}
                          </Badge>
                        </div>
                        <div className="flex items-center space-x-2">
                          <div className="flex items-center space-x-1">
                            <Star className="w-4 h-4 text-yellow-500" />
                            <span className="text-sm text-gray-600">{item.rating}</span>
                          </div>
                          <span className="text-sm text-gray-500">
                            {item.downloadCount.toLocaleString()} 下载
                          </span>
                        </div>
                      </div>

                      <p className="text-gray-600 mb-3 leading-relaxed">
                        {item.description}
                      </p>

                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4 text-sm text-gray-500">
                          <div className="flex items-center space-x-1">
                            <User className="w-3 h-3" />
                            <span>{item.author}</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Calendar className="w-3 h-3" />
                            <span>{item.releaseDate}</span>
                          </div>
                          <span>大小: {item.size}</span>
                          <div className="flex items-center space-x-1">
                            <span>平台:</span>
                            {item.platform.map((platform, index) => (
                              <Badge key={platform} variant="outline" className="text-xs">
                                {platform}
                              </Badge>
                            ))}
                          </div>
                        </div>

                        <Button
                          onClick={() => handleDownload(item)}
                          className="flex items-center space-x-2"
                        >
                          <Download className="w-4 h-4" />
                          <span>下载</span>
                        </Button>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )
          })
        )}
      </motion.div>
    </div>
  )
}

export default DownloadsPage
