import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  FileText, 
  Search, 
  Filter,
  Play,
  Copy,
  CheckCircle,
  AlertCircle,
  Code,
  Book,
  Zap
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible'
import type { ApiCategory, ApiEndpoint } from '@/types'

const ApiDocsPage: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [selectedMethod, setSelectedMethod] = useState<string>('all')
  const [expandedEndpoints, setExpandedEndpoints] = useState<Set<string>>(new Set())
  const [apiCategories, setApiCategories] = useState<ApiCategory[]>([])
  const [loading, setLoading] = useState(true)

  // 模拟API文档数据
  const mockApiCategories: ApiCategory[] = [
    {
      id: 'auth',
      name: '认证管理',
      description: '用户认证和授权相关接口',
      endpoints: [
        {
          id: 'auth-login',
          method: 'POST',
          path: '/api/auth/login',
          summary: '用户登录',
          description: '使用邮箱和密码进行用户登录',
          tags: ['认证'],
          requiresAuth: false,
          requestBody: {
            contentType: 'application/json',
            schema: {
              type: 'object',
              properties: {
                email: { type: 'string', description: '用户邮箱' },
                password: { type: 'string', description: '用户密码' }
              },
              required: ['email', 'password']
            },
            example: {
              email: '<EMAIL>',
              password: 'password123'
            }
          },
          responses: [
            {
              statusCode: 200,
              description: '登录成功',
              example: {
                success: true,
                data: {
                  token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
                  user: {
                    id: '1',
                    email: '<EMAIL>',
                    username: 'user123'
                  }
                }
              }
            },
            {
              statusCode: 401,
              description: '认证失败',
              example: {
                success: false,
                error: '邮箱或密码错误'
              }
            }
          ]
        },
        {
          id: 'auth-logout',
          method: 'POST',
          path: '/api/auth/logout',
          summary: '用户登出',
          description: '退出当前用户会话',
          tags: ['认证'],
          requiresAuth: true,
          responses: [
            {
              statusCode: 200,
              description: '登出成功',
              example: {
                success: true,
                message: '已成功登出'
              }
            }
          ]
        }
      ]
    },
    {
      id: 'users',
      name: '用户管理',
      description: '用户信息管理相关接口',
      endpoints: [
        {
          id: 'users-profile',
          method: 'GET',
          path: '/api/users/profile',
          summary: '获取用户资料',
          description: '获取当前登录用户的详细资料信息',
          tags: ['用户'],
          requiresAuth: true,
          responses: [
            {
              statusCode: 200,
              description: '获取成功',
              example: {
                success: true,
                data: {
                  id: '1',
                  email: '<EMAIL>',
                  username: 'user123',
                  avatar: 'https://example.com/avatar.jpg',
                  created_at: '2024-01-01T00:00:00Z'
                }
              }
            }
          ]
        },
        {
          id: 'users-update',
          method: 'PUT',
          path: '/api/users/profile',
          summary: '更新用户资料',
          description: '更新当前登录用户的资料信息',
          tags: ['用户'],
          requiresAuth: true,
          requestBody: {
            contentType: 'application/json',
            schema: {
              type: 'object',
              properties: {
                username: { type: 'string', description: '用户名' },
                bio: { type: 'string', description: '个人简介' }
              }
            },
            example: {
              username: 'newusername',
              bio: '这是我的个人简介'
            }
          },
          responses: [
            {
              statusCode: 200,
              description: '更新成功',
              example: {
                success: true,
                message: '资料更新成功'
              }
            }
          ]
        }
      ]
    },
    {
      id: 'chat',
      name: '聊天系统',
      description: '实时聊天相关接口',
      endpoints: [
        {
          id: 'chat-messages',
          method: 'GET',
          path: '/api/chat/messages',
          summary: '获取聊天消息',
          description: '获取聊天室的历史消息',
          tags: ['聊天'],
          requiresAuth: true,
          parameters: [
            {
              name: 'page',
              in: 'query',
              required: false,
              type: 'integer',
              description: '页码，默认为1',
              example: 1
            },
            {
              name: 'limit',
              in: 'query',
              required: false,
              type: 'integer',
              description: '每页消息数量，默认为20',
              example: 20
            }
          ],
          responses: [
            {
              statusCode: 200,
              description: '获取成功',
              example: {
                success: true,
                data: {
                  messages: [
                    {
                      id: '1',
                      user_id: '1',
                      username: 'user123',
                      content: '你好，大家好！',
                      created_at: '2024-01-01T12:00:00Z'
                    }
                  ],
                  pagination: {
                    page: 1,
                    limit: 20,
                    total: 100
                  }
                }
              }
            }
          ]
        }
      ]
    }
  ]

  // 加载API文档数据
  useEffect(() => {
    const loadApiDocs = async () => {
      setLoading(true)
      try {
        // TODO: 调用API获取文档数据
        await new Promise(resolve => setTimeout(resolve, 1000))
        setApiCategories(mockApiCategories)
      } catch (error) {
        console.error('加载API文档失败:', error)
      } finally {
        setLoading(false)
      }
    }

    loadApiDocs()
  }, [])

  // 过滤API接口
  const filteredEndpoints = apiCategories.flatMap(category => 
    category.endpoints.filter(endpoint => {
      const matchesSearch = 
        endpoint.summary.toLowerCase().includes(searchTerm.toLowerCase()) ||
        endpoint.path.toLowerCase().includes(searchTerm.toLowerCase()) ||
        endpoint.description.toLowerCase().includes(searchTerm.toLowerCase())
      
      const matchesCategory = selectedCategory === 'all' || category.id === selectedCategory
      const matchesMethod = selectedMethod === 'all' || endpoint.method === selectedMethod
      
      return matchesSearch && matchesCategory && matchesMethod
    }).map(endpoint => ({ ...endpoint, categoryName: category.name }))
  )

  // 获取HTTP方法的颜色
  const getMethodColor = (method: string) => {
    const colors = {
      GET: 'bg-green-100 text-green-700 border-green-200',
      POST: 'bg-blue-100 text-blue-700 border-blue-200',
      PUT: 'bg-orange-100 text-orange-700 border-orange-200',
      DELETE: 'bg-red-100 text-red-700 border-red-200',
      PATCH: 'bg-purple-100 text-purple-700 border-purple-200'
    }
    return colors[method as keyof typeof colors] || 'bg-gray-100 text-gray-700 border-gray-200'
  }

  // 切换接口展开状态
  const toggleEndpoint = (endpointId: string) => {
    setExpandedEndpoints(prev => {
      const newSet = new Set(prev)
      if (newSet.has(endpointId)) {
        newSet.delete(endpointId)
      } else {
        newSet.add(endpointId)
      }
      return newSet
    })
  }

  // 复制代码
  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text)
      // TODO: 显示复制成功提示
    } catch (error) {
      console.error('复制失败:', error)
    }
  }

  return (
    <div className="p-6 max-w-7xl mx-auto">
      {/* 页面标题 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="mb-8"
      >
        <div className="flex items-center space-x-3 mb-4">
          <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center">
            <FileText className="w-5 h-5 text-white" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">API文档</h1>
            <p className="text-gray-600">完整的API接口文档和使用指南</p>
          </div>
        </div>

        {/* 快速统计 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-3">
                <Book className="w-8 h-8 text-blue-500" />
                <div>
                  <p className="text-2xl font-bold text-gray-900">
                    {apiCategories.length}
                  </p>
                  <p className="text-sm text-gray-600">API分类</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-3">
                <Zap className="w-8 h-8 text-green-500" />
                <div>
                  <p className="text-2xl font-bold text-gray-900">
                    {apiCategories.reduce((total, cat) => total + cat.endpoints.length, 0)}
                  </p>
                  <p className="text-sm text-gray-600">API接口</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-3">
                <Code className="w-8 h-8 text-purple-500" />
                <div>
                  <p className="text-2xl font-bold text-gray-900">100%</p>
                  <p className="text-sm text-gray-600">文档覆盖率</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </motion.div>

      {/* 搜索和过滤 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="mb-6"
      >
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    placeholder="搜索API接口..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              
              <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="选择分类" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部分类</SelectItem>
                  {apiCategories.map(category => (
                    <SelectItem key={category.id} value={category.id}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select value={selectedMethod} onValueChange={setSelectedMethod}>
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="HTTP方法" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部方法</SelectItem>
                  <SelectItem value="GET">GET</SelectItem>
                  <SelectItem value="POST">POST</SelectItem>
                  <SelectItem value="PUT">PUT</SelectItem>
                  <SelectItem value="DELETE">DELETE</SelectItem>
                  <SelectItem value="PATCH">PATCH</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* API接口列表 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
        className="space-y-4"
      >
        {loading ? (
          Array.from({ length: 5 }).map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="animate-pulse space-y-4">
                  <div className="flex items-center space-x-4">
                    <div className="h-6 bg-gray-200 rounded w-16" />
                    <div className="h-4 bg-gray-200 rounded flex-1" />
                  </div>
                  <div className="h-3 bg-gray-200 rounded w-3/4" />
                </div>
              </CardContent>
            </Card>
          ))
        ) : filteredEndpoints.length === 0 ? (
          <Card>
            <CardContent className="p-12 text-center">
              <FileText className="w-12 h-12 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                没有找到匹配的API接口
              </h3>
              <p className="text-gray-600">
                请尝试调整搜索条件或过滤器
              </p>
            </CardContent>
          </Card>
        ) : (
          filteredEndpoints.map((endpoint) => (
            <Card key={endpoint.id} className="overflow-hidden">
              <Collapsible
                open={expandedEndpoints.has(endpoint.id)}
                onOpenChange={() => toggleEndpoint(endpoint.id)}
              >
                <CollapsibleTrigger asChild>
                  <CardHeader className="cursor-pointer hover:bg-gray-50 transition-colors">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <Badge 
                          className={`px-3 py-1 text-xs font-mono border ${getMethodColor(endpoint.method)}`}
                        >
                          {endpoint.method}
                        </Badge>
                        <div className="flex-1">
                          <div className="flex items-center space-x-2">
                            <h3 className="text-lg font-semibold text-gray-900">
                              {endpoint.summary}
                            </h3>
                            {endpoint.requiresAuth && (
                              <Badge variant="outline" className="text-xs">
                                需要认证
                              </Badge>
                            )}
                            {endpoint.deprecated && (
                              <Badge variant="destructive" className="text-xs">
                                已废弃
                              </Badge>
                            )}
                          </div>
                          <p className="text-sm text-gray-600 mt-1">
                            {endpoint.description}
                          </p>
                          <code className="text-sm font-mono text-blue-600 mt-2 block">
                            {endpoint.path}
                          </code>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge variant="secondary" className="text-xs">
                          {(endpoint as any).categoryName}
                        </Badge>
                      </div>
                    </div>
                  </CardHeader>
                </CollapsibleTrigger>
                
                <CollapsibleContent>
                  <CardContent className="pt-0">
                    <Tabs defaultValue="request" className="w-full">
                      <TabsList className="grid w-full grid-cols-3">
                        <TabsTrigger value="request">请求</TabsTrigger>
                        <TabsTrigger value="response">响应</TabsTrigger>
                        <TabsTrigger value="examples">示例</TabsTrigger>
                      </TabsList>
                      
                      <TabsContent value="request" className="space-y-4">
                        {/* 请求参数 */}
                        {endpoint.parameters && endpoint.parameters.length > 0 && (
                          <div>
                            <h4 className="text-sm font-medium text-gray-900 mb-2">请求参数</h4>
                            <div className="border rounded-lg overflow-hidden">
                              <table className="w-full text-sm">
                                <thead className="bg-gray-50">
                                  <tr>
                                    <th className="px-4 py-2 text-left">参数名</th>
                                    <th className="px-4 py-2 text-left">位置</th>
                                    <th className="px-4 py-2 text-left">类型</th>
                                    <th className="px-4 py-2 text-left">必需</th>
                                    <th className="px-4 py-2 text-left">说明</th>
                                  </tr>
                                </thead>
                                <tbody>
                                  {endpoint.parameters.map((param, index) => (
                                    <tr key={index} className="border-t">
                                      <td className="px-4 py-2 font-mono text-blue-600">
                                        {param.name}
                                      </td>
                                      <td className="px-4 py-2">{param.in}</td>
                                      <td className="px-4 py-2">{param.type}</td>
                                      <td className="px-4 py-2">
                                        {param.required ? (
                                          <CheckCircle className="w-4 h-4 text-green-500" />
                                        ) : (
                                          <AlertCircle className="w-4 h-4 text-gray-400" />
                                        )}
                                      </td>
                                      <td className="px-4 py-2">{param.description}</td>
                                    </tr>
                                  ))}
                                </tbody>
                              </table>
                            </div>
                          </div>
                        )}

                        {/* 请求体 */}
                        {endpoint.requestBody && (
                          <div>
                            <h4 className="text-sm font-medium text-gray-900 mb-2">请求体</h4>
                            <div className="bg-gray-50 rounded-lg p-4">
                              <div className="flex items-center justify-between mb-2">
                                <span className="text-xs text-gray-600">
                                  Content-Type: {endpoint.requestBody.contentType}
                                </span>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => copyToClipboard(JSON.stringify(endpoint.requestBody.example, null, 2))}
                                >
                                  <Copy className="w-4 h-4" />
                                </Button>
                              </div>
                              <pre className="text-xs overflow-x-auto">
                                <code>
                                  {JSON.stringify(endpoint.requestBody.example, null, 2)}
                                </code>
                              </pre>
                            </div>
                          </div>
                        )}
                      </TabsContent>
                      
                      <TabsContent value="response" className="space-y-4">
                        {endpoint.responses.map((response, index) => (
                          <div key={index}>
                            <div className="flex items-center space-x-2 mb-2">
                              <Badge 
                                className={`px-2 py-1 text-xs ${
                                  response.statusCode < 300 
                                    ? 'bg-green-100 text-green-700' 
                                    : 'bg-red-100 text-red-700'
                                }`}
                              >
                                {response.statusCode}
                              </Badge>
                              <span className="text-sm font-medium">{response.description}</span>
                            </div>
                            <div className="bg-gray-50 rounded-lg p-4">
                              <div className="flex items-center justify-between mb-2">
                                <span className="text-xs text-gray-600">响应示例</span>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => copyToClipboard(JSON.stringify(response.example, null, 2))}
                                >
                                  <Copy className="w-4 h-4" />
                                </Button>
                              </div>
                              <pre className="text-xs overflow-x-auto">
                                <code>
                                  {JSON.stringify(response.example, null, 2)}
                                </code>
                              </pre>
                            </div>
                          </div>
                        ))}
                      </TabsContent>
                      
                      <TabsContent value="examples">
                        <div className="space-y-4">
                          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                            <h4 className="text-sm font-medium text-blue-900 mb-2">
                              cURL 示例
                            </h4>
                            <div className="bg-gray-900 text-gray-100 rounded p-3 text-xs font-mono overflow-x-auto">
                              <div className="flex items-center justify-between mb-2">
                                <span>curl</span>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="text-gray-400 hover:text-white"
                                  onClick={() => copyToClipboard(`curl -X ${endpoint.method} "${endpoint.path}"`)}
                                >
                                  <Copy className="w-3 h-3" />
                                </Button>
                              </div>
                              <code>
                                curl -X {endpoint.method} "{endpoint.path}"
                                {endpoint.requiresAuth && '\n  -H "Authorization: Bearer YOUR_TOKEN"'}
                                {endpoint.requestBody && '\n  -H "Content-Type: application/json"'}
                                {endpoint.requestBody && `\n  -d '${JSON.stringify(endpoint.requestBody.example)}'`}
                              </code>
                            </div>
                          </div>
                          
                          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                            <h4 className="text-sm font-medium text-green-900 mb-2">
                              JavaScript 示例
                            </h4>
                            <div className="bg-gray-900 text-gray-100 rounded p-3 text-xs font-mono overflow-x-auto">
                              <div className="flex items-center justify-between mb-2">
                                <span>fetch</span>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="text-gray-400 hover:text-white"
                                  onClick={() => copyToClipboard(`fetch('${endpoint.path}', { method: '${endpoint.method}' })`)}
                                >
                                  <Copy className="w-3 h-3" />
                                </Button>
                              </div>
                              <code>
                                {`fetch('${endpoint.path}', {
  method: '${endpoint.method}',${endpoint.requiresAuth ? `
  headers: {
    'Authorization': 'Bearer YOUR_TOKEN',
    'Content-Type': 'application/json'
  },` : ''}${endpoint.requestBody ? `
  body: JSON.stringify(${JSON.stringify(endpoint.requestBody.example, null, 2)})` : ''}
})`}
                              </code>
                            </div>
                          </div>
                        </div>
                      </TabsContent>
                    </Tabs>
                  </CardContent>
                </CollapsibleContent>
              </Collapsible>
            </Card>
          ))
        )}
      </motion.div>
    </div>
  )
}

export default ApiDocsPage
