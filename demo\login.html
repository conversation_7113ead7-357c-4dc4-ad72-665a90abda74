<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - AugmentAPI</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>🧸</text></svg>">
    
    <!-- 引入Element Plus CSS - 本地资源 -->
    <link rel="stylesheet" href="/lib/element-plus/index.min.css">

    <!-- 引入Vue 3 - 本地资源 -->
    <script src="/lib/vue/vue.global.min.js"></script>
    <!-- 引入Element Plus - 本地资源 -->
    <script src="/lib/element-plus/index.full.min.js"></script>
    <!-- 引入Element Plus图标 - 本地资源 -->
    <script src="/lib/element-plus/icons.iife.min.js"></script>
    <!-- 引入Socket.IO客户端 - 本地资源 -->
    <script src="/lib/socket.io/socket.io.min.js"></script>

    <!-- 引入认证工具 -->
    <script src="/js/auth-utils.js"></script>
    <!-- 静默控制台（生产环境隐藏console输出；加 ?debug=1 显示） -->
    <script src="/js/quiet-console.js"></script>

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: #f5f7fa;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .login-container {
            display: flex;
            max-width: 1200px;
            width: 100%;
            min-height: 500px;
            background: #fff;
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .login-left {
            flex: 1;
            padding: 40px 30px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            border-right: 1px solid #e8eaec;
        }

        .login-center {
            flex: 1;
            padding: 40px 30px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            border-right: 1px solid #e8eaec;
            background: linear-gradient(135deg, #ffffff 0%, #f8fbff 80%, #e6f3ff 100%);
        }

        .login-right {
            flex: 1;
            padding: 40px 30px;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            min-height: 500px;
        }
        
        .logo {
            font-size: 4em;
            margin-bottom: 24px;
        }
        
        .brand-title {
            font-size: 2.2em;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 16px;
        }
        
        .brand-subtitle {
            font-size: 1.1em;
            color: #4a5568;
            margin-bottom: 32px;
            line-height: 1.6;
        }
        
        .feature-list {
            text-align: left;
            max-width: 300px;
        }
        
        .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 16px;
            color: #4a5568;
            font-size: 14px;
        }
        
        .feature-icon {
            margin-right: 12px;
            font-size: 16px;
        }
        
        .login-form {
            max-width: 360px;
            margin: 0 auto;
        }
        
        .login-title {
            font-size: 1.8em;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 8px;
            text-align: center;
        }
        
        .login-subtitle {
            color: #718096;
            text-align: center;
            margin-bottom: 32px;
        }
        
        .qr-section {
            text-align: center;
            margin-bottom: 32px;
        }
        
        .qr-image {
            width: 240px;
            height: 280px;
            border-radius: 12px;
            border: 1px solid #e8eaec;
            margin-bottom: 16px;
            object-fit: cover;
        }
        
        .qr-title {
            color: #2d3748;
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 24px;
        }

        .community-section {
            text-align: left;
            margin-top: 20px;
        }

        .community-title {
            font-size: 1.2em;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 8px;
            text-align: center;
        }

        .community-subtitle {
            color: #4a5568;
            font-size: 13px;
            margin-bottom: 20px;
            text-align: center;
            line-height: 1.4;
        }

        .community-features {
            text-align: left;
        }

        .community-features .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
            color: #4a5568;
            font-size: 13px;
            line-height: 1.4;
        }

        .community-features .feature-icon {
            margin-right: 8px;
            font-size: 14px;
            width: 20px;
            text-align: center;
        }
        
        .login-button {
            width: 100%;
            height: 48px;
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 16px;
        }
        
        .login-tips {
            background: linear-gradient(135deg, #ffffff 0%, #f8fbff 80%, #e6f3ff 100%);
            border: 1px solid #e8eaec;
            border-radius: 8px;
            padding: 16px;
            color: #4a5568;
            font-size: 14px;
            line-height: 1.5;
        }

        .tips-title {
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 8px;
        }

        .intro-title {
            font-size: 1.5em;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 12px;
        }

        .intro-subtitle {
            color: #4a5568;
            font-size: 14px;
            margin-bottom: 24px;
            line-height: 1.5;
        }

        .section-divider {
            width: 100%;
            height: 1px;
            background: linear-gradient(90deg, transparent, #e2e8f0, transparent);
            margin: 24px 0;
            position: relative;
        }

        .section-divider::before {
            content: '';
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            width: 6px;
            height: 6px;
            background: #cbd5e0;
            border-radius: 50%;
        }

        .new-feature-placeholder {
            margin-top: 30px;
            text-align: center;
            padding: 40px 20px;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-radius: 12px;
            border: 2px dashed #cbd5e0;
        }

        .placeholder-icon {
            font-size: 3em;
            margin-bottom: 16px;
            opacity: 0.7;
        }

        .placeholder-title {
            font-size: 1.3em;
            font-weight: 600;
            color: #4a5568;
            margin-bottom: 8px;
        }

        .placeholder-text {
            color: #718096;
            font-size: 14px;
            line-height: 1.5;
        }

        /* 运行时间统计样式 */
        .runtime-section {
            margin-top: 30px;
            text-align: center;
            padding: 24px 20px;
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            border-radius: 12px;
            border: 1px solid #bae6fd;
        }

        .runtime-title {
            font-size: 1.3em;
            font-weight: 600;
            color: #0369a1;
            margin-bottom: 8px;
        }

        .runtime-subtitle {
            color: #0284c7;
            font-size: 14px;
            margin-bottom: 20px;
            line-height: 1.5;
        }

        .runtime-stats {
            margin-bottom: 20px;
        }

        .runtime-stat-item {
            text-align: center;
        }

        .runtime-stat-item .el-statistic__content {
            font-size: 18px !important;
            font-weight: 600;
            color: #0369a1;
        }

        .runtime-stat-item .el-statistic__head {
            font-size: 12px;
            color: #64748b;
            margin-bottom: 4px;
        }

        .runtime-summary {
            margin-top: 16px;
        }
        
        /* 移动端适配 */
        @media (max-width: 768px) {
            .login-container {
                flex-direction: column;
                max-width: 400px;
                min-height: auto;
            }

            .login-left, .login-center, .login-right {
                padding: 30px 20px;
                border-right: none;
                border-bottom: 1px solid #e8eaec;
            }

            .login-right {
                border-bottom: none;
            }

            .brand-title {
                font-size: 1.8em;
            }

            .feature-list {
                max-width: 100%;
            }

            .qr-image {
                width: 180px;
                height: 210px;
            }
        }

        /* 登录页面统计卡片样式 */
        .login-stat-card {
            border: none;
            border-radius: 8px;
            transition: all 0.3s ease;
            margin-bottom: 12px;
        }

        .login-stat-card:hover {
            transform: translateY(-2px);
        }

        .login-stat-content {
            display: flex;
            align-items: center;
            padding: 4px 0;
        }

        .login-stat-icon {
            width: 36px;
            height: 36px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            color: white;
        }

        .login-stat-icon.emoji-icon {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%) !important;
            border: 2px solid #e2e8f0;
            font-size: 18px;
            color: initial;
        }

        .login-stat-info {
            flex: 1;
        }

        .login-stat-number {
            font-size: 20px;
            font-weight: bold;
            color: #303133;
            line-height: 1;
            margin-bottom: 2px;
        }

        .login-stat-label {
            font-size: 12px;
            color: #909399;
            line-height: 1;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="login-container">
            <!-- 左侧：技术交流群 -->
            <div class="login-left">
                <img src="/img/image.png" alt="加入技术群" class="qr-image" @error="handleImageError">
                <div class="qr-title">📱 扫码加入技术交流群</div>

                <div class="community-section">
                    <h3 class="community-title">💬 技术交流群</h3>
                    <p class="community-subtitle">加入我们的技术社区，与开发者一起成长</p>

                    <div class="community-features">
                        <div class="feature-item">
                            <span class="feature-icon">👥</span>
                            <span>活跃的技术讨论氛围</span>
                        </div>
                        <div class="feature-item">
                            <span class="feature-icon">🎯</span>
                            <span>实时问题解答与指导</span>
                        </div>
                        <div class="feature-item">
                            <span class="feature-icon">💎</span>
                            <span>最新技术资讯分享</span>
                        </div>
                        <div class="feature-item">
                            <span class="feature-icon">🤝</span>
                            <span>项目合作与经验交流</span>
                        </div>
                        <div class="feature-item">
                            <span class="feature-icon">🎁</span>
                            <span>独家学习资源与福利</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 中间：登录表单 -->
            <div class="login-center">
                <div class="logo">🧸</div>
                <h1 class="brand-title">欢迎登录</h1>
                <p class="brand-subtitle">Augment 管理系统</p>

                <!-- 登录按钮 -->
                <el-button
                    type="primary"
                    class="login-button"
                    :loading="loginLoading"
                    @click="handleLinuxDoLogin">
                    <span v-if="!loginLoading">🐧 使用 LinuxDo 登录</span>
                    <span v-else>登录中...</span>
                </el-button>

                <!-- GitHub登录按钮 -->
                <el-button
                    type="success"
                    class="login-button"
                    :loading="githubLoginLoading"
                    @click="handleGitHubLogin">
                    <span v-if="!githubLoginLoading">🐙 使用 GitHub 登录</span>
                    <span v-else>登录中...</span>
                </el-button>

                <!-- 提示信息 -->
                <div class="login-tips">
                    <div class="tips-title">💡 提示</div>
                    <div>支持LinuxDo和GitHub账户安全登录</div>
                </div>
            </div>
            
            <!-- 右侧：网站统计和介绍 -->
            <div class="login-right">
                <h2 class="intro-title">📊 网站统计</h2>
                <p class="intro-subtitle">实时数据展示平台活跃度</p>

                <!-- 统计数据网格 -->
                <el-row :gutter="12" style="margin-bottom: 24px;">
                    <el-col :span="12">
                        <el-card shadow="hover" class="login-stat-card online-card">
                            <div class="login-stat-content">
                                <div class="login-stat-icon emoji-icon">
                                    🟢
                                </div>
                                <div class="login-stat-info">
                                    <div class="login-stat-number">{{ siteStats.onlineUsers }}</div>
                                    <div class="login-stat-label">在线人数</div>
                                </div>
                            </div>
                        </el-card>
                    </el-col>
                    <el-col :span="12">
                        <el-card shadow="hover" class="login-stat-card total-card">
                            <div class="login-stat-content">
                                <div class="login-stat-icon emoji-icon">
                                    👥
                                </div>
                                <div class="login-stat-info">
                                    <div class="login-stat-number">{{ siteStats.users.total }}</div>
                                    <div class="login-stat-label">总用户</div>
                                </div>
                            </div>
                        </el-card>
                    </el-col>
                    <el-col :span="12">
                        <el-card shadow="hover" class="login-stat-card today-card">
                            <div class="login-stat-content">
                                <div class="login-stat-icon emoji-icon">
                                    ✨
                                </div>
                                <div class="login-stat-info">
                                    <div class="login-stat-number">{{ siteStats.users.todayNew }}</div>
                                    <div class="login-stat-label">今日新增</div>
                                </div>
                            </div>
                        </el-card>
                    </el-col>
                    <el-col :span="12">
                        <el-card shadow="hover" class="login-stat-card yesterday-card">
                            <div class="login-stat-content">
                                <div class="login-stat-icon emoji-icon">
                                    📈
                                </div>
                                <div class="login-stat-info">
                                    <div class="login-stat-number">{{ siteStats.users.yesterdayNew }}</div>
                                    <div class="login-stat-label">昨日新增</div>
                                </div>
                            </div>
                        </el-card>
                    </el-col>
                </el-row>

                <!-- 网站运行时间统计 -->
                <div class="runtime-section">
                    <h3 class="runtime-title">⏰ 网站运行时间</h3>
                    <p class="runtime-subtitle">自2025年8月10日上线以来</p>

                    <div class="runtime-stats">
                        <el-row :gutter="8">
                            <el-col :span="8">
                                <el-statistic
                                    title="年"
                                    :value="runtime.years"
                                    :precision="0"
                                    class="runtime-stat-item">
                                    <template #suffix>
                                        <span style="font-size: 12px; color: #909399;">年</span>
                                    </template>
                                </el-statistic>
                            </el-col>
                            <el-col :span="8">
                                <el-statistic
                                    title="月"
                                    :value="runtime.months"
                                    :precision="0"
                                    class="runtime-stat-item">
                                    <template #suffix>
                                        <span style="font-size: 12px; color: #909399;">月</span>
                                    </template>
                                </el-statistic>
                            </el-col>
                            <el-col :span="8">
                                <el-statistic
                                    title="日"
                                    :value="runtime.days"
                                    :precision="0"
                                    class="runtime-stat-item">
                                    <template #suffix>
                                        <span style="font-size: 12px; color: #909399;">日</span>
                                    </template>
                                </el-statistic>
                            </el-col>
                        </el-row>

                        <el-row :gutter="8" style="margin-top: 16px;">
                            <el-col :span="8">
                                <el-statistic
                                    title="时"
                                    :value="runtime.hours"
                                    :precision="0"
                                    class="runtime-stat-item">
                                    <template #suffix>
                                        <span style="font-size: 12px; color: #909399;">时</span>
                                    </template>
                                </el-statistic>
                            </el-col>
                            <el-col :span="8">
                                <el-statistic
                                    title="分"
                                    :value="runtime.minutes"
                                    :precision="0"
                                    class="runtime-stat-item">
                                    <template #suffix>
                                        <span style="font-size: 12px; color: #909399;">分</span>
                                    </template>
                                </el-statistic>
                            </el-col>
                            <el-col :span="8">
                                <el-statistic
                                    title="秒"
                                    :value="runtime.seconds"
                                    :precision="0"
                                    class="runtime-stat-item">
                                    <template #suffix>
                                        <span style="font-size: 12px; color: #909399;">秒</span>
                                    </template>
                                </el-statistic>
                            </el-col>
                        </el-row>
                    </div>

                    <div class="runtime-summary">
                        <el-tag type="success" size="small">
                            总计运行 {{ runtime.totalDays }} 天
                        </el-tag>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 错误提示 -->
        <el-dialog v-model="showError" title="登录失败" width="400px">
            <p>{{ errorMessage }}</p>
            <template #footer>
                <el-button @click="showError = false">确定</el-button>
            </template>
        </el-dialog>
    </div>

    <!-- CDN资源已由服务端自动注入到head部分 -->

    <script>
        const { createApp, ref, reactive, onMounted } = Vue;
        const { User, UserFilled, Plus, Star } = ElementPlusIconsVue;

        createApp({
            setup() {
                const loginLoading = ref(false);
                const githubLoginLoading = ref(false);
                const showError = ref(false);
                const errorMessage = ref('');

                // 网站统计数据
                const siteStats = reactive({
                    onlineUsers: 0,
                    users: {
                        total: 0,
                        todayNew: 0,
                        yesterdayNew: 0
                    }
                });

                // 网站运行时间统计
                const runtime = reactive({
                    years: 0,
                    months: 0,
                    days: 0,
                    hours: 0,
                    minutes: 0,
                    seconds: 0,
                    totalDays: 0
                });

                // 计算运行时间的函数
                const calculateRuntime = () => {
                    const startDate = new Date('2025-08-10 00:00:00');
                    const now = new Date();

                    // 计算总的时间差（毫秒）
                    const totalMs = now.getTime() - startDate.getTime();

                    // 计算各个时间单位
                    const totalSeconds = Math.floor(totalMs / 1000);
                    const totalMinutes = Math.floor(totalSeconds / 60);
                    const totalHours = Math.floor(totalMinutes / 60);
                    const totalDays = Math.floor(totalHours / 24);

                    // 计算年月日
                    let years = now.getFullYear() - startDate.getFullYear();
                    let months = now.getMonth() - startDate.getMonth();
                    let days = now.getDate() - startDate.getDate();

                    // 处理负数情况
                    if (days < 0) {
                        months--;
                        const lastMonth = new Date(now.getFullYear(), now.getMonth(), 0);
                        days += lastMonth.getDate();
                    }

                    if (months < 0) {
                        years--;
                        months += 12;
                    }

                    // 计算剩余的时分秒
                    const remainingHours = totalHours % 24;
                    const remainingMinutes = totalMinutes % 60;
                    const remainingSeconds = totalSeconds % 60;

                    // 更新响应式数据
                    runtime.years = years;
                    runtime.months = months;
                    runtime.days = days;
                    runtime.hours = remainingHours;
                    runtime.minutes = remainingMinutes;
                    runtime.seconds = remainingSeconds;
                    runtime.totalDays = totalDays;
                };

                const handleLinuxDoLogin = async () => {
                    try {
                        loginLoading.value = true;
                        
                        const response = await fetch('/api/auth/linuxdo', {
                            method: 'GET',
                            headers: {
                                'Content-Type': 'application/json',
                            }
                        });

                        if (response.ok) {
                            const data = await response.json();
                            if (data.auth_url) {
                                window.location.href = data.auth_url;
                            } else {
                                throw new Error('未获取到授权链接');
                            }
                        } else {
                            const errorData = await response.json();
                            throw new Error(errorData.error || '登录请求失败');
                        }
                    } catch (error) {
                        console.error('登录错误:', error);
                        errorMessage.value = error.message || '网络错误，请重试';
                        showError.value = true;
                    } finally {
                        loginLoading.value = false;
                    }
                };

                const handleGitHubLogin = async () => {
                    try {
                        githubLoginLoading.value = true;

                        const response = await fetch('/api/auth/github', {
                            method: 'GET',
                            headers: {
                                'Content-Type': 'application/json',
                            }
                        });

                        if (response.ok) {
                            const data = await response.json();
                            if (data.auth_url) {
                                window.location.href = data.auth_url;
                            } else {
                                throw new Error('未获取到GitHub授权链接');
                            }
                        } else {
                            const errorData = await response.json();
                            throw new Error(errorData.error || 'GitHub登录请求失败');
                        }
                    } catch (error) {
                        console.error('GitHub登录错误:', error);
                        errorMessage.value = error.message || '网络错误，请重试';
                        showError.value = true;
                    } finally {
                        githubLoginLoading.value = false;
                    }
                };

                const handleImageError = (event) => {
                    event.target.style.display = 'none';
                };

                // 检查是否已登录
                onMounted(async () => {
                    // 检查URL参数中是否有错误信息
                    const urlParams = new URLSearchParams(window.location.search);
                    const errorParam = urlParams.get('error');
                    if (errorParam) {
                        errorMessage.value = decodeURIComponent(errorParam);
                        showError.value = true;
                        // 清除URL中的错误参数
                        window.history.replaceState({}, document.title, window.location.pathname);
                    }

                    // 初始化运行时间计算
                    calculateRuntime();

                    // 每秒更新运行时间
                    setInterval(calculateRuntime, 1000);
                    // 使用统一的认证工具检查登录状态
                    const { isLoggedIn, authenticatedFetch } = window.AuthUtils;

                    if (isLoggedIn()) {
                        try {
                            const response = await authenticatedFetch('/api/user');
                            if (response.ok) {
                                // 已登录，跳转到首页
                                console.log('用户已登录，跳转到首页');
                                window.location.href = '/';
                                return;
                            }
                        } catch (error) {
                            // token无效，authenticatedFetch已经清除了token
                            console.log('Token无效，显示登录页面');
                        }
                    }

                    console.log('用户未登录，显示登录页面');

                    // 初始化Socket.IO连接（用于接收统计数据和被统计为在线用户）
                    if (typeof io !== 'undefined') {
                        const socket = io();

                        socket.on('connect', () => {
                            console.log('Socket.IO连接成功 - 已被统计为在线用户');
                        });

                        // 监听统计数据更新
                        socket.on('statistics', (data) => {
                            console.log('收到统计数据更新:', data);
                            siteStats.onlineUsers = data.onlineUsers || 0;
                            siteStats.users.total = data.users.total || 0;
                            siteStats.users.todayNew = data.users.todayNew || 0;
                            siteStats.users.yesterdayNew = data.users.yesterdayNew || 0;
                        });

                        // 定期发送心跳（保持连接活跃）
                        setInterval(() => {
                            if (socket && socket.connected) {
                                socket.emit('heartbeat');
                            }
                        }, 30000); // 每30秒发送一次心跳
                    }

                    // 加载初始统计数据
                    try {
                        const response = await fetch('/api/statistics', {
                            method: 'GET',
                            credentials: 'include', // 包含Cookie
                            headers: {
                                'Content-Type': 'application/json',
                            }
                        });

                        if (response.ok) {
                            const result = await response.json();
                            if (result.success) {
                                siteStats.users.total = result.data.users.total || 0;
                                siteStats.users.todayNew = result.data.users.todayNew || 0;
                                siteStats.users.yesterdayNew = result.data.users.yesterdayNew || 0;
                            }
                        } else {
                            console.log('统计数据接口返回错误:', response.status);
                            // 设置默认值
                            siteStats.onlineUsers = 1;
                            siteStats.users.total = 100;
                            siteStats.users.todayNew = 5;
                            siteStats.users.yesterdayNew = 8;
                        }
                    } catch (error) {
                        console.error('加载统计数据失败:', error);
                        // 设置默认值
                        siteStats.onlineUsers = 1;
                        siteStats.users.total = 100;
                        siteStats.users.todayNew = 5;
                        siteStats.users.yesterdayNew = 8;
                    }
                });

                return {
                    loginLoading,
                    githubLoginLoading,
                    showError,
                    errorMessage,
                    siteStats,
                    runtime,
                    handleLinuxDoLogin,
                    handleGitHubLogin,
                    handleImageError,
                    calculateRuntime,
                    // 图标组件
                    User,
                    UserFilled,
                    Plus,
                    Star
                };
            }
        }).use(ElementPlus).mount('#app');
    </script>
</body>
</html>
