<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>外部接口测试 - AugmentAPI</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>🧸</text></svg>">

    <!-- 引入Element Plus CSS - 本地资源 -->
    <link rel="stylesheet" href="/lib/element-plus/index.min.css">

    <!-- 引入Vue 3 - 本地资源 -->
    <script src="/lib/vue/vue.global.min.js"></script>
    <!-- 引入Element Plus - 本地资源 -->
    <script src="/lib/element-plus/index.full.min.js"></script>
    <!-- 引入Element Plus图标 - 本地资源 -->
    <script src="/lib/element-plus/icons.iife.min.js"></script>
    <!-- 引入Socket.IO客户端 - 本地资源 -->
    <script src="/lib/socket.io/socket.io.min.js"></script>

    <!-- 认证工具函数 -->
    <script src="/js/auth-utils.js"></script>
    <!-- 静默控制台（生产环境隐藏console输出；加 ?debug=1 显示） -->
    <script src="/js/quiet-console.js"></script>

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: #f5f7fa;
        }

        .layout-container {
            height: 100vh;
            display: flex;
        }

        .sidebar {
            background: #fff;
            box-shadow: 2px 0 6px rgba(0,21,41,.35);
            transition: width 0.3s;
            overflow: hidden;
            position: relative;
        }

        .sidebar.collapsed {
            width: 64px !important;
        }

        .sidebar.collapsed .user-info {
            justify-content: center;
            padding: 0;
        }

        .sidebar.collapsed .user-details {
            display: none;
        }

        .sidebar.collapsed .user-avatar {
            margin-right: 0;
            flex-shrink: 0;
        }

        .sidebar.collapsed .el-menu-item span,
        .sidebar.collapsed .el-sub-menu .el-sub-menu__title span {
            display: none;
        }

        .sidebar.collapsed .el-sub-menu .el-sub-menu__icon-arrow {
            display: none;
        }

        .sidebar.expanded {
            width: 250px !important;
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .content-header {
            background: #fff;
            padding: 16px 24px;
            border-bottom: 1px solid #e8eaec;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .content-body {
            flex: 1;
            padding: 24px;
            overflow-y: auto;
            background: #f5f7fa;
        }

        .user-info {
            display: flex;
            align-items: center;
            padding: 16px;
            border-bottom: 1px solid #e8eaec;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin-right: 12px;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #409eff;
            color: white;
            font-weight: bold;
        }

        .user-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        /* 当有头像图片时，移除背景色（兼容不支持 :has 的浏览器） */
        .user-avatar.has-image {
            background: transparent;
        }

        /* 公告铃铛按钮样式 */
        .bell-button {
            position: relative;
            padding: 8px 12px !important;
            border-radius: 50% !important;
            font-size: 18px !important;
            transition: all 0.3s ease !important;
            border: none !important;
        }

        .bell-button:hover {
            background-color: rgba(64, 158, 255, 0.1) !important;
            transform: scale(1.1);
        }

        .has-unread {
            position: relative;
            animation: bell-shake 2s infinite;
            color: #409eff !important;
        }

        .has-unread::after {
            content: '';
            position: absolute;
            top: 2px;
            right: 2px;
            width: 8px;
            height: 8px;
            background: #f56c6c;
            border-radius: 50%;
            border: 2px solid #fff;
            z-index: 1;
        }

        @keyframes bell-shake {
            0%, 50%, 100% { transform: rotate(0deg); }
            10%, 30% { transform: rotate(-10deg); }
            20%, 40% { transform: rotate(10deg); }
        }

        .user-details {
            flex: 1;
            min-width: 0;
        }

        .user-name {
            font-weight: 600;
            color: #303133;
            font-size: 14px;
            margin-bottom: 4px;
            display: flex;
            align-items: center;
        }

        .user-email {
            color: #909399;
            font-size: 12px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .trust-level {
            margin-left: 8px;
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 10px;
            font-weight: 600;
        }

        .trust-level-0 { background: #f4f4f5; color: #909399; }
        .trust-level-1 { background: #ecf5ff; color: #409eff; }
        .trust-level-2 { background: #f0f9ff; color: #67c23a; }
        .trust-level-3 { background: #fdf6ec; color: #e6a23c; }
        .trust-level-4 { background: #fef0f0; color: #f56c6c; }

        .sidebar-toggle {
            position: absolute;
            top: 16px;
            right: -12px;
            width: 24px;
            height: 24px;
            background: #409eff;
            border: none;
            border-radius: 50%;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            z-index: 1000;
        }

        .sidebar-toggle:hover {
            background: #337ecc;
        }

        .test-card {
            background: #fff;
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 24px;
        }

        .test-title {
            font-size: 1.5em;
            color: #303133;
            margin-bottom: 24px;
            text-align: center;
            font-weight: 600;
        }

        .test-step {
            margin-bottom: 24px;
            padding: 20px;
            border: 2px solid #e8eaec;
            border-radius: 8px;
            background: #fafbfc;
            position: relative;
        }

        .test-step.active {
            border-color: #409eff;
            background: #f0f9ff;
        }

        .test-step.completed {
            border-color: #67c23a;
            background: #f0f9ff;
        }

        .step-number {
            position: absolute;
            top: -12px;
            left: 20px;
            background: #409eff;
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 14px;
        }

        .test-step.completed .step-number {
            background: #67c23a;
        }

        .test-step h3 {
            color: #303133;
            margin-bottom: 12px;
            font-size: 1.2em;
        }

        .test-step p {
            color: #606266;
            margin-bottom: 16px;
            line-height: 1.6;
        }

        .step-content {
            margin-top: 16px;
        }

        .url-display {
            background: #f5f7fa;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            padding: 12px;
            font-family: 'Courier New', monospace;
            word-break: break-all;
            margin: 12px 0;
        }

        .result-display {
            margin-top: 16px;
        }

        /* 禁止textarea调整大小 */
        .el-textarea__inner {
            resize: none !important;
        }

        /* 设置Token输入框样式 - 单行输入框 */
        .test-step:first-of-type .el-input__inner {
            background-color: #f5f7fa !important;
            cursor: default !important;
        }

        /* 设置授权响应输入框最小高度 */
        .test-step:nth-of-type(3) .el-textarea__inner {
            min-height: 100px !important;
            height: 100px !important;
        }

        /* 移动端适配 */
        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                left: 0;
                top: 0;
                height: 100vh;
                z-index: 1000;
                transform: translateX(-100%);
                transition: transform 0.3s;
            }

            .sidebar.mobile-open {
                transform: translateX(0);
            }

            .main-content {
                width: 100%;
            }

            .content-body {
                padding: 16px;
            }
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="layout-container">
            <!-- 侧边栏 -->
            <div class="sidebar" :class="{
                collapsed: isCollapsed,
                expanded: !isCollapsed,
                'mobile-open': mobileMenuOpen
            }">
                <button class="sidebar-toggle" @click="toggleSidebar">
                    {{ isCollapsed ? '→' : '←' }}
                </button>

                <!-- 用户信息 -->
                <div class="user-info" v-if="currentUser">
                    <div class="user-avatar" :class="{ 'has-image': !!(currentUser && currentUser.linuxdo_avatar) }">
                        <img v-if="currentUser.linuxdo_avatar" :src="currentUser.linuxdo_avatar" :alt="currentUser.linuxdo_username">
                        <span v-else>{{ currentUser.email ? currentUser.email.charAt(0).toUpperCase() : '?' }}</span>
                    </div>
                    <div class="user-details" v-show="!isCollapsed">
                        <div class="user-name">
                            {{ currentUser.linuxdo_username || currentUser.email.split('@')[0] }}
                            <span v-if="currentUser.linuxdo_trust_level !== null"
                                  :class="`trust-level trust-level-${currentUser.linuxdo_trust_level}`">
                                {{ getTrustLevelName(currentUser.linuxdo_trust_level) }}
                            </span>
                        </div>
                        <div class="user-email">{{ currentUser.email }}</div>
                    </div>
                </div>

                <!-- 导航菜单 -->
                <el-menu
                    :default-active="activeMenu"
                    :collapse="isCollapsed"
                    :unique-opened="true"
                    background-color="#fff"
                    text-color="#303133"
                    active-text-color="#409eff">

                    <el-menu-item index="home" @click="navigateTo('/')">
                        <el-icon><i class="el-icon-house"></i></el-icon>
                        <span>首页</span>
                    </el-menu-item>

                    <el-menu-item index="profile" @click="navigateTo('/profile')">
                        <el-icon><i class="el-icon-user"></i></el-icon>
                        <span>个人中心</span>
                    </el-menu-item>

                    <el-menu-item index="chat" @click="navigateTo('/chat')">
                        <el-icon><i class="el-icon-chat-dot-round"></i></el-icon>
                        <span>实时聊天</span>
                    </el-menu-item>

                    <el-sub-menu index="api">
                        <template #title>
                            <el-icon><i class="el-icon-document"></i></el-icon>
                            <span>API管理</span>
                        </template>
                        <el-menu-item index="api-docs" @click="navigateTo('/api-docs')">
                            <el-icon><i class="el-icon-reading"></i></el-icon>
                            <span>API文档</span>
                        </el-menu-item>
                        <el-menu-item index="external-test" @click="navigateTo('/external-test')">
                            <el-icon><i class="el-icon-connection"></i></el-icon>
                            <span>外部接口测试</span>
                        </el-menu-item>
                    </el-sub-menu>

                        <el-menu-item index="docs" @click="navigateTo('/docs')">
                            <el-icon><i class="el-icon-folder"></i></el-icon>
                            <span>文档中心</span>
                        </el-menu-item>


                    <!-- 管理后台菜单项（仅管理员可见） -->
                    <el-menu-item v-if="currentUser && currentUser.isAdmin" index="admin" @click="navigateTo('/admin')">
                        <el-icon><i class="el-icon-setting"></i></el-icon>
                        <span>管理后台</span>
                    </el-menu-item>

                    <el-menu-item index="logout" @click="handleLogout">
                        <el-icon><i class="el-icon-switch-button"></i></el-icon>
                        <span>退出登录</span>
                    </el-menu-item>
                </el-menu>
            </div>

            <!-- 主内容区 -->
            <div class="main-content">
                <!-- 内容头部 -->
                <div class="content-header">
                    <div>
                        <el-button v-if="isMobile" @click="toggleMobileSidebar" icon="el-icon-menu" circle></el-button>
                        <span style="font-size: 18px; font-weight: 600; margin-left: 8px;">外部接口测试</span>
                    </div>
                    <div>
                        <el-button @click="showAnnouncementDialog" :class="{ 'has-unread': hasUnreadAnnouncements, 'bell-button': true }" text>
                            🔔
                        </el-button>
                    </div>
                </div>

                <!-- 内容主体 -->
                <div class="content-body">
                    <!-- 外部接口测试工具 -->
                    <div class="test-card">
                        <div class="test-title">
                            🧪 外部接口完整测试
                        </div>

                        <!-- 测试说明 -->
                        <el-alert
                            title="📋 测试说明"
                            type="info"
                            :closable="false"
                            show-icon>
                            <p>这个页面将完整测试外部API接口的整个流程：获取授权链接 → 完成授权 → 验证结果</p>
                        </el-alert>

                        <!-- 步骤1：配置Token -->
                        <div class="test-step" :class="{ active: currentStep === 1, completed: currentStep > 1 }">
                            <div class="step-number">1</div>
                            <h3>配置用户Token</h3>
                            <p>用户Token（从个人中心获取）：</p>
                            <div class="step-content">
                                <el-input
                                    v-model="userToken"
                                    readonly>
                                </el-input>
                                <div style="margin-top: 12px;">
                                    <el-button
                                        type="primary"
                                        @click="validateToken"
                                        :loading="step1Loading"
                                        :disabled="!userToken.trim()">
                                        验证Token
                                    </el-button>
                                </div>
                                <div v-if="step1Result" class="result-display">
                                    <el-alert
                                        :title="step1Result.success ? '✅ Token验证成功' : '❌ Token验证失败'"
                                        :type="step1Result.success ? 'success' : 'error'"
                                        :closable="false"
                                        show-icon>
                                        <p>{{ step1Result.message }}</p>
                                    </el-alert>
                                </div>
                            </div>
                        </div>

                        <!-- 步骤2：获取授权链接 -->
                        <div class="test-step" :class="{ active: currentStep === 2, completed: currentStep > 2 }">
                            <div class="step-number">2</div>
                            <h3>获取授权链接</h3>
                            <p>使用外部API获取OAuth授权链接</p>
                            <div class="step-content">
                                <el-button
                                    type="primary"
                                    @click="getAuthUrl"
                                    :loading="step2Loading"
                                    :disabled="currentStep < 2">
                                    获取授权链接
                                </el-button>
                                <div v-if="step2Result" class="result-display">
                                    <el-alert
                                        :title="step2Result.success ? '✅ 授权链接获取成功' : '❌ 获取失败'"
                                        :type="step2Result.success ? 'success' : 'error'"
                                        :closable="false"
                                        show-icon>
                                        <p>{{ step2Result.message }}</p>
                                    </el-alert>
                                </div>
                                <div v-if="authUrl" class="result-display">
                                    <p><strong>授权链接：</strong></p>
                                    <div class="url-display">{{ authUrl }}</div>
                                    <el-button type="success" @click="openAuthUrl">
                                        🚀 打开授权页面
                                    </el-button>
                                </div>
                            </div>
                        </div>

                        <!-- 步骤3：完成授权 -->
                        <div class="test-step" :class="{ active: currentStep === 3, completed: currentStep > 3 }">
                            <div class="step-number">3</div>
                            <h3>完成授权</h3>
                            <p>在授权页面完成授权后，将返回的授权响应粘贴到下面：</p>
                            <div class="step-content">
                                <el-input
                                    v-model="authResponse"
                                    placeholder='粘贴授权响应，格式如：{"code":"xxx","state":"xxx","tenant_url":"xxx"}'
                                    type="textarea"
                                    :rows="4">
                                </el-input>
                                <div style="margin-top: 12px;">
                                    <el-button
                                        type="primary"
                                        @click="completeAuth"
                                        :loading="step3Loading"
                                        :disabled="currentStep < 3 || !authResponse.trim()">
                                        完成授权
                                    </el-button>
                                </div>
                                <div v-if="step3Result" class="result-display">
                                    <el-alert
                                        :title="step3Result.success ? '✅ 授权完成' : '❌ 授权失败'"
                                        :type="step3Result.success ? 'success' : 'error'"
                                        :closable="false"
                                        show-icon>
                                        <p>{{ step3Result.message }}</p>
                                    </el-alert>
                                </div>
                            </div>
                        </div>

                        <!-- 步骤4：验证结果 -->
                        <div class="test-step" :class="{ active: currentStep === 4, completed: currentStep > 4 }">
                            <div class="step-number">4</div>
                            <h3>验证结果</h3>
                            <p>验证授权是否成功完成，并显示获取的访问令牌信息</p>
                            <div class="step-content">
                                <el-button
                                    type="primary"
                                    @click="verifyResult"
                                    :loading="step4Loading"
                                    :disabled="currentStep < 4">
                                    验证结果
                                </el-button>
                                <div v-if="step4Result" class="result-display">
                                    <el-alert
                                        :title="step4Result.success ? '✅ 验证成功' : '❌ 验证失败'"
                                        :type="step4Result.success ? 'success' : 'error'"
                                        :closable="false"
                                        show-icon>
                                        <p>{{ step4Result.message }}</p>
                                    </el-alert>
                                    <div v-if="step4Result.success && step4Result.data">
                                        <div style="margin-top: 16px;">
                                            <p><strong>访问令牌：</strong></p>
                                            <div class="url-display">{{ step4Result.data.token }}</div>
                                            <p><strong>租户URL：</strong></p>
                                            <div class="url-display">{{ step4Result.data.tenant_url }}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 系统公告弹窗 -->
        <el-dialog
            v-model="announcementDialogVisible"
            width="500px"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
            :show-close="true"
            class="announcement-dialog">

            <template #header>
                <span style="font-size: 18px; font-weight: 600;">系统公告</span>
            </template>

            <div v-if="currentAnnouncement" class="announcement-dialog-content">
                <div class="announcement-header">
                    <div class="announcement-title">
                        <el-tag v-if="currentAnnouncement.is_pinned" type="warning" size="small" style="margin-right: 8px;">置顶</el-tag>
                        <el-tag :type="getAnnouncementTypeColor(currentAnnouncement.type)" size="small" style="margin-right: 8px;">
                            {{ getAnnouncementTypeText(currentAnnouncement.type) }}
                        </el-tag>
                        <span style="font-size: 16px; font-weight: 600;">{{ currentAnnouncement.title }}</span>
                    </div>
                    <div class="announcement-meta" style="margin-top: 8px; color: #909399; font-size: 12px;">
                        发布时间：{{ formatDate(currentAnnouncement.created_at) }}
                        <span v-if="currentAnnouncement.author_name" style="margin-left: 16px;">
                            发布人：{{ currentAnnouncement.author_name }}
                        </span>
                    </div>
                    <el-divider />
                </div>
                <div class="announcement-content" style="line-height: 1.8; white-space: pre-wrap;">
                    {{ currentAnnouncement.content }}
                </div>
            </div>

            <template #footer>
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <div style="color: #909399; font-size: 12px;">
                        {{ currentAnnouncementIndex + 1 }} / {{ announcements.length }}
                    </div>
                    <div>
                        <el-button type="primary" @click="announcementDialogVisible = false">
                            知道了
                        </el-button>
                    </div>
                </div>
            </template>
        </el-dialog>

        <!-- 移动端遮罩 -->
        <div v-if="isMobile && mobileMenuOpen"
             style="position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.5); z-index: 999;"
             @click="closeMobileSidebar"></div>
    </div>

    <!-- CDN资源已由服务端自动注入到head部分 -->

    <script>
        const { createApp, ref, reactive, onMounted, computed } = Vue;

        createApp({
            setup() {
                // 响应式数据
                const isCollapsed = ref(false);
                const mobileMenuOpen = ref(false);
                const currentUser = ref(null);
                const activeMenu = ref('external-test');

                // 公告数据
                const announcements = ref([]);
                const announcementDialogVisible = ref(false);
                const currentAnnouncement = ref(null);
                const currentAnnouncementIndex = ref(0);
                const hasUnreadAnnouncements = ref(false);

                // 测试相关数据
                const currentStep = ref(1);
                const userToken = ref('');
                const authUrl = ref('');
                const authResponse = ref('');
                const accessToken = ref('');
                const tenantUrl = ref('');

                // 各步骤状态
                const step1Loading = ref(false);
                const step2Loading = ref(false);
                const step3Loading = ref(false);
                const step4Loading = ref(false);

                const step1Result = ref(null);
                const step2Result = ref(null);
                const step3Result = ref(null);
                const step4Result = ref(null);

                // 计算属性
                const isMobile = computed(() => {
                    return window.innerWidth <= 768;
                });

                // 基础方法
                const toggleSidebar = () => {
                    isCollapsed.value = !isCollapsed.value;
                };

                const toggleMobileSidebar = () => {
                    mobileMenuOpen.value = !mobileMenuOpen.value;
                };

                const closeMobileSidebar = () => {
                    mobileMenuOpen.value = false;
                };

                const navigateTo = (path) => {
                    window.location.href = path;
                };

                const refreshPage = () => {
                    window.location.reload();
                };

                const handleLogout = async () => {
                    try {
                        const response = await fetch('/api/logout', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            }
                        });

                        if (response.ok) {
                            localStorage.removeItem('auth_token');
                            window.location.href = '/login';
                        } else {
                            ElementPlus.ElMessage.error('登出失败，请重试');
                        }
                    } catch (error) {
                        console.error('登出错误:', error);
                        ElementPlus.ElMessage.error('网络错误，请重试');
                    }
                };

                const getTrustLevelName = (level) => {
                    const levelNames = {
                        0: '新用户',
                        1: '基础',
                        2: '成员',
                        3: '常规',
                        4: '领导'
                    };
                    return levelNames[level] || '未知';
                };

                const checkLoginStatus = async () => {
                    try {
                        const response = await fetch('/api/user');
                        if (response.ok) {
                            const result = await response.json();
                            currentUser.value = result.user;
                            return true;
                        } else if (response.status === 401) {
                            window.location.href = '/login';
                            return false;
                        }
                    } catch (error) {
                        console.error('检查登录状态失败:', error);
                        return false;
                    }
                };

                // 测试相关方法
                const validateToken = async () => {
                    if (!userToken.value.trim()) {
                        ElementPlus.ElMessage.warning('请输入用户Token');
                        return;
                    }

                    step1Loading.value = true;
                    step1Result.value = null;

                    try {
                        const response = await fetch('/api/external/auth-url', {
                            method: 'GET',
                            headers: {
                                'Authorization': `Bearer ${userToken.value}`,
                                'Content-Type': 'application/json'
                            }
                        });

                        if (response.ok) {
                            step1Result.value = {
                                success: true,
                                message: 'Token验证成功，可以进行下一步'
                            };
                            currentStep.value = 2;
                            ElementPlus.ElMessage.success('Token验证成功');
                        } else {
                            const errorData = await response.json();
                            step1Result.value = {
                                success: false,
                                message: errorData.error || 'Token验证失败'
                            };
                            ElementPlus.ElMessage.error('Token验证失败');
                        }
                    } catch (error) {
                        step1Result.value = {
                            success: false,
                            message: '网络错误: ' + error.message
                        };
                        ElementPlus.ElMessage.error('网络错误');
                    } finally {
                        step1Loading.value = false;
                    }
                };

                const getAuthUrl = async () => {
                    step2Loading.value = true;
                    step2Result.value = null;

                    try {
                        const response = await fetch('/api/external/auth-url', {
                            method: 'GET',
                            headers: {
                                'Authorization': `Bearer ${userToken.value}`,
                                'Content-Type': 'application/json'
                            }
                        });

                        if (response.ok) {
                            const data = await response.json();
                            authUrl.value = data.authorize_url;
                            step2Result.value = {
                                success: true,
                                message: '授权链接获取成功'
                            };
                            currentStep.value = 3;
                            ElementPlus.ElMessage.success('授权链接获取成功');
                        } else {
                            const errorData = await response.json();
                            step2Result.value = {
                                success: false,
                                message: errorData.error || '获取授权链接失败'
                            };
                            ElementPlus.ElMessage.error('获取授权链接失败');
                        }
                    } catch (error) {
                        step2Result.value = {
                            success: false,
                            message: '网络错误: ' + error.message
                        };
                        ElementPlus.ElMessage.error('网络错误');
                    } finally {
                        step2Loading.value = false;
                    }
                };

                const openAuthUrl = () => {
                    if (authUrl.value) {
                        window.open(authUrl.value, '_blank');
                    }
                };

                const completeAuth = async () => {
                    if (!authResponse.value.trim()) {
                        ElementPlus.ElMessage.warning('请输入授权响应');
                        return;
                    }

                    step3Loading.value = true;
                    step3Result.value = null;

                    try {
                        // 验证JSON格式
                        const authData = JSON.parse(authResponse.value);

                        const response = await fetch('/api/external/complete-auth', {
                            method: 'POST',
                            headers: {
                                'Authorization': `Bearer ${userToken.value}`,
                                'Content-Type': 'application/json'
                            },
                            body: authResponse.value
                        });

                        if (response.ok) {
                            const result = await response.json();
                            accessToken.value = result.token;
                            tenantUrl.value = authData.tenant_url;
                            step3Result.value = {
                                success: true,
                                message: '授权完成成功'
                            };
                            currentStep.value = 4;
                            ElementPlus.ElMessage.success('授权完成成功');
                        } else {
                            const errorData = await response.json();
                            step3Result.value = {
                                success: false,
                                message: errorData.error || '授权完成失败'
                            };
                            ElementPlus.ElMessage.error('授权完成失败');
                        }
                    } catch (error) {
                        step3Result.value = {
                            success: false,
                            message: '数据格式错误或网络错误: ' + error.message
                        };
                        ElementPlus.ElMessage.error('数据格式错误或网络错误');
                    } finally {
                        step3Loading.value = false;
                    }
                };

                const verifyResult = async () => {
                    step4Loading.value = true;
                    step4Result.value = null;

                    try {
                        // 这里可以调用验证接口，或者直接显示结果
                        if (accessToken.value && tenantUrl.value) {
                            step4Result.value = {
                                success: true,
                                message: '验证成功！授权流程已完成',
                                data: {
                                    token: accessToken.value,
                                    tenant_url: tenantUrl.value
                                }
                            };
                            ElementPlus.ElMessage.success('验证成功！');
                        } else {
                            step4Result.value = {
                                success: false,
                                message: '缺少必要的授权信息'
                            };
                            ElementPlus.ElMessage.error('验证失败');
                        }
                    } catch (error) {
                        step4Result.value = {
                            success: false,
                            message: '验证过程出错: ' + error.message
                        };
                        ElementPlus.ElMessage.error('验证失败');
                    } finally {
                        step4Loading.value = false;
                    }
                };

                // 初始化Socket.IO连接（用于在线人数统计）
                const initSocket = () => {
                    if (typeof io === 'undefined') {
                        console.warn('Socket.IO未加载');
                        return;
                    }

                    const socket = io();

                    socket.on('connect', () => {
                        console.log('Socket.IO连接成功 - 已被统计为在线用户');

                        // 如果用户已登录，发送认证信息（可选）
                        if (currentUser.value) {
                            socket.emit('authenticate', {
                                userEmail: currentUser.value.email
                            });
                        }
                    });

                    socket.on('disconnect', () => {
                        console.log('Socket.IO连接断开');
                    });

                    // 定期发送心跳（保持连接活跃）
                    setInterval(() => {
                        if (socket && socket.connected) {
                            socket.emit('heartbeat');
                        }
                    }, 30000); // 每30秒发送一次心跳
                };

                // 公告相关方法
                const showAnnouncementDialog = () => {
                    if (announcements.value.length > 0) {
                        currentAnnouncementIndex.value = 0;
                        currentAnnouncement.value = announcements.value[0];
                        announcementDialogVisible.value = true;
                        hasUnreadAnnouncements.value = false;
                    } else {
                        ElementPlus.ElMessage.info('暂无公告');
                    }
                };

                const loadAnnouncements = async () => {
                    try {
                        const response = await fetch('/api/announcements/active');
                        if (response.ok) {
                            const data = await response.json();
                            announcements.value = data.data || [];
                            hasUnreadAnnouncements.value = announcements.value.length > 0;
                        }
                    } catch (error) {
                        console.error('加载公告失败:', error);
                    }
                };

                // 生命周期
                onMounted(async () => {
                    await checkLoginStatus();
                    await loadAnnouncements();

                    // 初始化Socket.IO连接
                    initSocket();

                    // 自动使用当前用户Token
                    if (currentUser.value && currentUser.value.auth_token) {
                        userToken.value = currentUser.value.auth_token;
                    }

                    // 监听窗口大小变化
                    window.addEventListener('resize', () => {
                        if (!isMobile.value && mobileMenuOpen.value) {
                            mobileMenuOpen.value = false;
                        }
                    });
                });

                return {
                    isCollapsed,
                    mobileMenuOpen,
                    currentUser,
                    activeMenu,
                    isMobile,
                    currentStep,
                    userToken,
                    authUrl,
                    authResponse,
                    accessToken,
                    tenantUrl,
                    step1Loading,
                    step2Loading,
                    step3Loading,
                    step4Loading,
                    step1Result,
                    step2Result,
                    step3Result,
                    step4Result,
                    toggleSidebar,
                    toggleMobileSidebar,
                    closeMobileSidebar,
                    navigateTo,
                    refreshPage,
                    handleLogout,
                    getTrustLevelName,
                    validateToken,
                    getAuthUrl,
                    openAuthUrl,
                    completeAuth,
                    verifyResult,
                    // 公告相关
                    announcements,
                    announcementDialogVisible,
                    currentAnnouncement,
                    currentAnnouncementIndex,
                    hasUnreadAnnouncements,
                    showAnnouncementDialog,
                    loadAnnouncements,
                    getAnnouncementTypeColor: (type) => {
                        const colors = { info: '', warning: 'warning', success: 'success', error: 'danger' };
                        return colors[type] || '';
                    },
                    getAnnouncementTypeText: (type) => {
                        const texts = { info: '信息', warning: '警告', success: '成功', error: '错误' };
                        return texts[type] || type;
                    },
                    formatDate: (dateString) => new Date(dateString).toLocaleDateString('zh-CN')
                };
            }
        }).use(ElementPlus).mount('#app');
    </script>
</body>
</html>
