import Cookies from 'js-cookie'
import axios from 'axios'
import type { User, ApiResponse } from '@/types'

// Cookie配置
const COOKIE_OPTIONS = {
  expires: 1, // 1天
  sameSite: 'lax' as const,
  secure: process.env.NODE_ENV === 'production'
}

// Token管理
export const authTokens = {
  get: (): string | null => {
    return Cookies.get('auth_token') || null
  },
  
  set: (token: string): void => {
    Cookies.set('auth_token', token, COOKIE_OPTIONS)
  },
  
  remove: (): void => {
    Cookies.remove('auth_token')
  }
}

// 检查是否已登录
export const isAuthenticated = (): boolean => {
  return !!authTokens.get()
}

// 创建带认证的axios实例
export const createAuthenticatedAxios = () => {
  const instance = axios.create({
    baseURL: '/api',
    timeout: 10000,
    headers: {
      'Content-Type': 'application/json'
    }
  })

  // 请求拦截器 - 添加token
  instance.interceptors.request.use(
    (config) => {
      const token = authTokens.get()
      if (token) {
        config.headers.Authorization = `Bearer ${token}`
      }
      return config
    },
    (error) => {
      return Promise.reject(error)
    }
  )

  // 响应拦截器 - 处理token过期
  instance.interceptors.response.use(
    (response) => response,
    async (error) => {
      if (error.response?.status === 401) {
        // Token过期，尝试刷新
        try {
          const refreshResponse = await axios.post('/api/refresh-token', {}, {
            withCredentials: true
          })
          
          if (refreshResponse.data.success) {
            const newToken = refreshResponse.data.token
            authTokens.set(newToken)
            
            // 重试原请求
            error.config.headers.Authorization = `Bearer ${newToken}`
            return instance.request(error.config)
          }
        } catch (refreshError) {
          // 刷新失败，清除token并跳转登录
          authTokens.remove()
          window.location.href = '/login'
        }
      }
      return Promise.reject(error)
    }
  )

  return instance
}

// 默认的认证axios实例
export const authApi = createAuthenticatedAxios()

// 获取当前用户信息
export const getCurrentUser = async (): Promise<User | null> => {
  try {
    const response = await authApi.get<ApiResponse<{ user: User }>>('/user')
    return response.data.data?.user || null
  } catch (error) {
    console.error('获取用户信息失败:', error)
    return null
  }
}

// 登出
export const logout = async (): Promise<void> => {
  try {
    await authApi.post('/logout')
  } catch (error) {
    console.error('登出请求失败:', error)
  } finally {
    authTokens.remove()
    window.location.href = '/login'
  }
}

// 获取信任等级名称
export const getTrustLevelName = (level: number): string => {
  const levelNames: Record<number, string> = {
    0: '新用户',
    1: '基础',
    2: '成员', 
    3: '常规',
    4: '领导'
  }
  return levelNames[level] || '未知'
}
