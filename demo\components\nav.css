/* 统一导航栏样式 */
.header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 15px 0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.header-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    font-size: 1.5em;
    font-weight: bold;
    color: #667eea;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 8px;
}

.logo:hover {
    color: #5a67d8;
}

.nav-buttons {
    display: flex;
    align-items: center;
    gap: 15px;
}

.nav-btn {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 6px;
    transition: all 0.2s ease;
}

.nav-btn.primary {
    background: #667eea;
    color: white;
}

.nav-btn.primary:hover {
    background: #5a67d8;
    transform: translateY(-1px);
}

.nav-btn.secondary {
    background: #f7fafc;
    color: #4a5568;
    border: 1px solid #e2e8f0;
}

.nav-btn.secondary:hover {
    background: #edf2f7;
    border-color: #cbd5e0;
}

.user-info {
    color: #4a5568;
    font-size: 14px;
    margin-right: 15px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: #667eea;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 14px;
}

/* 当有头像图片时，移除背景色 */
.user-avatar:has(img) {
    background: transparent;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 15px;
        padding: 0 15px;
    }
    
    .nav-buttons {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .user-info {
        margin-right: 0;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .nav-btn {
        padding: 6px 12px;
        font-size: 13px;
    }
    
    .logo {
        font-size: 1.3em;
    }
}
