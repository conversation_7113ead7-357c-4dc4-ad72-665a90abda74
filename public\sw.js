// Service Worker for AugmentAPI PWA
const CACHE_NAME = 'augmentapi-v1.0.0'
const STATIC_CACHE = 'augmentapi-static-v1.0.0'
const DYNAMIC_CACHE = 'augmentapi-dynamic-v1.0.0'

// 需要缓存的静态资源
const STATIC_ASSETS = [
  '/',
  '/manifest.json',
  '/icons/icon-192x192.png',
  '/icons/icon-512x512.png'
]

// 需要缓存的API路径
const API_CACHE_PATTERNS = [
  /^https:\/\/api\.augmentapi\.com\/v1\//,
  /^\/api\//
]

// 安装事件
self.addEventListener('install', (event) => {
  console.log('Service Worker: Installing...')
  
  event.waitUntil(
    caches.open(STATIC_CACHE)
      .then((cache) => {
        console.log('Service Worker: Caching static assets')
        return cache.addAll(STATIC_ASSETS)
      })
      .then(() => {
        console.log('Service Worker: Installed successfully')
        return self.skipWaiting()
      })
      .catch((error) => {
        console.error('Service Worker: Installation failed', error)
      })
  )
})

// 激活事件
self.addEventListener('activate', (event) => {
  console.log('Service Worker: Activating...')
  
  event.waitUntil(
    caches.keys()
      .then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName !== STATIC_CACHE && cacheName !== DYNAMIC_CACHE) {
              console.log('Service Worker: Deleting old cache', cacheName)
              return caches.delete(cacheName)
            }
          })
        )
      })
      .then(() => {
        console.log('Service Worker: Activated successfully')
        return self.clients.claim()
      })
  )
})

// 获取事件
self.addEventListener('fetch', (event) => {
  const { request } = event
  const url = new URL(request.url)
  
  // 只处理GET请求
  if (request.method !== 'GET') {
    return
  }
  
  // 处理导航请求（页面请求）
  if (request.mode === 'navigate') {
    event.respondWith(
      fetch(request)
        .then((response) => {
          // 如果网络请求成功，返回响应
          return response
        })
        .catch(() => {
          // 如果网络请求失败，返回缓存的首页
          return caches.match('/')
        })
    )
    return
  }
  
  // 处理静态资源
  if (STATIC_ASSETS.some(asset => request.url.includes(asset))) {
    event.respondWith(
      caches.match(request)
        .then((cachedResponse) => {
          if (cachedResponse) {
            return cachedResponse
          }
          
          return fetch(request)
            .then((response) => {
              const responseClone = response.clone()
              caches.open(STATIC_CACHE)
                .then((cache) => {
                  cache.put(request, responseClone)
                })
              return response
            })
        })
    )
    return
  }
  
  // 处理API请求
  if (API_CACHE_PATTERNS.some(pattern => pattern.test(request.url))) {
    event.respondWith(
      fetch(request)
        .then((response) => {
          // 只缓存成功的响应
          if (response.status === 200) {
            const responseClone = response.clone()
            caches.open(DYNAMIC_CACHE)
              .then((cache) => {
                cache.put(request, responseClone)
              })
          }
          return response
        })
        .catch(() => {
          // 如果网络请求失败，尝试返回缓存的响应
          return caches.match(request)
        })
    )
    return
  }
  
  // 处理其他资源（图片、字体等）
  event.respondWith(
    caches.match(request)
      .then((cachedResponse) => {
        if (cachedResponse) {
          return cachedResponse
        }
        
        return fetch(request)
          .then((response) => {
            // 只缓存成功的响应
            if (response.status === 200) {
              const responseClone = response.clone()
              caches.open(DYNAMIC_CACHE)
                .then((cache) => {
                  cache.put(request, responseClone)
                })
            }
            return response
          })
      })
  )
})

// 后台同步
self.addEventListener('sync', (event) => {
  console.log('Service Worker: Background sync', event.tag)
  
  if (event.tag === 'background-sync') {
    event.waitUntil(
      // 执行后台同步任务
      doBackgroundSync()
    )
  }
})

// 推送通知
self.addEventListener('push', (event) => {
  console.log('Service Worker: Push notification received')
  
  const options = {
    body: event.data ? event.data.text() : 'AugmentAPI有新消息',
    icon: '/icons/icon-192x192.png',
    badge: '/icons/badge-72x72.png',
    vibrate: [100, 50, 100],
    data: {
      dateOfArrival: Date.now(),
      primaryKey: 1
    },
    actions: [
      {
        action: 'explore',
        title: '查看详情',
        icon: '/icons/checkmark.png'
      },
      {
        action: 'close',
        title: '关闭',
        icon: '/icons/xmark.png'
      }
    ]
  }
  
  event.waitUntil(
    self.registration.showNotification('AugmentAPI', options)
  )
})

// 通知点击事件
self.addEventListener('notificationclick', (event) => {
  console.log('Service Worker: Notification clicked')
  
  event.notification.close()
  
  if (event.action === 'explore') {
    // 打开应用
    event.waitUntil(
      clients.openWindow('/')
    )
  } else if (event.action === 'close') {
    // 关闭通知
    event.notification.close()
  } else {
    // 默认行为：打开应用
    event.waitUntil(
      clients.openWindow('/')
    )
  }
})

// 后台同步函数
async function doBackgroundSync() {
  try {
    // 这里可以执行后台同步任务
    // 比如同步离线时的操作、更新缓存等
    console.log('Service Worker: Performing background sync')
    
    // 示例：清理过期缓存
    const cacheNames = await caches.keys()
    const expiredCaches = cacheNames.filter(name => 
      name.includes('dynamic') && 
      !name.includes(DYNAMIC_CACHE)
    )
    
    await Promise.all(
      expiredCaches.map(name => caches.delete(name))
    )
    
    console.log('Service Worker: Background sync completed')
  } catch (error) {
    console.error('Service Worker: Background sync failed', error)
  }
}

// 错误处理
self.addEventListener('error', (event) => {
  console.error('Service Worker: Error occurred', event.error)
})

// 未处理的Promise拒绝
self.addEventListener('unhandledrejection', (event) => {
  console.error('Service Worker: Unhandled promise rejection', event.reason)
})
