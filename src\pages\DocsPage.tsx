import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  Book, 
  Search, 
  ChevronRight,
  FileText,
  Video,
  Code,
  HelpCircle,
  Star,
  Clock,
  User,
  Tag,
  ExternalLink
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'

interface DocCategory {
  id: string
  name: string
  description: string
  icon: React.ComponentType<any>
  color: string
  docs: DocItem[]
}

interface DocItem {
  id: string
  title: string
  description: string
  type: 'guide' | 'tutorial' | 'reference' | 'faq'
  difficulty: 'beginner' | 'intermediate' | 'advanced'
  readTime: number
  tags: string[]
  author: string
  updatedAt: string
  content?: string
  popular?: boolean
}

const DocsPage: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [selectedDoc, setSelectedDoc] = useState<DocItem | null>(null)
  const [docCategories, setDocCategories] = useState<DocCategory[]>([])
  const [loading, setLoading] = useState(true)

  // 模拟文档数据
  const mockDocCategories: DocCategory[] = [
    {
      id: 'getting-started',
      name: '快速开始',
      description: '新手入门指南和基础教程',
      icon: Star,
      color: 'text-yellow-600 bg-yellow-50',
      docs: [
        {
          id: 'quick-start',
          title: '快速开始指南',
          description: '5分钟快速上手AugmentAPI，了解基本功能和使用方法',
          type: 'guide',
          difficulty: 'beginner',
          readTime: 5,
          tags: ['入门', '基础'],
          author: 'AugmentAPI团队',
          updatedAt: '2024-01-15',
          popular: true,
          content: `# 快速开始指南

欢迎使用AugmentAPI！本指南将帮助您在5分钟内快速上手。

## 1. 注册账户

首先，您需要注册一个AugmentAPI账户：
- 访问登录页面
- 选择LinuxDo或GitHub登录
- 完成账户验证

## 2. 获取API密钥

登录后，前往个人中心：
1. 点击侧边栏的"个人中心"
2. 切换到"API密钥"标签页
3. 点击"创建密钥"按钮
4. 输入密钥名称并保存

## 3. 发送第一个请求

使用您的API密钥发送请求：

\`\`\`bash
curl -X GET "https://api.augmentapi.com/v1/user/profile" \\
  -H "Authorization: Bearer YOUR_API_KEY"
\`\`\`

## 4. 探索更多功能

- 查看API文档了解所有可用接口
- 使用外部接口测试工具调试API
- 加入实时聊天与其他开发者交流

恭喜！您已经成功开始使用AugmentAPI。`
        },
        {
          id: 'authentication',
          title: '认证和授权',
          description: '了解如何使用API密钥进行身份验证和权限管理',
          type: 'guide',
          difficulty: 'beginner',
          readTime: 8,
          tags: ['认证', '安全'],
          author: 'AugmentAPI团队',
          updatedAt: '2024-01-14',
          content: `# 认证和授权

AugmentAPI使用API密钥进行身份验证。

## API密钥

每个API请求都需要在请求头中包含有效的API密钥：

\`\`\`
Authorization: Bearer YOUR_API_KEY
\`\`\`

## 获取API密钥

1. 登录AugmentAPI控制台
2. 前往个人中心 > API密钥
3. 创建新的API密钥
4. 妥善保管您的密钥

## 权限级别

API密钥具有不同的权限级别：
- **只读**: 只能读取数据
- **读写**: 可以读取和修改数据
- **管理员**: 具有完全访问权限

## 安全最佳实践

- 不要在客户端代码中暴露API密钥
- 定期轮换API密钥
- 为不同环境使用不同的密钥
- 监控API密钥的使用情况`
        }
      ]
    },
    {
      id: 'api-reference',
      name: 'API参考',
      description: '完整的API接口文档和参数说明',
      icon: Code,
      color: 'text-blue-600 bg-blue-50',
      docs: [
        {
          id: 'user-api',
          title: '用户管理API',
          description: '用户注册、登录、资料管理等相关接口',
          type: 'reference',
          difficulty: 'intermediate',
          readTime: 15,
          tags: ['API', '用户'],
          author: 'API团队',
          updatedAt: '2024-01-13',
          content: `# 用户管理API

## 获取用户资料

\`GET /api/users/profile\`

获取当前登录用户的详细资料信息。

### 请求头
\`\`\`
Authorization: Bearer YOUR_API_KEY
Content-Type: application/json
\`\`\`

### 响应示例
\`\`\`json
{
  "success": true,
  "data": {
    "id": "1",
    "email": "<EMAIL>",
    "username": "user123",
    "avatar": "https://example.com/avatar.jpg",
    "created_at": "2024-01-01T00:00:00Z"
  }
}
\`\`\`

## 更新用户资料

\`PUT /api/users/profile\`

更新当前登录用户的资料信息。

### 请求体
\`\`\`json
{
  "username": "newusername",
  "bio": "这是我的个人简介"
}
\`\`\`

### 响应示例
\`\`\`json
{
  "success": true,
  "message": "资料更新成功"
}
\`\`\``
        },
        {
          id: 'chat-api',
          title: '聊天系统API',
          description: '实时聊天、消息管理等相关接口',
          type: 'reference',
          difficulty: 'intermediate',
          readTime: 12,
          tags: ['API', '聊天'],
          author: 'API团队',
          updatedAt: '2024-01-12'
        }
      ]
    },
    {
      id: 'tutorials',
      name: '教程指南',
      description: '详细的使用教程和最佳实践',
      icon: Video,
      color: 'text-green-600 bg-green-50',
      docs: [
        {
          id: 'integration-guide',
          title: '集成指南',
          description: '如何将AugmentAPI集成到您的应用程序中',
          type: 'tutorial',
          difficulty: 'intermediate',
          readTime: 20,
          tags: ['集成', '开发'],
          author: '开发团队',
          updatedAt: '2024-01-11',
          popular: true
        },
        {
          id: 'best-practices',
          title: '最佳实践',
          description: '使用AugmentAPI的最佳实践和性能优化建议',
          type: 'tutorial',
          difficulty: 'advanced',
          readTime: 25,
          tags: ['最佳实践', '性能'],
          author: '架构团队',
          updatedAt: '2024-01-10'
        }
      ]
    },
    {
      id: 'faq',
      name: '常见问题',
      description: '常见问题解答和故障排除',
      icon: HelpCircle,
      color: 'text-purple-600 bg-purple-50',
      docs: [
        {
          id: 'common-issues',
          title: '常见问题解答',
          description: '用户最常遇到的问题和解决方案',
          type: 'faq',
          difficulty: 'beginner',
          readTime: 10,
          tags: ['FAQ', '问题'],
          author: '支持团队',
          updatedAt: '2024-01-09',
          content: `# 常见问题解答

## Q: 如何获取API密钥？

A: 登录后前往个人中心 > API密钥页面，点击"创建密钥"按钮即可创建新的API密钥。

## Q: API有请求频率限制吗？

A: 是的，免费用户每分钟最多可发送100个请求，付费用户根据套餐不同有更高的限制。

## Q: 如何重置密码？

A: 由于我们使用第三方登录（LinuxDo/GitHub），请前往对应平台重置密码。

## Q: 支持哪些编程语言？

A: AugmentAPI是RESTful API，支持所有能发送HTTP请求的编程语言，包括JavaScript、Python、Java、PHP等。

## Q: 如何联系技术支持？

A: 您可以通过以下方式联系我们：
- 实时聊天功能
- 发送邮件至 <EMAIL>
- 在GitHub仓库提交Issue`
        },
        {
          id: 'troubleshooting',
          title: '故障排除',
          description: '常见错误代码和解决方法',
          type: 'faq',
          difficulty: 'intermediate',
          readTime: 15,
          tags: ['故障排除', '错误'],
          author: '支持团队',
          updatedAt: '2024-01-08'
        }
      ]
    }
  ]

  // 加载文档数据
  useEffect(() => {
    const loadDocs = async () => {
      setLoading(true)
      try {
        // TODO: 调用API获取文档数据
        await new Promise(resolve => setTimeout(resolve, 1000))
        setDocCategories(mockDocCategories)
      } catch (error) {
        console.error('加载文档失败:', error)
      } finally {
        setLoading(false)
      }
    }

    loadDocs()
  }, [])

  // 过滤文档
  const filteredDocs = docCategories.flatMap(category => 
    category.docs.filter(doc => {
      const matchesSearch = 
        doc.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        doc.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        doc.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
      
      const matchesCategory = selectedCategory === 'all' || category.id === selectedCategory
      
      return matchesSearch && matchesCategory
    }).map(doc => ({ ...doc, categoryName: category.name, categoryColor: category.color }))
  )

  // 获取难度颜色
  const getDifficultyColor = (difficulty: string) => {
    const colors = {
      beginner: 'bg-green-100 text-green-700',
      intermediate: 'bg-yellow-100 text-yellow-700',
      advanced: 'bg-red-100 text-red-700'
    }
    return colors[difficulty as keyof typeof colors] || 'bg-gray-100 text-gray-700'
  }

  // 获取类型图标
  const getTypeIcon = (type: string) => {
    const icons = {
      guide: FileText,
      tutorial: Video,
      reference: Code,
      faq: HelpCircle
    }
    return icons[type as keyof typeof icons] || FileText
  }

  return (
    <div className="p-6 max-w-7xl mx-auto">
      {/* 页面标题 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="mb-8"
      >
        <div className="flex items-center space-x-3 mb-4">
          <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-teal-600 rounded-lg flex items-center justify-center">
            <Book className="w-5 h-5 text-white" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">文档中心</h1>
            <p className="text-gray-600">帮助文档、使用指南和最佳实践</p>
          </div>
        </div>

        {/* 搜索 */}
        <div className="max-w-md">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="搜索文档..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>
      </motion.div>

      {selectedDoc ? (
        /* 文档详情页 */
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          className="space-y-6"
        >
          <div className="flex items-center space-x-2 text-sm text-gray-500">
            <button
              onClick={() => setSelectedDoc(null)}
              className="hover:text-gray-700 transition-colors"
            >
              文档中心
            </button>
            <ChevronRight className="w-4 h-4" />
            <span>{selectedDoc.title}</span>
          </div>

          <Card>
            <CardHeader>
              <div className="flex items-start justify-between">
                <div>
                  <CardTitle className="text-2xl mb-2">{selectedDoc.title}</CardTitle>
                  <p className="text-gray-600 mb-4">{selectedDoc.description}</p>
                  <div className="flex items-center space-x-4 text-sm text-gray-500">
                    <div className="flex items-center space-x-1">
                      <User className="w-4 h-4" />
                      <span>{selectedDoc.author}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Clock className="w-4 h-4" />
                      <span>{selectedDoc.readTime} 分钟阅读</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Clock className="w-4 h-4" />
                      <span>更新于 {selectedDoc.updatedAt}</span>
                    </div>
                  </div>
                </div>
                <div className="flex flex-col items-end space-y-2">
                  <Badge className={getDifficultyColor(selectedDoc.difficulty)}>
                    {selectedDoc.difficulty === 'beginner' ? '初级' : 
                     selectedDoc.difficulty === 'intermediate' ? '中级' : '高级'}
                  </Badge>
                  {selectedDoc.popular && (
                    <Badge className="bg-yellow-100 text-yellow-700">
                      热门
                    </Badge>
                  )}
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {selectedDoc.content ? (
                <div className="prose max-w-none">
                  <pre className="whitespace-pre-wrap text-sm leading-relaxed">
                    {selectedDoc.content}
                  </pre>
                </div>
              ) : (
                <div className="text-center py-12">
                  <FileText className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                  <p className="text-gray-500">文档内容正在准备中...</p>
                </div>
              )}
            </CardContent>
          </Card>
        </motion.div>
      ) : (
        /* 文档列表页 */
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* 分类侧边栏 */}
          <div className="lg:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">文档分类</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <button
                  onClick={() => setSelectedCategory('all')}
                  className={`w-full text-left px-3 py-2 rounded-lg transition-colors ${
                    selectedCategory === 'all' 
                      ? 'bg-blue-50 text-blue-700' 
                      : 'hover:bg-gray-50'
                  }`}
                >
                  全部文档
                </button>
                {docCategories.map((category) => {
                  const Icon = category.icon
                  return (
                    <button
                      key={category.id}
                      onClick={() => setSelectedCategory(category.id)}
                      className={`w-full text-left px-3 py-2 rounded-lg transition-colors ${
                        selectedCategory === category.id 
                          ? 'bg-blue-50 text-blue-700' 
                          : 'hover:bg-gray-50'
                      }`}
                    >
                      <div className="flex items-center space-x-2">
                        <Icon className="w-4 h-4" />
                        <span className="text-sm">{category.name}</span>
                      </div>
                    </button>
                  )
                })}
              </CardContent>
            </Card>
          </div>

          {/* 文档列表 */}
          <div className="lg:col-span-3">
            {loading ? (
              <div className="space-y-4">
                {Array.from({ length: 6 }).map((_, i) => (
                  <Card key={i}>
                    <CardContent className="p-6">
                      <div className="animate-pulse space-y-3">
                        <div className="h-4 bg-gray-200 rounded w-3/4" />
                        <div className="h-3 bg-gray-200 rounded w-full" />
                        <div className="h-3 bg-gray-200 rounded w-1/2" />
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : filteredDocs.length === 0 ? (
              <Card>
                <CardContent className="p-12 text-center">
                  <Book className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    没有找到匹配的文档
                  </h3>
                  <p className="text-gray-600">
                    请尝试调整搜索条件或选择其他分类
                  </p>
                </CardContent>
              </Card>
            ) : (
              <div className="space-y-4">
                {filteredDocs.map((doc) => {
                  const TypeIcon = getTypeIcon(doc.type)
                  return (
                    <motion.div
                      key={doc.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                    >
                      <Card className="hover:shadow-md transition-shadow cursor-pointer">
                        <CardContent 
                          className="p-6"
                          onClick={() => setSelectedDoc(doc)}
                        >
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <div className="flex items-center space-x-3 mb-2">
                                <TypeIcon className="w-5 h-5 text-gray-500" />
                                <h3 className="text-lg font-semibold text-gray-900 hover:text-blue-600 transition-colors">
                                  {doc.title}
                                </h3>
                                {doc.popular && (
                                  <Badge className="bg-yellow-100 text-yellow-700 text-xs">
                                    热门
                                  </Badge>
                                )}
                              </div>
                              <p className="text-gray-600 mb-3 leading-relaxed">
                                {doc.description}
                              </p>
                              <div className="flex items-center space-x-4 text-sm text-gray-500">
                                <div className="flex items-center space-x-1">
                                  <Clock className="w-3 h-3" />
                                  <span>{doc.readTime} 分钟</span>
                                </div>
                                <div className="flex items-center space-x-1">
                                  <User className="w-3 h-3" />
                                  <span>{doc.author}</span>
                                </div>
                                <span>更新于 {doc.updatedAt}</span>
                              </div>
                              <div className="flex items-center space-x-2 mt-3">
                                <Badge className={getDifficultyColor(doc.difficulty)}>
                                  {doc.difficulty === 'beginner' ? '初级' : 
                                   doc.difficulty === 'intermediate' ? '中级' : '高级'}
                                </Badge>
                                {doc.tags.map((tag) => (
                                  <Badge key={tag} variant="outline" className="text-xs">
                                    {tag}
                                  </Badge>
                                ))}
                              </div>
                            </div>
                            <ChevronRight className="w-5 h-5 text-gray-400 ml-4" />
                          </div>
                        </CardContent>
                      </Card>
                    </motion.div>
                  )
                })}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}

export default DocsPage
