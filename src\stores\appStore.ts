import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import type { SiteStats, Announcement, SponsorRecord } from '@/types'

interface AppState {
  // UI状态
  sidebarCollapsed: boolean
  mobileMenuOpen: boolean
  
  // 数据状态
  siteStats: SiteStats
  announcements: Announcement[]
  sponsorRanking: SponsorRecord[]
  
  // 加载状态
  loading: {
    stats: boolean
    announcements: boolean
    sponsors: boolean
  }
  
  // 错误状态
  errors: {
    stats: string | null
    announcements: string | null
    sponsors: string | null
  }
}

interface AppStore extends AppState {
  // UI Actions
  toggleSidebar: () => void
  setSidebarCollapsed: (collapsed: boolean) => void
  toggleMobileMenu: () => void
  setMobileMenuOpen: (open: boolean) => void
  
  // Data Actions
  setSiteStats: (stats: SiteStats) => void
  setAnnouncements: (announcements: Announcement[]) => void
  setSponsorRanking: (sponsors: SponsorRecord[]) => void
  
  // Loading Actions
  setLoading: (key: keyof AppState['loading'], loading: boolean) => void
  
  // Error Actions
  setError: (key: keyof AppState['errors'], error: string | null) => void
  clearErrors: () => void
}

export const useAppStore = create<AppStore>()(
  devtools(
    (set, get) => ({
      // Initial state
      sidebarCollapsed: false,
      mobileMenuOpen: false,
      
      siteStats: {
        onlineUsers: 0,
        users: {
          total: 0,
          todayNew: 0,
          yesterdayNew: 0
        }
      },
      
      announcements: [],
      sponsorRanking: [],
      
      loading: {
        stats: false,
        announcements: false,
        sponsors: false
      },
      
      errors: {
        stats: null,
        announcements: null,
        sponsors: null
      },

      // UI Actions
      toggleSidebar: () => {
        set((state) => ({ 
          sidebarCollapsed: !state.sidebarCollapsed 
        }), false, 'toggleSidebar')
      },

      setSidebarCollapsed: (collapsed) => {
        set({ sidebarCollapsed: collapsed }, false, 'setSidebarCollapsed')
      },

      toggleMobileMenu: () => {
        set((state) => ({ 
          mobileMenuOpen: !state.mobileMenuOpen 
        }), false, 'toggleMobileMenu')
      },

      setMobileMenuOpen: (open) => {
        set({ mobileMenuOpen: open }, false, 'setMobileMenuOpen')
      },

      // Data Actions
      setSiteStats: (stats) => {
        set({ siteStats: stats }, false, 'setSiteStats')
      },

      setAnnouncements: (announcements) => {
        set({ announcements }, false, 'setAnnouncements')
      },

      setSponsorRanking: (sponsors) => {
        set({ sponsorRanking: sponsors }, false, 'setSponsorRanking')
      },

      // Loading Actions
      setLoading: (key, loading) => {
        set((state) => ({
          loading: { ...state.loading, [key]: loading }
        }), false, `setLoading:${key}`)
      },

      // Error Actions
      setError: (key, error) => {
        set((state) => ({
          errors: { ...state.errors, [key]: error }
        }), false, `setError:${key}`)
      },

      clearErrors: () => {
        set({
          errors: {
            stats: null,
            announcements: null,
            sponsors: null
          }
        }, false, 'clearErrors')
      }
    }),
    {
      name: 'app-store'
    }
  )
)
