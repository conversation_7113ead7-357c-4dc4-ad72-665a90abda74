import React, { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { motion } from 'framer-motion'
import { Save, Upload, Camera, Loader2 } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { useAuthStore } from '@/stores/authStore'
import { getTrustLevelName } from '@/lib/auth'

// 表单验证schema
const profileSchema = z.object({
  displayName: z.string().min(1, '显示名称不能为空').max(50, '显示名称不能超过50个字符'),
  bio: z.string().max(500, '个人简介不能超过500个字符').optional(),
  website: z.string().url('请输入有效的网址').optional().or(z.literal('')),
  location: z.string().max(100, '地址不能超过100个字符').optional(),
  company: z.string().max(100, '公司不能超过100个字符').optional()
})

type ProfileFormData = z.infer<typeof profileSchema>

interface ProfileFormProps {
  onSave?: (data: ProfileFormData) => Promise<void>
}

const ProfileForm: React.FC<ProfileFormProps> = ({ onSave }) => {
  const { user, refreshUser } = useAuthStore()
  const [isUploading, setIsUploading] = useState(false)
  const [isSaving, setIsSaving] = useState(false)

  const {
    register,
    handleSubmit,
    formState: { errors, isDirty },
    reset
  } = useForm<ProfileFormData>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      displayName: user?.linuxdo_username || user?.email || '',
      bio: '',
      website: '',
      location: '',
      company: ''
    }
  })

  // 处理头像上传
  const handleAvatarUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    // 验证文件类型
    if (!file.type.startsWith('image/')) {
      alert('请选择图片文件')
      return
    }

    // 验证文件大小（5MB）
    if (file.size > 5 * 1024 * 1024) {
      alert('图片大小不能超过5MB')
      return
    }

    setIsUploading(true)
    try {
      // TODO: 实现头像上传逻辑
      console.log('上传头像:', file)
      
      // 模拟上传延迟
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // 这里应该调用API上传头像并更新用户信息
      await refreshUser()
      
    } catch (error) {
      console.error('头像上传失败:', error)
      alert('头像上传失败，请重试')
    } finally {
      setIsUploading(false)
    }
  }

  // 处理表单提交
  const handleFormSubmit = async (data: ProfileFormData) => {
    setIsSaving(true)
    try {
      if (onSave) {
        await onSave(data)
      } else {
        // TODO: 调用API保存用户资料
        console.log('保存用户资料:', data)
        
        // 模拟保存延迟
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        await refreshUser()
      }
      
      reset(data) // 重置表单状态
      alert('保存成功！')
    } catch (error) {
      console.error('保存失败:', error)
      alert('保存失败，请重试')
    } finally {
      setIsSaving(false)
    }
  }

  if (!user) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-gray-500">
            用户信息加载中...
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>个人资料</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* 头像区域 */}
        <div className="flex items-center space-x-6">
          <div className="relative">
            <Avatar className="w-24 h-24">
              <AvatarImage src={user.linuxdo_avatar} />
              <AvatarFallback className="text-2xl">
                {user.linuxdo_username?.[0]?.toUpperCase() || user.email[0].toUpperCase()}
              </AvatarFallback>
            </Avatar>
            
            {/* 上传按钮 */}
            <label className="absolute bottom-0 right-0 bg-blue-500 text-white rounded-full p-2 cursor-pointer hover:bg-blue-600 transition-colors">
              {isUploading ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <Camera className="w-4 h-4" />
              )}
              <input
                type="file"
                accept="image/*"
                onChange={handleAvatarUpload}
                className="hidden"
                disabled={isUploading}
              />
            </label>
          </div>

          <div className="flex-1">
            <h3 className="text-lg font-semibold text-gray-900">
              {user.linuxdo_username || user.email}
            </h3>
            <p className="text-gray-600 text-sm mb-2">
              {user.email}
            </p>
            <div className="flex items-center space-x-2">
              {user.linuxdo_trust_level !== undefined && (
                <Badge variant="secondary">
                  {getTrustLevelName(user.linuxdo_trust_level)}
                </Badge>
              )}
              {user.isAdmin && (
                <Badge variant="destructive">
                  管理员
                </Badge>
              )}
            </div>
          </div>
        </div>

        {/* 表单 */}
        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-4">
          {/* 显示名称 */}
          <div className="space-y-2">
            <Label htmlFor="displayName">显示名称 *</Label>
            <Input
              id="displayName"
              {...register('displayName')}
              placeholder="输入显示名称"
            />
            {errors.displayName && (
              <p className="text-sm text-red-500">{errors.displayName.message}</p>
            )}
          </div>

          {/* 个人简介 */}
          <div className="space-y-2">
            <Label htmlFor="bio">个人简介</Label>
            <Textarea
              id="bio"
              {...register('bio')}
              placeholder="介绍一下自己..."
              rows={4}
            />
            {errors.bio && (
              <p className="text-sm text-red-500">{errors.bio.message}</p>
            )}
          </div>

          {/* 网站 */}
          <div className="space-y-2">
            <Label htmlFor="website">个人网站</Label>
            <Input
              id="website"
              type="url"
              {...register('website')}
              placeholder="https://example.com"
            />
            {errors.website && (
              <p className="text-sm text-red-500">{errors.website.message}</p>
            )}
          </div>

          {/* 地址和公司 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="location">地址</Label>
              <Input
                id="location"
                {...register('location')}
                placeholder="城市, 国家"
              />
              {errors.location && (
                <p className="text-sm text-red-500">{errors.location.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="company">公司</Label>
              <Input
                id="company"
                {...register('company')}
                placeholder="公司名称"
              />
              {errors.company && (
                <p className="text-sm text-red-500">{errors.company.message}</p>
              )}
            </div>
          </div>

          {/* 提交按钮 */}
          <div className="flex justify-end space-x-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => reset()}
              disabled={!isDirty || isSaving}
            >
              重置
            </Button>
            <Button
              type="submit"
              disabled={!isDirty || isSaving}
              className="flex items-center space-x-2"
            >
              {isSaving ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <Save className="w-4 h-4" />
              )}
              <span>{isSaving ? '保存中...' : '保存更改'}</span>
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}

export default ProfileForm
