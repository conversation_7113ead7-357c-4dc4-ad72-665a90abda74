import React, { useEffect, useRef, useState } from 'react'
import { motion } from 'framer-motion'
import { useBreakpoint, useSwipeGesture } from '@/hooks/useMobile'
import { MessageSquare, Users, Settings, Wifi, WifiOff } from 'lucide-react'
import { Button } from '@/components/ui/button'
import ChatMessage from '@/components/ChatMessage'
import ChatInput from '@/components/ChatInput'
import OnlineUserList from '@/components/OnlineUserList'
import { useChatStore } from '@/stores/chatStore'
import { useAuthStore } from '@/stores/authStore'
import { socketManager } from '@/lib/socket'
import type { ChatMessage as ChatMessageType } from '@/types'

const ChatPage: React.FC = () => {
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const messagesContainerRef = useRef<HTMLDivElement>(null)
  const [showUserList, setShowUserList] = useState(false)
  const { isMobile } = useBreakpoint()

  // 手势支持
  const swipeHandlers = useSwipeGesture((direction) => {
    if (isMobile) {
      if (direction === 'right' && !showUserList) {
        setShowUserList(true)
      } else if (direction === 'left' && showUserList) {
        setShowUserList(false)
      }
    }
  })

  const { user } = useAuthStore()
  const {
    messages,
    connected,
    connecting,
    onlineCount,
    autoScroll,
    setConnected,
    setConnecting,
    addMessage,
    setOnlineCount,
    setAutoScroll
  } = useChatStore()

  // 模拟聊天消息数据
  const mockMessages: ChatMessageType[] = [
    {
      id: '1',
      user_id: 'system',
      username: '系统',
      content: '欢迎来到 AugmentAPI 聊天室！',
      type: 'system',
      created_at: new Date(Date.now() - 60000).toISOString()
    },
    {
      id: '2',
      user_id: 'admin',
      username: 'admin',
      avatar: '',
      content: '大家好！有什么问题可以随时提问。',
      type: 'text',
      created_at: new Date(Date.now() - 30000).toISOString()
    },
    {
      id: '3',
      user_id: 'user123',
      username: 'user123',
      avatar: '',
      content: '这个API文档在哪里可以找到？',
      type: 'text',
      created_at: new Date(Date.now() - 15000).toISOString()
    }
  ]

  // 滚动到底部
  const scrollToBottom = () => {
    if (autoScroll && messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' })
    }
  }

  // 检查是否应该自动滚动
  const handleScroll = () => {
    if (!messagesContainerRef.current) return
    
    const { scrollTop, scrollHeight, clientHeight } = messagesContainerRef.current
    const isNearBottom = scrollHeight - scrollTop - clientHeight < 100
    
    if (isNearBottom !== autoScroll) {
      setAutoScroll(isNearBottom)
    }
  }

  // 发送消息
  const handleSendMessage = async (content: string) => {
    if (!user) return

    // 创建临时消息（实际应该等待服务器确认）
    const tempMessage: ChatMessageType = {
      id: Date.now().toString(),
      user_id: user.id,
      username: user.linuxdo_username || user.email,
      avatar: user.linuxdo_avatar,
      content,
      type: 'text',
      created_at: new Date().toISOString()
    }

    addMessage(tempMessage)
    
    try {
      socketManager.sendMessage(content)
    } catch (error) {
      console.error('发送消息失败:', error)
      // 这里可以添加错误处理，比如显示重试按钮
    }
  }

  // 初始化Socket连接
  useEffect(() => {
    setConnecting(true)

    // 设置Socket事件监听
    const handleConnect = () => {
      setConnected(true)
      setConnecting(false)
    }

    const handleDisconnect = () => {
      setConnected(false)
    }

    const handleMessage = (message: ChatMessageType) => {
      addMessage(message)
    }

    const handleOnlineUsersUpdate = (count: number) => {
      setOnlineCount(count)
    }

    // 注册事件监听器
    socketManager.on('connected', handleConnect)
    socketManager.on('disconnected', handleDisconnect)
    socketManager.on('chat_message', handleMessage)
    socketManager.on('online_users_update', handleOnlineUsersUpdate)

    // 模拟连接（实际环境中Socket会自动连接）
    setTimeout(() => {
      setConnected(true)
      setConnecting(false)
      setOnlineCount(3)
    }, 1000)

    return () => {
      socketManager.off('connected', handleConnect)
      socketManager.off('disconnected', handleDisconnect)
      socketManager.off('chat_message', handleMessage)
      socketManager.off('online_users_update', handleOnlineUsersUpdate)
    }
  }, [])

  // 自动滚动到底部
  useEffect(() => {
    scrollToBottom()
  }, [messages, autoScroll])

  // 使用模拟数据或实际数据
  const displayMessages = messages.length > 0 ? messages : mockMessages

  return (
    <div
      className="h-full flex"
      {...(isMobile ? {
        onTouchStart: swipeHandlers.onTouchStart as any,
        onTouchEnd: swipeHandlers.onTouchEnd as any
      } : {})}
    >
      {/* 主聊天区域 */}
      <div className="flex-1 flex flex-col min-w-0">
        {/* 聊天头部 */}
        <div className="bg-white border-b border-gray-200 px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center">
                <MessageSquare className="w-5 h-5 text-white" />
              </div>
              <div>
                <h1 className="text-lg font-semibold text-gray-900">
                  实时聊天
                </h1>
                <div className="flex items-center space-x-2 text-sm text-gray-500">
                  {connected ? (
                    <>
                      <Wifi className="w-4 h-4 text-green-500" />
                      <span>已连接</span>
                    </>
                  ) : (
                    <>
                      <WifiOff className="w-4 h-4 text-red-500" />
                      <span>{connecting ? '连接中...' : '连接断开'}</span>
                    </>
                  )}
                  <span>•</span>
                  <span>{onlineCount} 人在线</span>
                </div>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowUserList(!showUserList)}
                className="md:hidden"
              >
                <Users className="w-4 h-4" />
              </Button>
              <Button variant="outline" size="sm">
                <Settings className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* 消息列表 */}
        <div 
          ref={messagesContainerRef}
          onScroll={handleScroll}
          className="flex-1 overflow-y-auto bg-gray-50"
        >
          <div className="py-4">
            {displayMessages.map((message, index) => {
              const prevMessage = displayMessages[index - 1]
              const isConsecutive = 
                prevMessage &&
                prevMessage.user_id === message.user_id &&
                prevMessage.type !== 'system' &&
                message.type !== 'system' &&
                new Date(message.created_at).getTime() - new Date(prevMessage.created_at).getTime() < 300000 // 5分钟内

              return (
                <ChatMessage
                  key={message.id}
                  message={message}
                  isConsecutive={isConsecutive}
                  showAvatar={!isConsecutive}
                />
              )
            })}
            <div ref={messagesEndRef} />
          </div>

          {/* 自动滚动提示 */}
          {!autoScroll && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="fixed bottom-24 left-1/2 transform -translate-x-1/2 z-10"
            >
              <Button
                onClick={scrollToBottom}
                variant="secondary"
                size="sm"
                className="shadow-lg"
              >
                滚动到底部
              </Button>
            </motion.div>
          )}
        </div>

        {/* 输入区域 */}
        <ChatInput
          onSendMessage={handleSendMessage}
          disabled={!connected}
          placeholder="输入消息..."
        />
      </div>

      {/* 右侧用户列表（桌面端） */}
      <div className={`hidden md:block w-80 border-l border-gray-200 bg-white ${showUserList ? 'block' : 'hidden'}`}>
        <OnlineUserList />
      </div>

      {/* 移动端用户列表覆盖层 */}
      {showUserList && (
        <div className="md:hidden fixed inset-0 z-50 bg-black bg-opacity-50">
          <div className="absolute right-0 top-0 h-full w-80 bg-white">
            <div className="p-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h2 className="text-lg font-semibold">在线用户</h2>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowUserList(false)}
                >
                  ✕
                </Button>
              </div>
            </div>
            <OnlineUserList />
          </div>
        </div>
      )}
    </div>
  )
}

export default ChatPage
