import React from 'react'
import { Link, useLocation } from 'react-router-dom'
import { motion } from 'framer-motion'
import { 
  Home, 
  MessageSquare, 
  Settings, 
  FileText, 
  Users, 
  BarChart3,
  User,
  Download,
  HelpCircle,
  ChevronLeft,
  ChevronRight
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { useAuthStore } from '@/stores/authStore'
import { useAppStore } from '@/stores/appStore'
import { getTrustLevelName } from '@/lib/auth'

interface NavItem {
  title: string
  href: string
  icon: React.ComponentType<{ className?: string }>
  adminOnly?: boolean
  badge?: string
}

const navItems: NavItem[] = [
  {
    title: '首页',
    href: '/',
    icon: Home
  },
  {
    title: '个人中心',
    href: '/profile',
    icon: User
  },
  {
    title: '实时聊天',
    href: '/chat',
    icon: MessageSquare
  },
  {
    title: 'API文档',
    href: '/api-docs',
    icon: FileText
  },
  {
    title: '外部接口测试',
    href: '/external-test',
    icon: Settings
  },
  {
    title: '文档中心',
    href: '/docs',
    icon: FileText
  },
  {
    title: '下载中心',
    href: '/downloads',
    icon: Download
  },
  {
    title: '管理后台',
    href: '/admin',
    icon: Users,
    adminOnly: true
  }
]

const Sidebar: React.FC = () => {
  const location = useLocation()
  const { user } = useAuthStore()
  const { sidebarCollapsed, toggleSidebar } = useAppStore()

  const filteredNavItems = navItems.filter(item => 
    !item.adminOnly || user?.isAdmin
  )

  return (
    <motion.div
      initial={false}
      animate={{ 
        width: sidebarCollapsed ? 80 : 280 
      }}
      transition={{ duration: 0.3, ease: "easeInOut" }}
      className="bg-white border-r border-gray-200 flex flex-col h-full relative"
    >
      {/* 折叠按钮 */}
      <button
        onClick={toggleSidebar}
        className="absolute -right-3 top-6 z-10 bg-white border border-gray-200 rounded-full p-1 hover:bg-gray-50 transition-colors"
      >
        {sidebarCollapsed ? (
          <ChevronRight className="w-4 h-4" />
        ) : (
          <ChevronLeft className="w-4 h-4" />
        )}
      </button>

      {/* Logo区域 */}
      <div className="p-6 border-b border-gray-100">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-lg">A</span>
          </div>
          {!sidebarCollapsed && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ delay: 0.1 }}
            >
              <h1 className="text-xl font-bold text-gray-900">AugmentAPI</h1>
              <p className="text-xs text-gray-500">智能编程助手</p>
            </motion.div>
          )}
        </div>
      </div>

      {/* 用户信息 */}
      {user && (
        <div className="p-4 border-b border-gray-100">
          <div className="flex items-center space-x-3">
            <Avatar className="w-10 h-10">
              <AvatarImage src={user.linuxdo_avatar} />
              <AvatarFallback>
                {user.linuxdo_username?.[0]?.toUpperCase() || user.email[0].toUpperCase()}
              </AvatarFallback>
            </Avatar>
            {!sidebarCollapsed && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                transition={{ delay: 0.1 }}
                className="flex-1 min-w-0"
              >
                <p className="text-sm font-medium text-gray-900 truncate">
                  {user.linuxdo_username || user.email}
                </p>
                <div className="flex items-center space-x-2 mt-1">
                  {user.linuxdo_trust_level !== undefined && (
                    <Badge variant="secondary" className="text-xs">
                      {getTrustLevelName(user.linuxdo_trust_level)}
                    </Badge>
                  )}
                  {user.isAdmin && (
                    <Badge variant="destructive" className="text-xs">
                      管理员
                    </Badge>
                  )}
                </div>
              </motion.div>
            )}
          </div>
        </div>
      )}

      {/* 导航菜单 */}
      <nav className="flex-1 p-4 space-y-2">
        {filteredNavItems.map((item) => {
          const isActive = location.pathname === item.href
          const Icon = item.icon

          return (
            <Link
              key={item.href}
              to={item.href}
              className={cn(
                "flex items-center space-x-3 px-3 py-2 rounded-lg transition-all duration-200 group",
                isActive
                  ? "bg-blue-50 text-blue-700 border border-blue-200"
                  : "text-gray-600 hover:bg-gray-50 hover:text-gray-900"
              )}
            >
              <Icon className={cn(
                "w-5 h-5 flex-shrink-0",
                isActive ? "text-blue-600" : "text-gray-400 group-hover:text-gray-600"
              )} />
              {!sidebarCollapsed && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  transition={{ delay: 0.1 }}
                  className="flex items-center justify-between flex-1"
                >
                  <span className="font-medium">{item.title}</span>
                  {item.badge && (
                    <Badge variant="secondary" className="text-xs">
                      {item.badge}
                    </Badge>
                  )}
                </motion.div>
              )}
            </Link>
          )
        })}
      </nav>

      {/* 底部信息 */}
      {!sidebarCollapsed && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ delay: 0.1 }}
          className="p-4 border-t border-gray-100"
        >
          <div className="text-center">
            <p className="text-xs text-gray-500 mb-2">
              © 2024 AugmentAPI
            </p>
            <div className="flex justify-center space-x-4">
              <a 
                href="#" 
                className="text-xs text-gray-400 hover:text-gray-600"
              >
                技术交流群
              </a>
              <a 
                href="#" 
                className="text-xs text-gray-400 hover:text-gray-600"
              >
                赞助支持
              </a>
            </div>
          </div>
        </motion.div>
      )}
    </motion.div>
  )
}

export default Sidebar
