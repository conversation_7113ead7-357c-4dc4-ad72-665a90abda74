import React, { useEffect, useState } from 'react'
import { motion } from 'framer-motion'
import { 
  Users, 
  UserPlus, 
  Activity, 
  TrendingUp,
  Megaphone,
  Heart,
  RefreshCw
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import StatCard from '@/components/StatCard'
import AnnouncementCard from '@/components/AnnouncementCard'
import SponsorRanking from '@/components/SponsorRanking'
import { useAppStore } from '@/stores/appStore'
import { useAuthStore } from '@/stores/authStore'
import { statsApi, announcementApi, sponsorApi } from '@/lib/api'

const HomePage: React.FC = () => {
  const { user } = useAuthStore()
  const { 
    siteStats, 
    announcements, 
    sponsorRanking,
    loading,
    errors,
    setSiteStats,
    setAnnouncements,
    setSponsorRanking,
    setLoading,
    setError
  } = useAppStore()

  const [refreshing, setRefreshing] = useState(false)

  // 加载统计数据
  const loadStats = async () => {
    try {
      setLoading('stats', true)
      setError('stats', null)
      const stats = await statsApi.getSiteStats()
      setSiteStats(stats)
    } catch (error) {
      console.error('加载统计数据失败:', error)
      setError('stats', '加载统计数据失败')
    } finally {
      setLoading('stats', false)
    }
  }

  // 加载公告
  const loadAnnouncements = async () => {
    try {
      setLoading('announcements', true)
      setError('announcements', null)
      const announcements = await announcementApi.getAnnouncements()
      setAnnouncements(announcements)
    } catch (error) {
      console.error('加载公告失败:', error)
      setError('announcements', '加载公告失败')
    } finally {
      setLoading('announcements', false)
    }
  }

  // 加载赞助排行榜
  const loadSponsors = async () => {
    try {
      setLoading('sponsors', true)
      setError('sponsors', null)
      const sponsors = await sponsorApi.getSponsorRanking()
      setSponsorRanking(sponsors)
    } catch (error) {
      console.error('加载赞助排行榜失败:', error)
      setError('sponsors', '加载赞助排行榜失败')
    } finally {
      setLoading('sponsors', false)
    }
  }

  // 刷新所有数据
  const refreshAll = async () => {
    setRefreshing(true)
    await Promise.all([
      loadStats(),
      loadAnnouncements(),
      loadSponsors()
    ])
    setRefreshing(false)
  }

  // 标记公告为已读
  const handleMarkAnnouncementAsRead = async (id: string) => {
    try {
      await announcementApi.markAsRead(id)
      // 可以在这里更新本地状态，移除已读公告
    } catch (error) {
      console.error('标记公告已读失败:', error)
    }
  }

  // 初始加载
  useEffect(() => {
    refreshAll()
  }, [])

  return (
    <div className="p-6 space-y-6">
      {/* 欢迎区域 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="flex items-center justify-between"
      >
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            欢迎回来，{user?.linuxdo_username || user?.email}！
          </h1>
          <p className="text-gray-600 mt-1">
            这里是您的 AugmentAPI 控制台
          </p>
        </div>
        <Button
          onClick={refreshAll}
          disabled={refreshing}
          variant="outline"
          className="flex items-center space-x-2"
        >
          <RefreshCw className={`w-4 h-4 ${refreshing ? 'animate-spin' : ''}`} />
          <span>刷新数据</span>
        </Button>
      </motion.div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="在线用户"
          value={siteStats.onlineUsers}
          icon={Activity}
          color="blue"
        />
        <StatCard
          title="总用户数"
          value={siteStats.users.total}
          icon={Users}
          color="green"
        />
        <StatCard
          title="今日新增"
          value={siteStats.users.todayNew}
          icon={UserPlus}
          color="orange"
          trend={{
            value: siteStats.users.yesterdayNew > 0 
              ? ((siteStats.users.todayNew - siteStats.users.yesterdayNew) / siteStats.users.yesterdayNew * 100)
              : 0,
            isPositive: siteStats.users.todayNew >= siteStats.users.yesterdayNew
          }}
        />
        <StatCard
          title="增长趋势"
          value={`+${((siteStats.users.todayNew / Math.max(siteStats.users.total, 1)) * 100).toFixed(1)}%`}
          icon={TrendingUp}
          color="purple"
        />
      </div>

      {/* 主要内容区域 */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 公告区域 */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Megaphone className="w-5 h-5 text-blue-500" />
                <span>系统公告</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {loading.announcements ? (
                <div className="space-y-4">
                  {[1, 2, 3].map((i) => (
                    <div key={i} className="animate-pulse">
                      <div className="h-4 bg-gray-200 rounded w-3/4 mb-2" />
                      <div className="h-3 bg-gray-200 rounded w-1/2" />
                    </div>
                  ))}
                </div>
              ) : errors.announcements ? (
                <div className="text-center py-8">
                  <p className="text-red-500">{errors.announcements}</p>
                  <Button 
                    onClick={loadAnnouncements}
                    variant="outline"
                    size="sm"
                    className="mt-2"
                  >
                    重试
                  </Button>
                </div>
              ) : announcements.length === 0 ? (
                <div className="text-center py-8">
                  <Megaphone className="w-12 h-12 text-gray-300 mx-auto mb-3" />
                  <p className="text-gray-500">暂无公告</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {announcements.slice(0, 5).map((announcement) => (
                    <AnnouncementCard
                      key={announcement.id}
                      announcement={announcement}
                      onMarkAsRead={handleMarkAnnouncementAsRead}
                    />
                  ))}
                  {announcements.length > 5 && (
                    <div className="text-center pt-4">
                      <Button variant="outline" size="sm">
                        查看更多公告
                      </Button>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* 赞助排行榜 */}
        <div>
          <SponsorRanking 
            sponsors={sponsorRanking}
            loading={loading.sponsors}
          />
        </div>
      </div>

      {/* 快速操作区域 */}
      <Card>
        <CardHeader>
          <CardTitle>快速操作</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Button variant="outline" className="h-20 flex flex-col space-y-2">
              <Activity className="w-6 h-6" />
              <span className="text-sm">实时聊天</span>
            </Button>
            <Button variant="outline" className="h-20 flex flex-col space-y-2">
              <Users className="w-6 h-6" />
              <span className="text-sm">API文档</span>
            </Button>
            <Button variant="outline" className="h-20 flex flex-col space-y-2">
              <Heart className="w-6 h-6" />
              <span className="text-sm">下载中心</span>
            </Button>
            <Button variant="outline" className="h-20 flex flex-col space-y-2">
              <TrendingUp className="w-6 h-6" />
              <span className="text-sm">个人中心</span>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default HomePage
