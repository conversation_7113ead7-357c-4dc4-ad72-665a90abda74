import React, { useState, useRef, useEffect } from 'react'
import { Send, Smile, Paperclip, Loader2 } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { cn } from '@/lib/utils'
import { useChatStore } from '@/stores/chatStore'
import { socketManager } from '@/lib/socket'

interface ChatInputProps {
  onSendMessage?: (content: string) => void
  disabled?: boolean
  placeholder?: string
}

const ChatInput: React.FC<ChatInputProps> = ({
  onSendMessage,
  disabled = false,
  placeholder = "输入消息..."
}) => {
  const inputRef = useRef<HTMLInputElement>(null)
  const [isSending, setIsSending] = useState(false)
  const [typingTimeout, setTypingTimeout] = useState<NodeJS.Timeout | null>(null)
  
  const { 
    inputValue, 
    setInputValue, 
    connected,
    isTyping,
    setIsTyping,
    showEmojiPicker,
    setShowEmojiPicker
  } = useChatStore()

  // 发送消息
  const handleSendMessage = async () => {
    const content = inputValue.trim()
    if (!content || isSending || !connected) return

    try {
      setIsSending(true)
      
      if (onSendMessage) {
        await onSendMessage(content)
      } else {
        socketManager.sendMessage(content)
      }
      
      setInputValue('')
      setIsTyping(false)
      
      // 清除打字状态
      if (typingTimeout) {
        clearTimeout(typingTimeout)
        setTypingTimeout(null)
      }
      
    } catch (error) {
      console.error('发送消息失败:', error)
    } finally {
      setIsSending(false)
      inputRef.current?.focus()
    }
  }

  // 处理键盘事件
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  // 处理输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setInputValue(value)

    // 打字状态管理
    if (value.trim() && !isTyping) {
      setIsTyping(true)
      // 这里可以发送打字状态到服务器
      // socketManager.emit('typing_start')
    }

    // 清除之前的超时
    if (typingTimeout) {
      clearTimeout(typingTimeout)
    }

    // 设置新的超时，停止打字状态
    const timeout = setTimeout(() => {
      setIsTyping(false)
      // socketManager.emit('typing_stop')
    }, 1000)
    
    setTypingTimeout(timeout)
  }

  // 处理文件上传
  const handleFileUpload = () => {
    // TODO: 实现文件上传功能
    console.log('文件上传功能待实现')
  }

  // 处理表情选择
  const handleEmojiClick = () => {
    setShowEmojiPicker(!showEmojiPicker)
  }

  // 清理定时器
  useEffect(() => {
    return () => {
      if (typingTimeout) {
        clearTimeout(typingTimeout)
      }
    }
  }, [typingTimeout])

  // 自动聚焦
  useEffect(() => {
    if (connected && inputRef.current) {
      inputRef.current.focus()
    }
  }, [connected])

  return (
    <div className="border-t border-gray-200 bg-white p-4">
      {/* 打字状态指示器 */}
      {isTyping && (
        <div className="text-xs text-gray-500 mb-2 flex items-center">
          <div className="flex space-x-1 mr-2">
            <div className="w-1 h-1 bg-gray-400 rounded-full animate-bounce" />
            <div className="w-1 h-1 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} />
            <div className="w-1 h-1 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
          </div>
          正在输入...
        </div>
      )}

      {/* 输入区域 */}
      <div className="flex items-end space-x-2">
        {/* 附件按钮 */}
        <Button
          variant="ghost"
          size="sm"
          onClick={handleFileUpload}
          disabled={disabled || !connected}
          className="flex-shrink-0"
        >
          <Paperclip className="w-4 h-4" />
        </Button>

        {/* 输入框 */}
        <div className="flex-1 relative">
          <Input
            ref={inputRef}
            value={inputValue}
            onChange={handleInputChange}
            onKeyPress={handleKeyPress}
            placeholder={connected ? placeholder : "连接中..."}
            disabled={disabled || !connected || isSending}
            className="pr-10 resize-none"
            maxLength={1000}
          />
          
          {/* 表情按钮 */}
          <Button
            variant="ghost"
            size="sm"
            onClick={handleEmojiClick}
            disabled={disabled || !connected}
            className="absolute right-1 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0"
          >
            <Smile className="w-4 h-4" />
          </Button>
        </div>

        {/* 发送按钮 */}
        <Button
          onClick={handleSendMessage}
          disabled={disabled || !connected || !inputValue.trim() || isSending}
          size="sm"
          className="flex-shrink-0"
        >
          {isSending ? (
            <Loader2 className="w-4 h-4 animate-spin" />
          ) : (
            <Send className="w-4 h-4" />
          )}
        </Button>
      </div>

      {/* 连接状态提示 */}
      {!connected && (
        <div className="mt-2 text-xs text-red-500 flex items-center">
          <div className="w-2 h-2 bg-red-500 rounded-full mr-2" />
          连接已断开，正在重连...
        </div>
      )}

      {/* 字符计数 */}
      <div className="mt-2 text-xs text-gray-400 text-right">
        {inputValue.length}/1000
      </div>
    </div>
  )
}

export default ChatInput
