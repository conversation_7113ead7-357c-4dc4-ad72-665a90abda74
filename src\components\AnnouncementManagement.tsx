import React, { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Megaphone, 
  Plus, 
  Edit, 
  Trash2, 
  Pin, 
  PinOff,
  Save,
  X,
  Info,
  AlertTriangle,
  CheckCircle,
  XCircle
} from 'lucide-react'
import dayjs from 'dayjs'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import type { Announcement } from '@/types'

// 公告表单验证
const announcementSchema = z.object({
  title: z.string().min(1, '标题不能为空').max(100, '标题不能超过100个字符'),
  content: z.string().min(1, '内容不能为空').max(2000, '内容不能超过2000个字符'),
  type: z.enum(['info', 'warning', 'success', 'error']),
  is_pinned: z.boolean()
})

type AnnouncementFormData = z.infer<typeof announcementSchema>

const AnnouncementManagement: React.FC = () => {
  const [announcements, setAnnouncements] = useState<Announcement[]>([])
  const [loading, setLoading] = useState(true)
  const [showCreateDialog, setShowCreateDialog] = useState(false)
  const [editingAnnouncement, setEditingAnnouncement] = useState<Announcement | null>(null)
  const [deletingAnnouncement, setDeletingAnnouncement] = useState<Announcement | null>(null)
  const [isSaving, setIsSaving] = useState(false)

  // 表单处理
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
    watch
  } = useForm<AnnouncementFormData>({
    resolver: zodResolver(announcementSchema),
    defaultValues: {
      title: '',
      content: '',
      type: 'info',
      is_pinned: false
    }
  })

  const watchedType = watch('type')

  // 模拟公告数据
  const mockAnnouncements: Announcement[] = [
    {
      id: '1',
      title: '系统维护通知',
      content: '系统将于今晚23:00-01:00进行维护，期间可能无法正常访问，请提前做好准备。',
      type: 'warning',
      is_pinned: true,
      author_name: 'admin',
      created_at: new Date(Date.now() - 86400000).toISOString(),
      updated_at: new Date(Date.now() - 86400000).toISOString()
    },
    {
      id: '2',
      title: '新功能上线',
      content: 'API文档功能已正式上线，现在您可以在线查看和测试所有API接口。',
      type: 'success',
      is_pinned: false,
      author_name: 'admin',
      created_at: new Date(Date.now() - 172800000).toISOString(),
      updated_at: new Date(Date.now() - 172800000).toISOString()
    },
    {
      id: '3',
      title: '重要安全更新',
      content: '我们发现了一个安全漏洞并已修复，建议所有用户更新API密钥。',
      type: 'error',
      is_pinned: false,
      author_name: 'admin',
      created_at: new Date(Date.now() - 259200000).toISOString(),
      updated_at: new Date(Date.now() - 259200000).toISOString()
    }
  ]

  // 加载公告数据
  useEffect(() => {
    const loadAnnouncements = async () => {
      setLoading(true)
      try {
        // TODO: 调用API获取公告列表
        await new Promise(resolve => setTimeout(resolve, 1000))
        setAnnouncements(mockAnnouncements)
      } catch (error) {
        console.error('加载公告列表失败:', error)
      } finally {
        setLoading(false)
      }
    }

    loadAnnouncements()
  }, [])

  // 获取类型配置
  const getTypeConfig = (type: Announcement['type']) => {
    const configs = {
      info: { icon: Info, color: 'text-blue-600', bg: 'bg-blue-50', border: 'border-blue-200', label: '通知' },
      warning: { icon: AlertTriangle, color: 'text-orange-600', bg: 'bg-orange-50', border: 'border-orange-200', label: '警告' },
      success: { icon: CheckCircle, color: 'text-green-600', bg: 'bg-green-50', border: 'border-green-200', label: '成功' },
      error: { icon: XCircle, color: 'text-red-600', bg: 'bg-red-50', border: 'border-red-200', label: '错误' }
    }
    return configs[type]
  }

  // 创建或更新公告
  const handleSaveAnnouncement = async (data: AnnouncementFormData) => {
    setIsSaving(true)
    try {
      if (editingAnnouncement) {
        // 更新公告
        const updatedAnnouncement: Announcement = {
          ...editingAnnouncement,
          ...data,
          updated_at: new Date().toISOString()
        }
        setAnnouncements(prev => prev.map(a => 
          a.id === editingAnnouncement.id ? updatedAnnouncement : a
        ))
      } else {
        // 创建新公告
        const newAnnouncement: Announcement = {
          id: Date.now().toString(),
          ...data,
          author_name: 'admin',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }
        setAnnouncements(prev => [newAnnouncement, ...prev])
      }

      reset()
      setShowCreateDialog(false)
      setEditingAnnouncement(null)
      alert('公告保存成功！')
    } catch (error) {
      console.error('保存公告失败:', error)
      alert('保存失败，请重试')
    } finally {
      setIsSaving(false)
    }
  }

  // 编辑公告
  const handleEditAnnouncement = (announcement: Announcement) => {
    setEditingAnnouncement(announcement)
    setValue('title', announcement.title)
    setValue('content', announcement.content)
    setValue('type', announcement.type)
    setValue('is_pinned', announcement.is_pinned)
    setShowCreateDialog(true)
  }

  // 切换置顶状态
  const handleTogglePin = async (announcement: Announcement) => {
    try {
      const updatedAnnouncement = {
        ...announcement,
        is_pinned: !announcement.is_pinned,
        updated_at: new Date().toISOString()
      }
      setAnnouncements(prev => prev.map(a => 
        a.id === announcement.id ? updatedAnnouncement : a
      ))
    } catch (error) {
      console.error('更新置顶状态失败:', error)
      alert('操作失败，请重试')
    }
  }

  // 删除公告
  const handleDeleteAnnouncement = async () => {
    if (!deletingAnnouncement) return

    try {
      setAnnouncements(prev => prev.filter(a => a.id !== deletingAnnouncement.id))
      setDeletingAnnouncement(null)
      alert('公告删除成功！')
    } catch (error) {
      console.error('删除公告失败:', error)
      alert('删除失败，请重试')
    }
  }

  // 关闭对话框
  const handleCloseDialog = () => {
    setShowCreateDialog(false)
    setEditingAnnouncement(null)
    reset()
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <Megaphone className="w-5 h-5" />
            <span>公告管理</span>
          </CardTitle>
          <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
            <DialogTrigger asChild>
              <Button className="flex items-center space-x-2">
                <Plus className="w-4 h-4" />
                <span>发布公告</span>
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>
                  {editingAnnouncement ? '编辑公告' : '发布新公告'}
                </DialogTitle>
              </DialogHeader>
              
              <form onSubmit={handleSubmit(handleSaveAnnouncement)} className="space-y-4">
                {/* 标题 */}
                <div className="space-y-2">
                  <Label htmlFor="title">标题 *</Label>
                  <Input
                    id="title"
                    {...register('title')}
                    placeholder="输入公告标题"
                  />
                  {errors.title && (
                    <p className="text-sm text-red-500">{errors.title.message}</p>
                  )}
                </div>

                {/* 类型和置顶 */}
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="type">类型 *</Label>
                    <Select 
                      value={watchedType} 
                      onValueChange={(value) => setValue('type', value as any)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="info">通知</SelectItem>
                        <SelectItem value="warning">警告</SelectItem>
                        <SelectItem value="success">成功</SelectItem>
                        <SelectItem value="error">错误</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="is_pinned">置顶显示</Label>
                    <div className="flex items-center space-x-2 pt-2">
                      <Switch
                        id="is_pinned"
                        {...register('is_pinned')}
                      />
                      <span className="text-sm text-gray-600">置顶到公告列表顶部</span>
                    </div>
                  </div>
                </div>

                {/* 内容 */}
                <div className="space-y-2">
                  <Label htmlFor="content">内容 *</Label>
                  <Textarea
                    id="content"
                    {...register('content')}
                    placeholder="输入公告内容..."
                    rows={6}
                  />
                  {errors.content && (
                    <p className="text-sm text-red-500">{errors.content.message}</p>
                  )}
                </div>

                {/* 按钮 */}
                <div className="flex justify-end space-x-3 pt-4">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleCloseDialog}
                    disabled={isSaving}
                  >
                    取消
                  </Button>
                  <Button
                    type="submit"
                    disabled={isSaving}
                    className="flex items-center space-x-2"
                  >
                    <Save className="w-4 h-4" />
                    <span>{isSaving ? '保存中...' : '保存'}</span>
                  </Button>
                </div>
              </form>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      <CardContent>
        {/* 公告列表 */}
        <div className="space-y-4">
          {loading ? (
            Array.from({ length: 3 }).map((_, i) => (
              <div key={i} className="border rounded-lg p-4 animate-pulse">
                <div className="flex items-start justify-between mb-3">
                  <div className="space-y-2">
                    <div className="h-4 bg-gray-200 rounded w-48" />
                    <div className="h-3 bg-gray-200 rounded w-32" />
                  </div>
                  <div className="h-8 bg-gray-200 rounded w-20" />
                </div>
                <div className="space-y-2">
                  <div className="h-3 bg-gray-200 rounded w-full" />
                  <div className="h-3 bg-gray-200 rounded w-3/4" />
                </div>
              </div>
            ))
          ) : announcements.length === 0 ? (
            <div className="text-center py-8">
              <Megaphone className="w-12 h-12 text-gray-300 mx-auto mb-3" />
              <p className="text-gray-500">暂无公告</p>
            </div>
          ) : (
            <AnimatePresence>
              {announcements
                .sort((a, b) => {
                  // 置顶公告优先
                  if (a.is_pinned && !b.is_pinned) return -1
                  if (!a.is_pinned && b.is_pinned) return 1
                  // 按创建时间倒序
                  return new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
                })
                .map((announcement) => {
                  const typeConfig = getTypeConfig(announcement.type)
                  const TypeIcon = typeConfig.icon

                  return (
                    <motion.div
                      key={announcement.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -20 }}
                      className={`border rounded-lg p-4 hover:shadow-sm transition-shadow ${typeConfig.border}`}
                    >
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex-1">
                          <div className="flex items-center space-x-3 mb-2">
                            <div className={`p-2 rounded-lg ${typeConfig.bg}`}>
                              <TypeIcon className={`w-4 h-4 ${typeConfig.color}`} />
                            </div>
                            <div className="flex-1">
                              <div className="flex items-center space-x-2">
                                <h3 className="font-semibold text-gray-900">
                                  {announcement.title}
                                </h3>
                                {announcement.is_pinned && (
                                  <Pin className="w-4 h-4 text-orange-500" />
                                )}
                              </div>
                              <div className="flex items-center space-x-2 mt-1">
                                <Badge variant="secondary" className="text-xs">
                                  {typeConfig.label}
                                </Badge>
                                <span className="text-xs text-gray-500">
                                  {announcement.author_name}
                                </span>
                                <span className="text-xs text-gray-500">
                                  {dayjs(announcement.created_at).format('YYYY-MM-DD HH:mm')}
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>
                        
                        <div className="flex items-center space-x-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleTogglePin(announcement)}
                          >
                            {announcement.is_pinned ? (
                              <PinOff className="w-4 h-4" />
                            ) : (
                              <Pin className="w-4 h-4" />
                            )}
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEditAnnouncement(announcement)}
                          >
                            <Edit className="w-4 h-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setDeletingAnnouncement(announcement)}
                            className="text-red-500 hover:text-red-700"
                          >
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>
                      </div>

                      <div className="text-sm text-gray-700 leading-relaxed">
                        {announcement.content}
                      </div>
                    </motion.div>
                  )
                })}
            </AnimatePresence>
          )}
        </div>
      </CardContent>

      {/* 删除确认对话框 */}
      <AlertDialog 
        open={!!deletingAnnouncement} 
        onOpenChange={() => setDeletingAnnouncement(null)}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除</AlertDialogTitle>
            <AlertDialogDescription>
              确定要删除公告 "{deletingAnnouncement?.title}" 吗？此操作不可恢复。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleDeleteAnnouncement}
              className="bg-red-600 hover:bg-red-700"
            >
              删除
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Card>
  )
}

export default AnnouncementManagement
