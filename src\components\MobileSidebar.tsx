import React from 'react'
import { Link, useLocation } from 'react-router-dom'
import { 
  Home, 
  MessageSquare, 
  Settings, 
  FileText, 
  Users, 
  BarChart3,
  User,
  Download,
  HelpCircle,
  X
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Sheet, SheetContent, SheetHeader, SheetTitle } from '@/components/ui/sheet'
import { useAuthStore } from '@/stores/authStore'
import { useAppStore } from '@/stores/appStore'
import { getTrustLevelName } from '@/lib/auth'

interface NavItem {
  title: string
  href: string
  icon: React.ComponentType<{ className?: string }>
  adminOnly?: boolean
  badge?: string
}

const navItems: NavItem[] = [
  {
    title: '首页',
    href: '/',
    icon: Home
  },
  {
    title: '实时聊天',
    href: '/chat',
    icon: MessageSquare,
    badge: 'new'
  },
  {
    title: 'API文档',
    href: '/api-docs',
    icon: FileText
  },
  {
    title: '下载中心',
    href: '/downloads',
    icon: Download
  },
  {
    title: '个人中心',
    href: '/profile',
    icon: User
  },
  {
    title: '管理后台',
    href: '/admin',
    icon: Users,
    adminOnly: true
  },
  {
    title: '数据统计',
    href: '/statistics',
    icon: BarChart3,
    adminOnly: true
  },
  {
    title: '帮助中心',
    href: '/help',
    icon: HelpCircle
  },
  {
    title: '设置',
    href: '/settings',
    icon: Settings
  }
]

const MobileSidebar: React.FC = () => {
  const location = useLocation()
  const { user } = useAuthStore()
  const { mobileMenuOpen, setMobileMenuOpen } = useAppStore()

  const filteredNavItems = navItems.filter(item => 
    !item.adminOnly || user?.isAdmin
  )

  const handleLinkClick = () => {
    setMobileMenuOpen(false)
  }

  return (
    <Sheet open={mobileMenuOpen} onOpenChange={setMobileMenuOpen}>
      <SheetContent side="left" className="w-80 p-0">
        <div className="flex flex-col h-full">
          {/* Header */}
          <SheetHeader className="p-6 border-b border-gray-100">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-lg">A</span>
                </div>
                <div>
                  <SheetTitle className="text-xl font-bold text-gray-900">
                    AugmentAPI
                  </SheetTitle>
                  <p className="text-xs text-gray-500">智能编程助手</p>
                </div>
              </div>
            </div>
          </SheetHeader>

          {/* 用户信息 */}
          {user && (
            <div className="p-4 border-b border-gray-100">
              <div className="flex items-center space-x-3">
                <Avatar className="w-12 h-12">
                  <AvatarImage src={user.linuxdo_avatar} />
                  <AvatarFallback>
                    {user.linuxdo_username?.[0]?.toUpperCase() || user.email[0].toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 truncate">
                    {user.linuxdo_username || user.email}
                  </p>
                  <div className="flex items-center space-x-2 mt-1">
                    {user.linuxdo_trust_level !== undefined && (
                      <Badge variant="secondary" className="text-xs">
                        {getTrustLevelName(user.linuxdo_trust_level)}
                      </Badge>
                    )}
                    {user.isAdmin && (
                      <Badge variant="destructive" className="text-xs">
                        管理员
                      </Badge>
                    )}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* 导航菜单 */}
          <nav className="flex-1 p-4 space-y-2">
            {filteredNavItems.map((item) => {
              const isActive = location.pathname === item.href
              const Icon = item.icon

              return (
                <Link
                  key={item.href}
                  to={item.href}
                  onClick={handleLinkClick}
                  className={cn(
                    "flex items-center space-x-3 px-4 py-3 rounded-lg transition-all duration-200",
                    isActive
                      ? "bg-blue-50 text-blue-700 border border-blue-200"
                      : "text-gray-600 hover:bg-gray-50 hover:text-gray-900"
                  )}
                >
                  <Icon className={cn(
                    "w-5 h-5 flex-shrink-0",
                    isActive ? "text-blue-600" : "text-gray-400"
                  )} />
                  <div className="flex items-center justify-between flex-1">
                    <span className="font-medium">{item.title}</span>
                    {item.badge && (
                      <Badge variant="secondary" className="text-xs">
                        {item.badge}
                      </Badge>
                    )}
                  </div>
                </Link>
              )
            })}
          </nav>

          {/* 底部信息 */}
          <div className="p-4 border-t border-gray-100">
            <div className="text-center">
              <p className="text-xs text-gray-500 mb-2">
                © 2024 AugmentAPI
              </p>
              <div className="flex justify-center space-x-4">
                <a 
                  href="#" 
                  className="text-xs text-gray-400 hover:text-gray-600"
                  onClick={handleLinkClick}
                >
                  技术交流群
                </a>
                <a 
                  href="#" 
                  className="text-xs text-gray-400 hover:text-gray-600"
                  onClick={handleLinkClick}
                >
                  赞助支持
                </a>
              </div>
            </div>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  )
}

export default MobileSidebar
