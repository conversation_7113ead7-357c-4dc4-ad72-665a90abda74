import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Key, 
  Plus, 
  <PERSON><PERSON>, 
  Eye, 
  EyeOff, 
  Trash2, 
  Calendar,
  CheckCircle,
  AlertCircle,
  Loader2
} from 'lucide-react'
import dayjs from 'dayjs'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'

interface ApiKey {
  id: string
  name: string
  key: string
  permissions: string[]
  lastUsed?: string
  createdAt: string
  expiresAt?: string
  isActive: boolean
}

const ApiKeyManager: React.FC = () => {
  const [apiKeys, setApiKeys] = useState<ApiKey[]>([
    {
      id: '1',
      name: '开发环境密钥',
      key: 'ak_dev_1234567890abcdef',
      permissions: ['read', 'write'],
      lastUsed: new Date(Date.now() - 86400000).toISOString(), // 1天前
      createdAt: new Date(Date.now() - 7 * 86400000).toISOString(), // 7天前
      expiresAt: new Date(Date.now() + 30 * 86400000).toISOString(), // 30天后
      isActive: true
    },
    {
      id: '2',
      name: '生产环境密钥',
      key: 'ak_prod_abcdef1234567890',
      permissions: ['read'],
      lastUsed: new Date(Date.now() - 3600000).toISOString(), // 1小时前
      createdAt: new Date(Date.now() - 30 * 86400000).toISOString(), // 30天前
      isActive: true
    }
  ])

  const [showCreateForm, setShowCreateForm] = useState(false)
  const [newKeyName, setNewKeyName] = useState('')
  const [isCreating, setIsCreating] = useState(false)
  const [visibleKeys, setVisibleKeys] = useState<Set<string>>(new Set())

  // 创建新密钥
  const handleCreateKey = async () => {
    if (!newKeyName.trim()) return

    setIsCreating(true)
    try {
      // TODO: 调用API创建密钥
      const newKey: ApiKey = {
        id: Date.now().toString(),
        name: newKeyName,
        key: `ak_${Date.now()}_${Math.random().toString(36).substr(2, 16)}`,
        permissions: ['read'],
        createdAt: new Date().toISOString(),
        isActive: true
      }

      setApiKeys(prev => [newKey, ...prev])
      setNewKeyName('')
      setShowCreateForm(false)
      
      // 自动显示新创建的密钥
      setVisibleKeys(prev => new Set([...prev, newKey.id]))
      
      alert('API密钥创建成功！')
    } catch (error) {
      console.error('创建密钥失败:', error)
      alert('创建失败，请重试')
    } finally {
      setIsCreating(false)
    }
  }

  // 复制密钥
  const handleCopyKey = async (key: string) => {
    try {
      await navigator.clipboard.writeText(key)
      alert('密钥已复制到剪贴板')
    } catch (error) {
      console.error('复制失败:', error)
      alert('复制失败，请手动复制')
    }
  }

  // 切换密钥可见性
  const toggleKeyVisibility = (keyId: string) => {
    setVisibleKeys(prev => {
      const newSet = new Set(prev)
      if (newSet.has(keyId)) {
        newSet.delete(keyId)
      } else {
        newSet.add(keyId)
      }
      return newSet
    })
  }

  // 删除密钥
  const handleDeleteKey = async (keyId: string) => {
    if (!confirm('确定要删除这个API密钥吗？此操作不可恢复。')) {
      return
    }

    try {
      // TODO: 调用API删除密钥
      setApiKeys(prev => prev.filter(key => key.id !== keyId))
      setVisibleKeys(prev => {
        const newSet = new Set(prev)
        newSet.delete(keyId)
        return newSet
      })
      
      alert('API密钥已删除')
    } catch (error) {
      console.error('删除失败:', error)
      alert('删除失败，请重试')
    }
  }

  // 格式化密钥显示
  const formatKey = (key: string, isVisible: boolean) => {
    if (isVisible) {
      return key
    }
    return key.substring(0, 8) + '•'.repeat(key.length - 12) + key.substring(key.length - 4)
  }

  // 获取权限标签颜色
  const getPermissionColor = (permission: string) => {
    switch (permission) {
      case 'read':
        return 'bg-green-100 text-green-700'
      case 'write':
        return 'bg-blue-100 text-blue-700'
      case 'admin':
        return 'bg-red-100 text-red-700'
      default:
        return 'bg-gray-100 text-gray-700'
    }
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <Key className="w-5 h-5" />
            <span>API密钥管理</span>
          </CardTitle>
          <Button
            onClick={() => setShowCreateForm(true)}
            size="sm"
            className="flex items-center space-x-2"
          >
            <Plus className="w-4 h-4" />
            <span>创建密钥</span>
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* 创建表单 */}
        <AnimatePresence>
          {showCreateForm && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="border border-gray-200 rounded-lg p-4 bg-gray-50"
            >
              <h4 className="text-sm font-medium mb-4">创建新的API密钥</h4>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="keyName">密钥名称</Label>
                  <Input
                    id="keyName"
                    value={newKeyName}
                    onChange={(e) => setNewKeyName(e.target.value)}
                    placeholder="输入密钥名称，如：开发环境密钥"
                    className="mt-1"
                  />
                </div>
                <div className="flex space-x-2">
                  <Button
                    onClick={handleCreateKey}
                    disabled={!newKeyName.trim() || isCreating}
                    size="sm"
                    className="flex items-center space-x-2"
                  >
                    {isCreating ? (
                      <Loader2 className="w-4 h-4 animate-spin" />
                    ) : (
                      <Plus className="w-4 h-4" />
                    )}
                    <span>{isCreating ? '创建中...' : '创建'}</span>
                  </Button>
                  <Button
                    onClick={() => {
                      setShowCreateForm(false)
                      setNewKeyName('')
                    }}
                    variant="outline"
                    size="sm"
                  >
                    取消
                  </Button>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* 密钥列表 */}
        <div className="space-y-4">
          {apiKeys.length === 0 ? (
            <div className="text-center py-8">
              <Key className="w-12 h-12 text-gray-300 mx-auto mb-3" />
              <p className="text-gray-500">暂无API密钥</p>
              <p className="text-sm text-gray-400 mt-1">
                创建您的第一个API密钥来开始使用
              </p>
            </div>
          ) : (
            apiKeys.map((apiKey) => {
              const isVisible = visibleKeys.has(apiKey.id)
              const isExpired = apiKey.expiresAt && new Date(apiKey.expiresAt) < new Date()
              
              return (
                <motion.div
                  key={apiKey.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="border border-gray-200 rounded-lg p-4 hover:shadow-sm transition-shadow"
                >
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        <h4 className="font-medium text-gray-900">{apiKey.name}</h4>
                        {isExpired ? (
                          <Badge variant="destructive" className="text-xs">
                            已过期
                          </Badge>
                        ) : (
                          <Badge variant="secondary" className="text-xs">
                            {apiKey.isActive ? '活跃' : '禁用'}
                          </Badge>
                        )}
                      </div>
                      <div className="flex items-center space-x-2 text-sm text-gray-500">
                        <Calendar className="w-3 h-3" />
                        <span>创建于 {dayjs(apiKey.createdAt).format('YYYY-MM-DD')}</span>
                        {apiKey.lastUsed && (
                          <>
                            <span>•</span>
                            <span>最后使用 {dayjs(apiKey.lastUsed).fromNow()}</span>
                          </>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button
                        onClick={() => toggleKeyVisibility(apiKey.id)}
                        variant="ghost"
                        size="sm"
                      >
                        {isVisible ? (
                          <EyeOff className="w-4 h-4" />
                        ) : (
                          <Eye className="w-4 h-4" />
                        )}
                      </Button>
                      <Button
                        onClick={() => handleCopyKey(apiKey.key)}
                        variant="ghost"
                        size="sm"
                      >
                        <Copy className="w-4 h-4" />
                      </Button>
                      <Button
                        onClick={() => handleDeleteKey(apiKey.id)}
                        variant="ghost"
                        size="sm"
                        className="text-red-500 hover:text-red-700"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>

                  {/* 密钥值 */}
                  <div className="bg-gray-50 rounded-md p-3 mb-3">
                    <div className="flex items-center justify-between">
                      <code className="text-sm font-mono text-gray-800 break-all">
                        {formatKey(apiKey.key, isVisible)}
                      </code>
                      {isVisible && (
                        <Button
                          onClick={() => handleCopyKey(apiKey.key)}
                          variant="ghost"
                          size="sm"
                          className="ml-2 flex-shrink-0"
                        >
                          <Copy className="w-3 h-3" />
                        </Button>
                      )}
                    </div>
                  </div>

                  {/* 权限和过期时间 */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-gray-500">权限:</span>
                      {apiKey.permissions.map((permission) => (
                        <Badge
                          key={permission}
                          variant="secondary"
                          className={`text-xs ${getPermissionColor(permission)}`}
                        >
                          {permission}
                        </Badge>
                      ))}
                    </div>
                    {apiKey.expiresAt && (
                      <div className="flex items-center space-x-1 text-sm text-gray-500">
                        {isExpired ? (
                          <AlertCircle className="w-3 h-3 text-red-500" />
                        ) : (
                          <CheckCircle className="w-3 h-3 text-green-500" />
                        )}
                        <span>
                          {isExpired ? '已过期' : `${dayjs(apiKey.expiresAt).fromNow()}过期`}
                        </span>
                      </div>
                    )}
                  </div>
                </motion.div>
              )
            })
          )}
        </div>

        {/* 使用说明 */}
        <Separator />
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h4 className="text-sm font-medium text-blue-900 mb-2">使用说明</h4>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• API密钥用于访问AugmentAPI服务</li>
            <li>• 请妥善保管您的密钥，不要在公开场所分享</li>
            <li>• 建议为不同环境创建不同的密钥</li>
            <li>• 如果密钥泄露，请立即删除并创建新的密钥</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  )
}

export default ApiKeyManager
