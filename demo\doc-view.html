<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AugmentAPI - 文档查看</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>🧸</text></svg>">
    
    <!-- 引入Element Plus CSS - 本地资源 -->
    <link rel="stylesheet" href="/lib/element-plus/index.min.css">

    <!-- 引入Vue 3 - 本地资源 -->
    <script src="/lib/vue/vue.global.min.js"></script>
    <!-- 引入Element Plus - 本地资源 -->
    <script src="/lib/element-plus/index.full.min.js"></script>
    <!-- 引入Element Plus图标 - 本地资源 -->
    <script src="/lib/element-plus-icons/index.min.js"></script>

    <!-- 静默控制台（生产环境隐藏console输出；加 ?debug=1 显示） -->
    <script src="/js/quiet-console.js"></script>

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .layout-container {
            height: 100vh;
            display: flex;
        }

        /* 侧边栏样式 */
        .sidebar {
            background: #fff;
            box-shadow: 2px 0 6px rgba(0,21,41,.35);
            transition: width 0.3s;
            overflow: hidden;
            height: 100vh;
            position: relative;
            z-index: 10;
        }

        .sidebar.collapsed {
            width: 64px !important;
        }

        .sidebar.collapsed .user-info {
            justify-content: center;
            padding: 0;
        }

        .sidebar.collapsed .user-details {
            display: none;
        }

        .sidebar.collapsed .user-avatar {
            margin-right: 0;
            flex-shrink: 0;
        }

        .sidebar.collapsed .el-menu-item span,
        .sidebar.collapsed .el-sub-menu .el-sub-menu__title span {
            display: none;
        }

        .sidebar.collapsed .el-sub-menu .el-sub-menu__icon-arrow {
            display: none;
        }

        .sidebar.expanded {
            width: 250px !important;
        }

        .sidebar-toggle {
            position: absolute;
            top: 16px;
            right: -12px;
            width: 24px;
            height: 24px;
            background: #409eff;
            border: none;
            border-radius: 50%;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            z-index: 1000;
        }

        .sidebar-toggle:hover {
            background: #337ecc;
        }

        /* 用户信息样式 */
        .user-info {
            display: flex;
            align-items: center;
            padding: 16px;
            border-bottom: 1px solid #e8eaec;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin-right: 12px;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #409eff;
            color: white;
            font-weight: bold;
        }

        .user-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        /* 当有头像图片时，移除背景色 */
        .user-avatar:has(img) {
            background: transparent;
        }

        /* 公告铃铛按钮样式 */
        .bell-button {
            position: relative;
            padding: 8px 12px !important;
            border-radius: 50% !important;
            font-size: 18px !important;
            transition: all 0.3s ease !important;
            border: none !important;
        }

        .bell-button:hover {
            background-color: rgba(64, 158, 255, 0.1) !important;
            transform: scale(1.1);
        }

        .has-unread {
            position: relative;
            animation: bell-shake 2s infinite;
            color: #409eff !important;
        }

        .has-unread::after {
            content: '';
            position: absolute;
            top: 2px;
            right: 2px;
            width: 8px;
            height: 8px;
            background: #f56c6c;
            border-radius: 50%;
            border: 2px solid #fff;
            z-index: 1;
        }

        @keyframes bell-shake {
            0%, 50%, 100% { transform: rotate(0deg); }
            10%, 30% { transform: rotate(-10deg); }
            20%, 40% { transform: rotate(10deg); }
        }

        .user-details {
            flex: 1;
            min-width: 0;
        }

        .user-name {
            font-weight: 600;
            color: #303133;
            font-size: 14px;
            margin-bottom: 4px;
            display: flex;
            align-items: center;
        }

        .user-email {
            color: #909399;
            font-size: 12px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .trust-level {
            margin-left: 8px;
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 10px;
            font-weight: 600;
        }

        .trust-level-0 { background: #f4f4f5; color: #909399; }
        .trust-level-1 { background: #ecf5ff; color: #409eff; }
        .trust-level-2 { background: #f0f9ff; color: #67c23a; }
        .trust-level-3 { background: #fdf6ec; color: #e6a23c; }
        .trust-level-4 { background: #fef0f0; color: #f56c6c; }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .content-header {
            background: #fff;
            padding: 16px 24px;
            border-bottom: 1px solid #e8eaec;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .content-body {
            flex: 1;
            padding: 24px;
            overflow-y: auto;
            background: #f5f7fa;
        }

        .doc-card {
            background: white;
            border-radius: 12px;
            padding: 40px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            max-width: 1500px;
            margin-left: auto;
            margin-right: auto;
        }

        .doc-header {
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f0f0f0;
        }

        .doc-title {
            font-size: 32px;
            font-weight: 600;
            color: #303133;
            margin-bottom: 15px;
        }

        .doc-meta {
            display: flex;
            gap: 20px;
            color: #909399;
            font-size: 14px;
            flex-wrap: wrap;
        }

        .doc-meta-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        /* Markdown内容样式 */
        .markdown-content {
            line-height: 1.8;
            color: #606266;
        }

        .markdown-content h1 {
            font-size: 28px;
            font-weight: 600;
            color: #303133;
            margin: 30px 0 20px 0;
            padding-bottom: 10px;
            border-bottom: 2px solid #f0f0f0;
        }

        .markdown-content h2 {
            font-size: 24px;
            color: #409eff;
            margin: 25px 0 15px 0;
            padding-bottom: 8px;
            border-bottom: 1px solid #f0f0f0;
        }

        .markdown-content h3 {
            font-size: 20px;
            color: #606266;
            margin: 20px 0 10px 0;
        }

        .markdown-content h4 {
            font-size: 18px;
            color: #606266;
            margin: 15px 0 10px 0;
        }

        .markdown-content p {
            margin-bottom: 15px;
        }

        .markdown-content ul, .markdown-content ol {
            margin-left: 25px;
            margin-bottom: 15px;
        }

        .markdown-content li {
            margin-bottom: 8px;
        }

        .markdown-content code {
            background: #f5f7fa;
            border: 1px solid #e4e7ed;
            border-radius: 3px;
            padding: 2px 6px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 14px;
            color: #e6a23c;
        }

        .markdown-content pre {
            background: #f5f7fa;
            border: 1px solid #e4e7ed;
            border-radius: 6px;
            padding: 20px;
            margin: 15px 0;
            overflow-x: auto;
            font-size: 14px;
        }

        .markdown-content pre code {
            background: none;
            border: none;
            padding: 0;
            color: #303133;
        }

        .markdown-content blockquote {
            border-left: 4px solid #409eff;
            padding-left: 20px;
            margin: 20px 0;
            color: #606266;
            background: #f8f9fa;
            padding: 15px 20px;
            border-radius: 0 6px 6px 0;
        }

        .markdown-content table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }

        .markdown-content th, .markdown-content td {
            border: 1px solid #e4e7ed;
            padding: 12px 15px;
            text-align: left;
        }

        .markdown-content th {
            background: #f5f7fa;
            font-weight: 600;
        }

        .markdown-content img {
            max-width: 100%;
            height: auto;
            border-radius: 6px;
            margin: 15px 0;
        }

        .markdown-content a {
            color: #409eff;
            text-decoration: none;
        }

        .markdown-content a:hover {
            text-decoration: underline;
        }

        /* 移动端适配 */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }
            
            .sidebar.mobile-open {
                transform: translateX(0);
            }
            
            .main-content {
                width: 100%;
            }
            
            .content-body {
                /* padding: 20px 15px; */
                padding: 16px;
            }
            
            .doc-card {
                padding: 25px 20px;
            }

            .doc-title {
                font-size: 24px;
            }

            .doc-meta {
                flex-direction: column;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="layout-container">
            <!-- 侧边栏 -->
            <div class="sidebar" :class="{ 'collapsed': !sidebarVisible, 'expanded': sidebarVisible, 'mobile-open': mobileSidebarOpen }">
                <!-- 侧边栏切换按钮 -->
                <button class="sidebar-toggle" @click="toggleSidebar" v-if="!isMobile">
                    {{ sidebarVisible ? '‹' : '›' }}
                </button>

                <!-- 用户信息 -->
                <div class="user-info" v-if="currentUser">
                    <div class="user-avatar">
                        <img v-if="currentUser.linuxdo_avatar" :src="currentUser.linuxdo_avatar" :alt="currentUser.linuxdo_username">
                        <span v-else>{{ currentUser.email ? currentUser.email.charAt(0).toUpperCase() : '?' }}</span>
                    </div>
                    <div class="user-details">
                        <div class="user-name">
                            {{ currentUser.linuxdo_username || currentUser.email.split('@')[0] }}
                            <span v-if="currentUser.linuxdo_trust_level !== null"
                                  :class="`trust-level trust-level-${currentUser.linuxdo_trust_level}`">
                                {{ getTrustLevelName(currentUser.linuxdo_trust_level) }}
                            </span>
                        </div>
                        <div class="user-email">{{ currentUser.email }}</div>
                    </div>
                </div>
                
                <el-menu
                    default-active="docs"
                    :collapse="isCollapsed"
                    :unique-opened="true"
                    background-color="transparent"
                    text-color="#303133"
                    active-text-color="#409eff">
                    
                    <el-menu-item index="home" @click="navigateTo('/')">
                        <el-icon><i class="el-icon-house"></i></el-icon>
                        <span>首页</span>
                    </el-menu-item>
                    
                    <el-menu-item index="profile" @click="navigateTo('/profile')">
                        <el-icon><i class="el-icon-user"></i></el-icon>
                        <span>个人中心</span>
                    </el-menu-item>
                    
                    <el-sub-menu index="api">
                        <template #title>
                            <el-icon><i class="el-icon-document"></i></el-icon>
                            <span>API管理</span>
                        </template>
                        <el-menu-item index="api-docs" @click="navigateTo('/api-docs')">
                            <el-icon><i class="el-icon-reading"></i></el-icon>
                            <span>API文档</span>
                        </el-menu-item>
                        <el-menu-item index="external-test" @click="navigateTo('/external-test')">
                            <el-icon><i class="el-icon-connection"></i></el-icon>
                            <span>外部接口测试</span>
                        </el-menu-item>
                    </el-sub-menu>
                    
                    <el-menu-item index="docs" @click="navigateTo('/docs')">
                        <el-icon><i class="el-icon-folder"></i></el-icon>
                        <span>文档中心</span>
                    </el-menu-item>

                    <!-- 管理后台菜单项（仅管理员可见） -->
                    <el-menu-item v-if="currentUser && currentUser.isAdmin" index="admin" @click="navigateTo('/admin')">
                        <el-icon><i class="el-icon-setting"></i></el-icon>
                        <span>管理后台</span>
                    </el-menu-item>

                    <el-menu-item index="logout" @click="handleLogout">
                        <el-icon><i class="el-icon-switch-button"></i></el-icon>
                        <span>退出登录</span>
                    </el-menu-item>
                </el-menu>
            </div>

            <!-- 主内容区 -->
            <div class="main-content">
                <!-- 内容头部 -->
                <div class="content-header">
                    <div style="display: flex; align-items: center;">
                        <el-button v-if="isMobile" @click="toggleMobileSidebar" icon="el-icon-menu" circle></el-button>
                        <el-button @click="navigateTo('/docs')" size="small" text>
                            ←
                        </el-button>
                    </div>
                    <div>
                        <el-button @click="showAnnouncementDialog" :class="{ 'has-unread': hasUnreadAnnouncements, 'bell-button': true }" text>
                            🔔
                        </el-button>
                    </div>
                </div>
                
                <!-- 内容主体 -->
                <div class="content-body">
                    <div class="doc-card" v-loading="loading">
                        <div v-if="document.title" class="doc-header">
                            <h1 class="doc-title">{{ document.title }}</h1>
                            <div class="doc-meta">
                                <div class="doc-meta-item">
                                    <el-icon><User /></el-icon>
                                    <span>{{ document.author_name }}</span>
                                </div>
                                <div class="doc-meta-item">
                                    <el-icon><Calendar /></el-icon>
                                    <span>{{ formatDate(document.created_at) }}</span>
                                </div>
                                <div class="doc-meta-item">
                                    <el-icon><Edit /></el-icon>
                                    <span>{{ formatDate(document.updated_at) }}</span>
                                </div>
                                <div class="doc-meta-item">
                                    <el-tag :type="getCategoryColor(document.category)" size="small">
                                        {{ getCategoryText(document.category) }}
                                    </el-tag>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 文档内容 -->
                        <div v-if="document.html" v-html="document.html" class="markdown-content"></div>
                        
                        <!-- 空状态 -->
                        <div v-else-if="!loading" style="text-align: center; padding: 40px; color: #909399;">
                            <el-icon size="48"><Document /></el-icon>
                            <p style="margin-top: 16px;">文档不存在或已被删除</p>
                            <el-button @click="navigateTo('/docs')" type="primary" style="margin-top: 16px;">
                                返回文档中心
                            </el-button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 系统公告弹窗 -->
        <el-dialog
            v-model="announcementDialogVisible"
            width="500px"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
            :show-close="true"
            class="announcement-dialog">

            <template #header>
                <span style="font-size: 18px; font-weight: 600;">系统公告</span>
            </template>

            <div v-if="currentAnnouncement" class="announcement-dialog-content">
                <div class="announcement-header">
                    <div class="announcement-title">
                        <el-tag v-if="currentAnnouncement.is_pinned" type="warning" size="small" style="margin-right: 8px;">置顶</el-tag>
                        <el-tag :type="getAnnouncementTypeColor(currentAnnouncement.type)" size="small" style="margin-right: 8px;">
                            {{ getAnnouncementTypeText(currentAnnouncement.type) }}
                        </el-tag>
                        <span style="font-size: 16px; font-weight: 600;">{{ currentAnnouncement.title }}</span>
                    </div>
                    <div class="announcement-meta" style="margin-top: 8px; color: #909399; font-size: 12px;">
                        发布时间：{{ formatDate(currentAnnouncement.created_at) }}
                        <span v-if="currentAnnouncement.author_name" style="margin-left: 16px;">
                            发布人：{{ currentAnnouncement.author_name }}
                        </span>
                    </div>
                    <el-divider />
                </div>
                <div class="announcement-content" style="line-height: 1.8; white-space: pre-wrap;">
                    {{ currentAnnouncement.content }}
                </div>
            </div>

            <template #footer>
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <div style="color: #909399; font-size: 12px;">
                        {{ currentAnnouncementIndex + 1 }} / {{ announcements.length }}
                    </div>
                    <div>
                        <el-button type="primary" @click="announcementDialogVisible = false">
                            知道了
                        </el-button>
                    </div>
                </div>
            </template>
        </el-dialog>
    </div>

    <script>
        const { createApp, ref, reactive, computed, onMounted } = Vue;
        const { ElMessage } = ElementPlus;

        createApp({
            setup() {
                // 响应式数据
                const sidebarVisible = ref(true);
                const mobileSidebarOpen = ref(false);
                const isMobile = ref(window.innerWidth <= 768);
                const loading = ref(true);
                const document = ref({});

                // 用户信息
                const currentUser = ref(null);

                // 通知相关
                const announcements = ref([]);
                const unreadCount = ref(0);
                const announcementDialogVisible = ref(false);
                const currentAnnouncement = ref(null);
                const currentAnnouncementIndex = ref(0);
                const hasUnreadAnnouncements = ref(false);

                // 方法
                const navigateTo = (path) => {
                    window.location.href = path;
                };

                const toggleMobileSidebar = () => {
                    mobileSidebarOpen.value = !mobileSidebarOpen.value;
                };

                const toggleSidebar = () => {
                    sidebarVisible.value = !sidebarVisible.value;
                };

                // 检查登录状态并加载用户信息
                // 可选登录检测：尝试获取当前用户，但不强制登录
                const checkLoginStatus = async () => {
                    try {
                        const response = await fetch('/api/user');
                        if (response.ok) {
                            const result = await response.json();
                            currentUser.value = result.data || result.user || null;
                            return true;
                        }
                    } catch (error) {
                        console.warn('可选登录检测失败（忽略）:', error);
                    }
                    return false;
                };

                // 加载通知
                const loadAnnouncements = async () => {
                    try {
                        const response = await fetch('/api/announcements/active');

                        if (response.ok) {
                            const data = await response.json();
                            announcements.value = data.data || [];
                            unreadCount.value = announcements.value.length;
                            hasUnreadAnnouncements.value = announcements.value.length > 0;
                        }
                    } catch (error) {
                        console.error('加载通知失败:', error);
                    }
                };

                const showAnnouncementDialog = () => {
                    if (announcements.value.length > 0) {
                        currentAnnouncementIndex.value = 0;
                        currentAnnouncement.value = announcements.value[0];
                        announcementDialogVisible.value = true;
                        hasUnreadAnnouncements.value = false;
                    } else {
                        ElMessage.info('暂无公告');
                    }
                };

                const loadDocument = async () => {
                    try {
                        loading.value = true;
                        
                        // 从URL获取slug
                        const slug = window.location.pathname.split('/').pop();
                        
                        const response = await fetch(`/api/documents/${slug}`);
                        
                        if (response.ok) {
                            const data = await response.json();
                            document.value = data.data || {};
                            
                            // 更新页面标题
                            if (document.value.title) {
                                document.title = `${document.value.title} - AugmentAPI`;
                            }
                        } else {
                            ElMessage.error('文档不存在');
                        }
                    } catch (error) {
                        console.error('加载文档失败:', error);
                        ElMessage.error('网络错误，无法加载文档');
                    } finally {
                        loading.value = false;
                    }
                };

                const formatDate = (dateString) => {
                    return new Date(dateString).toLocaleDateString('zh-CN');
                };

                const getCategoryColor = (category) => {
                    const colors = {
                        guide: 'success',
                        api: 'primary',
                        faq: 'warning',
                        general: ''
                    };
                    return colors[category] || '';
                };

                const getCategoryText = (category) => {
                    const texts = {
                        guide: '用户指南',
                        api: 'API文档',
                        faq: '常见问题',
                        general: '其他'
                    };
                    return texts[category] || category;
                };

                const handleLogout = async () => {
                    try {
                        const response = await fetch('/api/logout', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            }
                        });

                        if (response.ok) {
                            ElMessage.success('退出登录成功');
                            setTimeout(() => {
                                window.location.href = '/login';
                            }, 1000);
                        } else {
                            throw new Error('退出登录失败');
                        }
                    } catch (error) {
                        console.error('退出登录失败:', error);
                        ElMessage.error('退出登录失败');
                    }
                };

                const handleResize = () => {
                    isMobile.value = window.innerWidth <= 768;
                    if (!isMobile.value) {
                        mobileSidebarOpen.value = false;
                    }
                };

                onMounted(async () => {
                    window.addEventListener('resize', handleResize);

                    // 可选：尝试加载用户，不强制登录
                    await checkLoginStatus();

                    loadAnnouncements();
                    loadDocument();
                });

                // 获取信任等级名称
                const getTrustLevelName = (level) => {
                    const levels = {
                        0: '新手',
                        1: '基础',
                        2: '成员',
                        3: '常客',
                        4: '领袖'
                    };
                    return levels[level] || '未知';
                };

                return {
                    sidebarVisible,
                    mobileSidebarOpen,
                    isMobile,
                    loading,
                    document,
                    currentUser,
                    announcements,
                    unreadCount,
                    navigateTo,
                    toggleMobileSidebar,
                    toggleSidebar,
                    checkLoginStatus,
                    loadAnnouncements,
                    loadDocument,
                    formatDate,
                    getCategoryColor,
                    getCategoryText,
                    getTrustLevelName,
                    handleLogout,
                    // 公告相关
                    announcementDialogVisible,
                    currentAnnouncement,
                    currentAnnouncementIndex,
                    hasUnreadAnnouncements,
                    showAnnouncementDialog,
                    getAnnouncementTypeColor: (type) => {
                        const colors = { info: '', warning: 'warning', success: 'success', error: 'danger' };
                        return colors[type] || '';
                    },
                    getAnnouncementTypeText: (type) => {
                        const texts = { info: '信息', warning: '警告', success: '成功', error: '错误' };
                        return texts[type] || type;
                    },
                    formatDate: (dateString) => new Date(dateString).toLocaleDateString('zh-CN')
                };
            }
        }).use(ElementPlus).mount('#app');
    </script>
</body>
</html>
