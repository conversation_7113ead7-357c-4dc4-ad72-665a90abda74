import React from 'react'
import { motion } from 'framer-motion'
import { Crown, Medal, Award, Heart } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { cn } from '@/lib/utils'
import type { SponsorRecord } from '@/types'

interface SponsorRankingProps {
  sponsors: SponsorRecord[]
  loading?: boolean
}

const getRankIcon = (rank: number) => {
  switch (rank) {
    case 1:
      return { icon: Crown, color: 'text-yellow-500' }
    case 2:
      return { icon: Medal, color: 'text-gray-400' }
    case 3:
      return { icon: Award, color: 'text-orange-500' }
    default:
      return { icon: Heart, color: 'text-red-500' }
  }
}

const getRankBadgeColor = (rank: number) => {
  switch (rank) {
    case 1:
      return 'bg-gradient-to-r from-yellow-400 to-yellow-600 text-white'
    case 2:
      return 'bg-gradient-to-r from-gray-300 to-gray-500 text-white'
    case 3:
      return 'bg-gradient-to-r from-orange-400 to-orange-600 text-white'
    default:
      return 'bg-gray-100 text-gray-700'
  }
}

const SponsorRanking: React.FC<SponsorRankingProps> = ({ 
  sponsors, 
  loading = false 
}) => {
  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Heart className="w-5 h-5 text-red-500" />
            <span>赞助排行榜</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[1, 2, 3, 4, 5].map((i) => (
              <div key={i} className="flex items-center space-x-3 animate-pulse">
                <div className="w-10 h-10 bg-gray-200 rounded-full" />
                <div className="flex-1">
                  <div className="h-4 bg-gray-200 rounded w-24 mb-1" />
                  <div className="h-3 bg-gray-200 rounded w-16" />
                </div>
                <div className="h-6 bg-gray-200 rounded w-12" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Heart className="w-5 h-5 text-red-500" />
          <span>赞助排行榜</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {sponsors.length === 0 ? (
          <div className="text-center py-8">
            <Heart className="w-12 h-12 text-gray-300 mx-auto mb-3" />
            <p className="text-gray-500">暂无赞助记录</p>
            <p className="text-sm text-gray-400 mt-1">
              成为第一个支持我们的用户吧！
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {sponsors.map((sponsor, index) => {
              const { icon: RankIcon, color } = getRankIcon(sponsor.rank)
              
              return (
                <motion.div
                  key={sponsor.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  {/* 排名图标 */}
                  <div className="relative">
                    <Avatar className="w-10 h-10">
                      <AvatarImage src={sponsor.avatar} />
                      <AvatarFallback>
                        {sponsor.username[0]?.toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    <div className="absolute -top-1 -right-1">
                      <RankIcon className={cn("w-4 h-4", color)} />
                    </div>
                  </div>

                  {/* 用户信息 */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {sponsor.username}
                      </p>
                      <Badge 
                        className={cn(
                          "text-xs px-2 py-0.5",
                          getRankBadgeColor(sponsor.rank)
                        )}
                      >
                        #{sponsor.rank}
                      </Badge>
                    </div>
                    <p className="text-xs text-gray-500">
                      {sponsor.sponsor_count} 次赞助
                    </p>
                  </div>

                  {/* 金额 */}
                  <div className="text-right">
                    <p className="text-sm font-bold text-gray-900">
                      ¥{sponsor.amount.toFixed(2)}
                    </p>
                  </div>
                </motion.div>
              )
            })}
          </div>
        )}

        {/* 底部提示 */}
        <div className="mt-6 pt-4 border-t border-gray-100">
          <div className="text-center">
            <p className="text-xs text-gray-500 mb-2">
              感谢所有支持者的慷慨赞助 ❤️
            </p>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="text-xs text-blue-600 hover:text-blue-700 font-medium"
            >
              我也要赞助
            </motion.button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default SponsorRanking
