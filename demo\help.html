<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AugmentAPI - 帮助文档</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>🧸</text></svg>">

    <!-- 引入Element Plus CSS - 本地资源 -->
    <link rel="stylesheet" href="/lib/element-plus/index.min.css">

    <!-- 引入Vue 3 - 本地资源 -->
    <script src="/lib/vue/vue.global.min.js"></script>
    <!-- 引入Element Plus - 本地资源 -->
    <script src="/lib/element-plus/index.full.min.js"></script>
    <!-- 引入Element Plus图标 - 本地资源 -->
    <script src="/lib/element-plus-icons/index.min.js"></script>

    <!-- 静默控制台（生产环境隐藏console输出；加 ?debug=1 显示） -->
    <script src="/js/quiet-console.js"></script>

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .app-container {
            display: flex;
            min-height: 100vh;
        }

        /* 侧边栏样式 */
        .sidebar {
            width: 250px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-right: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            position: fixed;
            height: 100vh;
            z-index: 1000;
            overflow-y: auto;
        }

        .sidebar.collapsed {
            transform: translateX(-100%);
        }

        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #409eff;
            text-decoration: none;
        }

        .main-content {
            flex: 1;
            margin-left: 250px;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            min-height: 100vh;
        }

        .main-content.expanded {
            margin-left: 0;
        }

        /* 公告铃铛按钮样式 */
        .bell-button {
            position: relative;
            padding: 8px 12px !important;
            border-radius: 50% !important;
            font-size: 18px !important;
            transition: all 0.3s ease !important;
            border: none !important;
        }

        .bell-button:hover {
            background-color: rgba(64, 158, 255, 0.1) !important;
            transform: scale(1.1);
        }

        .has-unread {
            position: relative;
            animation: bell-shake 2s infinite;
            color: #409eff !important;
        }

        .has-unread::after {
            content: '';
            position: absolute;
            top: 2px;
            right: 2px;
            width: 8px;
            height: 8px;
            background: #f56c6c;
            border-radius: 50%;
            border: 2px solid #fff;
            z-index: 1;
        }

        @keyframes bell-shake {
            0%, 50%, 100% { transform: rotate(0deg); }
            10%, 30% { transform: rotate(-10deg); }
            20%, 40% { transform: rotate(10deg); }
        }
        .content-header {
            padding: 20px 30px;
            background: rgba(255, 255, 255, 0.95);
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
            backdrop-filter: blur(10px);
        }

        .content-body {
            padding: 30px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .help-card {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        .help-title {
            font-size: 28px;
            font-weight: 600;
            color: #303133;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .help-section {
            margin-bottom: 30px;
        }

        .help-section h2 {
            font-size: 20px;
            color: #409eff;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 2px solid #f0f0f0;
        }

        .help-section h3 {
            font-size: 16px;
            color: #606266;
            margin: 15px 0 10px 0;
        }

        .help-section p {
            line-height: 1.6;
            color: #606266;
            margin-bottom: 10px;
        }

        .help-section ul {
            margin-left: 20px;
            margin-bottom: 15px;
        }

        .help-section li {
            line-height: 1.6;
            color: #606266;
            margin-bottom: 5px;
        }

        .code-block {
            background: #f5f7fa;
            border: 1px solid #e4e7ed;
            border-radius: 6px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 14px;
            overflow-x: auto;
        }

        .highlight {
            background: #fff3cd;
            padding: 2px 4px;
            border-radius: 3px;
            color: #856404;
        }

        .warning-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
            color: #856404;
        }

        .info-box {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
            color: #0c5460;
        }

        /* Markdown内容样式 */
        .markdown-content {
            line-height: 1.6;
            color: #606266;
        }

        .markdown-content h1 {
            font-size: 28px;
            font-weight: 600;
            color: #303133;
            margin: 20px 0 15px 0;
            padding-bottom: 8px;
            border-bottom: 2px solid #f0f0f0;
        }

        .markdown-content h2 {
            font-size: 20px;
            color: #409eff;
            margin: 20px 0 15px 0;
            padding-bottom: 8px;
            border-bottom: 2px solid #f0f0f0;
        }

        .markdown-content h3 {
            font-size: 16px;
            color: #606266;
            margin: 15px 0 10px 0;
        }

        .markdown-content p {
            margin-bottom: 10px;
        }

        .markdown-content ul, .markdown-content ol {
            margin-left: 20px;
            margin-bottom: 15px;
        }

        .markdown-content li {
            margin-bottom: 5px;
        }

        .markdown-content code {
            background: #f5f7fa;
            border: 1px solid #e4e7ed;
            border-radius: 3px;
            padding: 2px 4px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 14px;
        }

        .markdown-content pre {
            background: #f5f7fa;
            border: 1px solid #e4e7ed;
            border-radius: 6px;
            padding: 15px;
            margin: 10px 0;
            overflow-x: auto;
        }

        .markdown-content pre code {
            background: none;
            border: none;
            padding: 0;
        }

        .markdown-content blockquote {
            border-left: 4px solid #409eff;
            padding-left: 15px;
            margin: 15px 0;
            color: #606266;
            background: #f8f9fa;
            padding: 10px 15px;
            border-radius: 0 6px 6px 0;
        }

        .markdown-content table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }

        .markdown-content th, .markdown-content td {
            border: 1px solid #e4e7ed;
            padding: 8px 12px;
            text-align: left;
        }

        .markdown-content th {
            background: #f5f7fa;
            font-weight: 600;
        }

        /* 移动端适配 */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }

            .sidebar.mobile-open {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .content-body {
                padding: 20px 15px;
            }

            .help-card {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="app-container">
            <!-- 侧边栏 -->
            <div class="sidebar" :class="{ 'collapsed': !sidebarVisible, 'mobile-open': mobileSidebarOpen }">
                <div class="sidebar-header">
                    <a href="/" class="logo">🧸 AugmentAPI</a>
                </div>

                <el-menu
                    default-active="help"
                    background-color="transparent"
                    text-color="#303133"
                    active-text-color="#409eff">

                    <el-menu-item index="home" @click="navigateTo('/')">
                        <el-icon><i class="el-icon-house"></i></el-icon>
                        <span>首页</span>
                    </el-menu-item>

                    <el-menu-item index="profile" @click="navigateTo('/profile')">
                        <el-icon><i class="el-icon-user"></i></el-icon>
                        <span>个人中心</span>
                    </el-menu-item>

                    <el-menu-item index="chat" @click="navigateTo('/chat')">
                        <el-icon><i class="el-icon-chat-dot-round"></i></el-icon>
                        <span>实时聊天</span>
                    </el-menu-item>

                    <el-sub-menu index="api">
                        <template #title>
                            <el-icon><i class="el-icon-document"></i></el-icon>
                            <span>API管理</span>
                        </template>
                        <el-menu-item index="api-docs" @click="navigateTo('/api-docs')">
                            <el-icon><i class="el-icon-reading"></i></el-icon>
                            <span>API文档</span>
                        </el-menu-item>
                        <el-menu-item index="external-test" @click="navigateTo('/external-test')">
                            <el-icon><i class="el-icon-connection"></i></el-icon>
                            <span>外部接口测试</span>
                        </el-menu-item>
                    </el-sub-menu>

                    <el-menu-item index="help" @click="navigateTo('/help')">
                        <el-icon><i class="el-icon-question"></i></el-icon>
                        <span>帮助文档</span>
                    </el-menu-item>

                    <el-menu-item index="logout" @click="handleLogout">
                        <el-icon><i class="el-icon-switch-button"></i></el-icon>
                        <span>退出登录</span>
                    </el-menu-item>
                </el-menu>
            </div>

            <!-- 主内容区 -->
            <div class="main-content" :class="{ 'expanded': !sidebarVisible }">
                <!-- 内容头部 -->
                <div class="content-header">
                    <div>
                        <el-button v-if="isMobile" @click="toggleMobileSidebar" icon="el-icon-menu" circle></el-button>
                        <span style="font-size: 18px; font-weight: 600; margin-left: 8px;">帮助文档</span>
                    </div>
                    <div>
                        <el-button @click="showAnnouncementDialog" :class="{ 'has-unread': hasUnreadAnnouncements, 'bell-button': true }" text>
                            🔔
                        </el-button>
                        <el-button @click="navigateTo('/')" type="primary" size="small">
                            返回首页
                        </el-button>
                    </div>
                </div>

                <!-- 内容主体 -->
                <div class="content-body">
                    <div class="help-card" v-loading="loading">
                        <div class="help-title">
                            📚 AugmentAPI 帮助文档
                        </div>

                        <!-- 动态渲染的Markdown内容 -->
                        <div v-html="helpContent" class="markdown-content"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 系统公告弹窗 -->
        <el-dialog
            v-model="announcementDialogVisible"
            width="500px"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
            :show-close="true"
            class="announcement-dialog">

            <template #header>
                <span style="font-size: 18px; font-weight: 600;">系统公告</span>
            </template>

            <div v-if="currentAnnouncement" class="announcement-dialog-content">
                <div class="announcement-header">
                    <div class="announcement-title">
                        <el-tag v-if="currentAnnouncement.is_pinned" type="warning" size="small" style="margin-right: 8px;">置顶</el-tag>
                        <el-tag :type="getAnnouncementTypeColor(currentAnnouncement.type)" size="small" style="margin-right: 8px;">
                            {{ getAnnouncementTypeText(currentAnnouncement.type) }}
                        </el-tag>
                        <span style="font-size: 16px; font-weight: 600;">{{ currentAnnouncement.title }}</span>
                    </div>
                    <div class="announcement-meta" style="margin-top: 8px; color: #909399; font-size: 12px;">
                        发布时间：{{ formatDate(currentAnnouncement.created_at) }}
                        <span v-if="currentAnnouncement.author_name" style="margin-left: 16px;">
                            发布人：{{ currentAnnouncement.author_name }}
                        </span>
                    </div>
                    <el-divider />
                </div>
                <div class="announcement-content" style="line-height: 1.8; white-space: pre-wrap;">
                    {{ currentAnnouncement.content }}
                </div>
            </div>

            <template #footer>
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <div style="color: #909399; font-size: 12px;">
                        {{ currentAnnouncementIndex + 1 }} / {{ announcements.length }}
                    </div>
                    <div>
                        <el-button type="primary" @click="announcementDialogVisible = false">
                            知道了
                        </el-button>
                    </div>
                </div>
            </template>
        </el-dialog>
    </div>

    <script>
        const { createApp, ref, reactive, computed, onMounted } = Vue;
        const { ElMessage } = ElementPlus;

        createApp({
            setup() {
                // 响应式数据
                const sidebarVisible = ref(true);
                const mobileSidebarOpen = ref(false);
                const isMobile = ref(window.innerWidth <= 768);
                const loading = ref(true);
                const helpContent = ref('');

                // 公告数据
                const announcements = ref([]);
                const announcementDialogVisible = ref(false);
                const currentAnnouncement = ref(null);
                const currentAnnouncementIndex = ref(0);
                const hasUnreadAnnouncements = ref(false);

                // 导航函数
                const navigateTo = (path) => {
                    window.location.href = path;
                };

                // 切换移动端侧边栏
                const toggleMobileSidebar = () => {
                    mobileSidebarOpen.value = !mobileSidebarOpen.value;
                };

                // 加载帮助文档
                const loadHelpDoc = async () => {
                    try {
                        loading.value = true;
                        const response = await fetch('/api/admin/docs/help/render', {
                            headers: {
                                'Authorization': `Bearer ${localStorage.getItem('auth_token')}`
                            }
                        });

                        if (response.ok) {
                            const data = await response.json();
                            helpContent.value = data.html || data.content || '暂无帮助文档内容';
                        } else {
                            helpContent.value = '加载帮助文档失败，请稍后重试';
                        }
                    } catch (error) {
                        console.error('加载帮助文档失败:', error);
                        helpContent.value = '网络错误，无法加载帮助文档';
                    } finally {
                        loading.value = false;
                    }
                };

                // 退出登录
                const handleLogout = async () => {
                    try {
                        const response = await fetch('/api/logout', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            }
                        });

                        if (response.ok) {
                            ElMessage.success('退出登录成功');
                            setTimeout(() => {
                                window.location.href = '/login';
                            }, 1000);
                        } else {
                            throw new Error('退出登录失败');
                        }
                    } catch (error) {
                        console.error('退出登录失败:', error);
                        ElMessage.error('退出登录失败');
                    }
                };

                // 监听窗口大小变化
                const handleResize = () => {
                    isMobile.value = window.innerWidth <= 768;
                    if (!isMobile.value) {
                        mobileSidebarOpen.value = false;
                    }
                };

                // 公告相关方法
                const showAnnouncementDialog = () => {
                    if (announcements.value.length > 0) {
                        currentAnnouncementIndex.value = 0;
                        currentAnnouncement.value = announcements.value[0];
                        announcementDialogVisible.value = true;
                        hasUnreadAnnouncements.value = false;
                    } else {
                        ElMessage.info('暂无公告');
                    }
                };

                const loadAnnouncements = async () => {
                    try {
                        const response = await fetch('/api/announcements/active');
                        if (response.ok) {
                            const data = await response.json();
                            announcements.value = data.data || [];
                            hasUnreadAnnouncements.value = announcements.value.length > 0;
                        }
                    } catch (error) {
                        console.error('加载公告失败:', error);
                    }
                };

                onMounted(() => {
                    window.addEventListener('resize', handleResize);
                    loadHelpDoc(); // 加载帮助文档
                    loadAnnouncements();
                });

                return {
                    sidebarVisible,
                    mobileSidebarOpen,
                    isMobile,
                    loading,
                    helpContent,
                    navigateTo,
                    toggleMobileSidebar,
                    handleLogout,
                    loadHelpDoc,
                    // 公告相关
                    announcements,
                    announcementDialogVisible,
                    currentAnnouncement,
                    currentAnnouncementIndex,
                    hasUnreadAnnouncements,
                    showAnnouncementDialog,
                    loadAnnouncements,
                    getAnnouncementTypeColor: (type) => {
                        const colors = { info: '', warning: 'warning', success: 'success', error: 'danger' };
                        return colors[type] || '';
                    },
                    getAnnouncementTypeText: (type) => {
                        const texts = { info: '信息', warning: '警告', success: '成功', error: '错误' };
                        return texts[type] || type;
                    },
                    formatDate: (dateString) => new Date(dateString).toLocaleDateString('zh-CN')
                };
            }
        }).use(ElementPlus).mount('#app');
    </script>
</body>
</html>
