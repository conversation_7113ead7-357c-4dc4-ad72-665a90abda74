<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />

    <!-- 基础meta标签 -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover" />
    <meta name="description" content="AugmentAPI - 现代化的API管理和开发平台，提供完整的API生命周期管理解决方案" />
    <meta name="keywords" content="API,管理,开发,文档,测试,聊天,下载" />
    <meta name="author" content="AugmentAPI Team" />

    <!-- PWA相关 -->
    <link rel="manifest" href="/manifest.json" />
    <meta name="theme-color" content="#3b82f6" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="apple-mobile-web-app-title" content="AugmentAPI" />
    <meta name="mobile-web-app-capable" content="yes" />

    <!-- iOS图标 -->
    <link rel="apple-touch-icon" sizes="180x180" href="/icons/apple-touch-icon.png" />
    <link rel="apple-touch-icon" sizes="152x152" href="/icons/icon-152x152.png" />
    <link rel="apple-touch-icon" sizes="144x144" href="/icons/icon-144x144.png" />
    <link rel="apple-touch-icon" sizes="120x120" href="/icons/icon-120x120.png" />

    <!-- 标准图标 -->
    <link rel="icon" type="image/png" sizes="32x32" href="/icons/favicon-32x32.png" />
    <link rel="icon" type="image/png" sizes="16x16" href="/icons/favicon-16x16.png" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />

    <!-- 安全区域支持（刘海屏等） -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover" />
    <style>
      :root {
        --safe-area-inset-top: env(safe-area-inset-top);
        --safe-area-inset-right: env(safe-area-inset-right);
        --safe-area-inset-bottom: env(safe-area-inset-bottom);
        --safe-area-inset-left: env(safe-area-inset-left);
      }

      /* 防止移动端缩放 */
      * {
        -webkit-touch-callout: none;
        -webkit-user-select: none;
        -khtml-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
      }

      input, textarea {
        -webkit-user-select: text;
        -moz-user-select: text;
        -ms-user-select: text;
        user-select: text;
      }

      /* 移动端优化 */
      body {
        -webkit-overflow-scrolling: touch;
        overscroll-behavior: none;
      }

      /* 防止iOS Safari的橡皮筋效果 */
      html, body {
        height: 100%;
        overflow: hidden;
      }

      #root {
        height: 100vh;
        overflow: auto;
      }
    </style>

    <title>AugmentAPI - 智能API管理平台</title>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
