/**
 * 认证工具函数
 * 统一处理Cookie中的认证token
 */

// 获取Cookie值的通用函数
function getCookie(name) {
    const cookies = document.cookie.split(';');
    for (let cookie of cookies) {
        const [cookieName, cookieValue] = cookie.trim().split('=');
        if (cookieName === name) {
            const decodedValue = decodeURIComponent(cookieValue);
            return decodedValue;
        }
    }
    return null;
}

// 设置Cookie的通用函数
function setCookie(name, value, days = 1) {
    const expires = new Date();
    expires.setTime(expires.getTime() + (days * 24 * 60 * 60 * 1000));
    document.cookie = `${name}=${encodeURIComponent(value)}; expires=${expires.toUTCString()}; path=/; SameSite=Lax`;
}

// 删除Cookie的通用函数
function deleteCookie(name) {
    document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
}

// 获取认证token
function getAuthToken() {
    return getCookie('auth_token');
}

// 设置认证token
function setAuthToken(token, days = 1) {
    setCookie('auth_token', token, days);
}

// 清除认证token
function clearAuthToken() {
    deleteCookie('auth_token');
}

// 检查是否已登录
function isLoggedIn() {
    return !!getAuthToken();
}

// 通用的认证请求函数（支持自动token刷新）
async function authenticatedFetch(url, options = {}) {
    let token = getAuthToken();
    if (!token) {
        throw new Error('未找到认证token');
    }

    const defaultHeaders = {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
    };

    const mergedOptions = {
        ...options,
        headers: {
            ...defaultHeaders,
            ...options.headers
        }
    };

    let response = await fetch(url, mergedOptions);

    // 如果token过期，尝试刷新
    if (response.status === 401) {
        try {
            const refreshResponse = await fetch('/api/refresh-token', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (refreshResponse.ok) {
                const refreshResult = await refreshResponse.json();
                if (refreshResult.success) {
                    setAuthToken(refreshResult.token);
                    mergedOptions.headers['Authorization'] = `Bearer ${refreshResult.token}`;
                    response = await fetch(url, mergedOptions);

                    if (response.ok) {
                        return response;
                    }
                }
            }
        } catch (refreshError) {
            // Token刷新失败
        }

        clearAuthToken();
        window.location.href = '/login';
        throw new Error('认证失败');
    }

    return response;
}

// 登出函数
async function logout() {
    try {
        await authenticatedFetch('/api/logout', { method: 'POST' });
    } catch (error) {
        // 登出失败
    } finally {
        clearAuthToken();
        window.location.href = '/login';
    }
}

// 导出函数（如果在模块环境中）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        getCookie,
        setCookie,
        deleteCookie,
        getAuthToken,
        setAuthToken,
        clearAuthToken,
        isLoggedIn,
        authenticatedFetch,
        logout
    };
}

// 全局暴露（用于直接在HTML中使用）
if (typeof window !== 'undefined') {
    window.AuthUtils = {
        getCookie,
        setCookie,
        deleteCookie,
        getAuthToken,
        setAuthToken,
        clearAuthToken,
        isLoggedIn,
        authenticatedFetch,
        logout
    };
}
