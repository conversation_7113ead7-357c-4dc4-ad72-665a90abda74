import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import type { ChatMessage } from '@/types'

interface OnlineUser {
  id: string
  username: string
  avatar?: string
  joinedAt: string
}

interface ChatState {
  // 消息相关
  messages: ChatMessage[]
  isLoading: boolean
  error: string | null
  
  // 连接状态
  connected: boolean
  connecting: boolean
  
  // 在线用户
  onlineUsers: OnlineUser[]
  onlineCount: number
  
  // 输入状态
  inputValue: string
  isTyping: boolean
  typingUsers: string[]
  
  // UI状态
  autoScroll: boolean
  showEmojiPicker: boolean
  showUserList: boolean
}

interface ChatStore extends ChatState {
  // 消息操作
  addMessage: (message: ChatMessage) => void
  addMessages: (messages: ChatMessage[]) => void
  clearMessages: () => void
  
  // 连接状态
  setConnected: (connected: boolean) => void
  setConnecting: (connecting: boolean) => void
  
  // 在线用户
  setOnlineUsers: (users: OnlineUser[]) => void
  setOnlineCount: (count: number) => void
  addOnlineUser: (user: OnlineUser) => void
  removeOnlineUser: (userId: string) => void
  
  // 输入状态
  setInputValue: (value: string) => void
  setIsTyping: (typing: boolean) => void
  setTypingUsers: (users: string[]) => void
  addTypingUser: (username: string) => void
  removeTypingUser: (username: string) => void
  
  // UI状态
  setAutoScroll: (autoScroll: boolean) => void
  setShowEmojiPicker: (show: boolean) => void
  setShowUserList: (show: boolean) => void
  
  // 错误处理
  setError: (error: string | null) => void
  setLoading: (loading: boolean) => void
  
  // 重置状态
  reset: () => void
}

const initialState: ChatState = {
  messages: [],
  isLoading: false,
  error: null,
  
  connected: false,
  connecting: false,
  
  onlineUsers: [],
  onlineCount: 0,
  
  inputValue: '',
  isTyping: false,
  typingUsers: [],
  
  autoScroll: true,
  showEmojiPicker: false,
  showUserList: false
}

export const useChatStore = create<ChatStore>()(
  devtools(
    (set, get) => ({
      ...initialState,

      // 消息操作
      addMessage: (message) => {
        set((state) => ({
          messages: [...state.messages, message]
        }), false, 'addMessage')
      },

      addMessages: (messages) => {
        set((state) => ({
          messages: [...state.messages, ...messages]
        }), false, 'addMessages')
      },

      clearMessages: () => {
        set({ messages: [] }, false, 'clearMessages')
      },

      // 连接状态
      setConnected: (connected) => {
        set({ connected, connecting: false }, false, 'setConnected')
      },

      setConnecting: (connecting) => {
        set({ connecting }, false, 'setConnecting')
      },

      // 在线用户
      setOnlineUsers: (users) => {
        set({ 
          onlineUsers: users,
          onlineCount: users.length 
        }, false, 'setOnlineUsers')
      },

      setOnlineCount: (count) => {
        set({ onlineCount: count }, false, 'setOnlineCount')
      },

      addOnlineUser: (user) => {
        set((state) => {
          const exists = state.onlineUsers.find(u => u.id === user.id)
          if (exists) return state

          return {
            onlineUsers: [...state.onlineUsers, user],
            onlineCount: state.onlineCount + 1
          }
        }, false, 'addOnlineUser')
      },

      removeOnlineUser: (userId) => {
        set((state) => ({
          onlineUsers: state.onlineUsers.filter(u => u.id !== userId),
          onlineCount: Math.max(0, state.onlineCount - 1)
        }), false, 'removeOnlineUser')
      },

      // 输入状态
      setInputValue: (value) => {
        set({ inputValue: value }, false, 'setInputValue')
      },

      setIsTyping: (typing) => {
        set({ isTyping: typing }, false, 'setIsTyping')
      },

      setTypingUsers: (users) => {
        set({ typingUsers: users }, false, 'setTypingUsers')
      },

      addTypingUser: (username) => {
        set((state) => {
          if (state.typingUsers.includes(username)) return state
          return {
            typingUsers: [...state.typingUsers, username]
          }
        }, false, 'addTypingUser')
      },

      removeTypingUser: (username) => {
        set((state) => ({
          typingUsers: state.typingUsers.filter(u => u !== username)
        }), false, 'removeTypingUser')
      },

      // UI状态
      setAutoScroll: (autoScroll) => {
        set({ autoScroll }, false, 'setAutoScroll')
      },

      setShowEmojiPicker: (show) => {
        set({ showEmojiPicker: show }, false, 'setShowEmojiPicker')
      },

      setShowUserList: (show) => {
        set({ showUserList: show }, false, 'setShowUserList')
      },

      // 错误处理
      setError: (error) => {
        set({ error }, false, 'setError')
      },

      setLoading: (loading) => {
        set({ isLoading: loading }, false, 'setLoading')
      },

      // 重置状态
      reset: () => {
        set(initialState, false, 'reset')
      }
    }),
    {
      name: 'chat-store'
    }
  )
)
