<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AugmentAPI - 实时聊天</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>💬</text></svg>">

    <!-- 引入Element Plus CSS - 本地资源 -->
    <link rel="stylesheet" href="/lib/element-plus/index.min.css">

    <!-- 引入Vue 3 - 本地资源 -->
    <script src="/lib/vue/vue.global.min.js"></script>
    <!-- 引入Element Plus - 本地资源 -->
    <script src="/lib/element-plus/index.full.min.js"></script>
    <!-- 引入Element Plus图标 - 本地资源 -->
    <script src="/lib/element-plus/icons.iife.min.js"></script>
    <!-- 引入Socket.IO客户端 - 本地资源 -->
    <script src="/lib/socket.io/socket.io.min.js"></script>

    <!-- 认证工具函数 -->
    <script src="/js/auth-utils.js"></script>
    <!-- 静默控制台（生产环境隐藏console输出；加 ?debug=1 显示） -->
    <script src="/js/quiet-console.js"></script>
    <!-- 侧边栏组件 -->
    <script src="/components/vue-sidebar.js"></script>

    <style>
        /* 基础样式重置 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: #f5f7fa;
        }

        /* 布局容器 */
        .layout-container {
            height: 100vh;
            display: flex;
        }

        /* 侧边栏样式 */
        .sidebar {
            background: #fff;
            box-shadow: 2px 0 6px rgba(0,21,41,.35);
            transition: width 0.3s;
            overflow: hidden;
            position: relative;
        }

        .sidebar.collapsed {
            width: 64px !important;
        }

        .sidebar.collapsed .user-info {
            justify-content: center;
            padding: 0;
        }

        .sidebar.collapsed .user-details {
            display: none;
        }

        .sidebar.collapsed .user-avatar {
            margin-right: 0;
            flex-shrink: 0;
        }

        .sidebar.collapsed .el-menu-item span,
        .sidebar.collapsed .el-sub-menu .el-sub-menu__title span {
            display: none;
        }

        .sidebar.collapsed .el-sub-menu .el-sub-menu__icon-arrow {
            display: none;
        }

        .sidebar.expanded {
            width: 250px !important;
        }

        /* 主内容区 */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .content-header {
            background: #fff;
            padding: 16px 24px;
            border-bottom: 1px solid #e8eaec;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .content-body {
            flex: 1;
            padding: 24px;
            overflow-y: auto;
            background: #f5f7fa;
        }

        /* 用户信息区域 */
        .user-info {
            display: flex;
            align-items: center;
            padding: 16px;
            border-bottom: 1px solid #e8eaec;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin-right: 12px;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #409eff;
            color: white;
            font-weight: bold;
        }

        .user-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        /* 当有头像图片时，移除背景色 */
        .user-avatar:has(img) {
            background: transparent;
        }

        .user-details {
            flex: 1;
            min-width: 0;
        }

        .user-name {
            font-weight: 600;
            color: #303133;
            font-size: 14px;
            margin-bottom: 4px;
            display: flex;
            align-items: center;
        }

        .user-email {
            color: #909399;
            font-size: 12px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .trust-level {
            margin-left: 8px;
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 10px;
            font-weight: 600;
        }

        .trust-level-0 { background: #f4f4f5; color: #909399; }
        .trust-level-1 { background: #ecf5ff; color: #409eff; }
        .trust-level-2 { background: #f0f9ff; color: #67c23a; }
        .trust-level-3 { background: #fdf6ec; color: #e6a23c; }
        .trust-level-4 { background: #fef0f0; color: #f56c6c; }

        /* 侧边栏切换按钮 */
        .sidebar-toggle {
            position: absolute;
            top: 16px;
            right: -12px;
            width: 24px;
            height: 24px;
            background: #409eff;
            border: none;
            border-radius: 50%;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            z-index: 1000;
        }

        .sidebar-toggle:hover {
            background: #337ecc;
        }

        /* 聊天页面特定样式 */
        .chat-card {
            background: #fff;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            overflow: hidden;
            height: calc(100vh - 120px);
            display: flex;
            flex-direction: column;
        }

        .chat-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 16px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .chat-title {
            font-size: 18px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .online-count {
            background: rgba(255,255,255,0.2);
            padding: 4px 12px;
            border-radius: 16px;
            font-size: 13px;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .chat-body {
            flex: 1;
            display: flex;
            overflow: hidden;
        }

        .user-list {
            width: 280px;
            background: #f8f9fa;
            border-right: 1px solid #e8eaec;
            overflow-y: auto;
            flex-shrink: 0;
        }

        .user-list-header {
            padding: 16px;
            background: #e9ecef;
            font-weight: 600;
            font-size: 14px;
            border-bottom: 1px solid #dee2e6;
            color: #495057;
        }

        .user-item {
            padding: 12px 16px;
            display: flex;
            align-items: center;
            gap: 12px;
            border-bottom: 1px solid #e9ecef;
            transition: all 0.2s;
            cursor: pointer;
        }

        .user-item:hover {
            background: #e9ecef;
        }

        .user-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: #409eff;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 14px;
            overflow: hidden;
        }

        .user-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        /* 当有头像图片时，移除背景色 */
        .user-avatar:has(img) {
            background: transparent !important;
        }

        .user-info {
            flex: 1;
            min-width: 0;
        }

        .user-name {
            font-weight: 500;
            font-size: 14px;
            color: #303133;
            margin-bottom: 2px;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .user-status {
            font-size: 12px;
            color: #909399;
        }

        .admin-badge {
            background: #e6a23c;
            color: white;
            padding: 2px 6px;
            border-radius: 8px;
            font-size: 10px;
            font-weight: 600;
        }

        .message-area {
            flex: 1;
            display: flex;
            flex-direction: column;
            min-width: 0;
        }

        .message-list {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #ffffff;
        }

        .message-item {
            margin-bottom: 16px;
            display: flex;
            align-items: flex-start;
            gap: 12px;
        }

        .message-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #409eff;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            flex-shrink: 0;
            overflow: hidden;
        }

        .message-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        /* 当有头像图片时，移除背景色 */
        .message-avatar:has(img) {
            background: transparent !important;
        }

        .message-content {
            flex: 1;
            min-width: 0;
        }

        .message-header {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 6px;
        }

        .message-username {
            font-weight: 600;
            color: #303133;
            font-size: 14px;
        }

        .message-time {
            font-size: 12px;
            color: #909399;
        }

        .message-text {
            background: #f5f7fa;
            padding: 12px 16px;
            border-radius: 12px;
            line-height: 1.5;
            word-wrap: break-word;
            color: #303133;
            border: 1px solid #e8eaec;
        }

        .system-message .message-text {
            background: #ecf5ff;
            color: #409eff;
            border-color: #b3d8ff;
            font-style: italic;
        }

        .admin-message .message-text {
            background: #fdf6ec;
            border-left: 4px solid #e6a23c;
            border-color: #f5dab1;
        }

        .token-grant-message .message-text {
            background: #f0f9ff;
            color: #67c23a;
            border-left: 4px solid #67c23a;
            border-color: #c2e7b0;
        }

        .token-request-message .message-text {
            background: #fdf6ec;
            border-left: 4px solid #e6a23c;
            border-color: #f5dab1;
        }

        .admin-actions {
            margin-top: 8px;
            display: flex;
            gap: 8px;
        }

        .message-input-area {
            padding: 16px 20px;
            background: #f8f9fa;
            border-top: 1px solid #e8eaec;
        }

        .input-container {
            display: flex;
            gap: 12px;
            align-items: flex-end;
        }

        .command-helper {
            background: #ecf5ff;
            border: 1px solid #b3d8ff;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 12px;
            font-size: 13px;
            color: #409eff;
        }

        /* 移动端适配 */
        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                left: 0;
                top: 0;
                height: 100vh;
                z-index: 1000;
                transform: translateX(-100%);
                transition: transform 0.3s;
            }

            .sidebar.mobile-open {
                transform: translateX(0);
            }

            .main-content {
                width: 100%;
            }

            .content-body {
                padding: 16px;
            }

            .chat-card {
                height: calc(100vh - 80px);
                border-radius: 0;
                margin: 0;
            }

            .user-list {
                display: none;
            }

            .chat-body {
                flex-direction: column;
            }

            .message-list {
                padding: 16px;
            }

            .message-input-area {
                padding: 12px 16px;
            }
        }

        /* 滚动条样式 */
        .message-list::-webkit-scrollbar,
        .user-list::-webkit-scrollbar {
            width: 6px;
        }

        .message-list::-webkit-scrollbar-track,
        .user-list::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        .message-list::-webkit-scrollbar-thumb,
        .user-list::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }

        .message-list::-webkit-scrollbar-thumb:hover,
        .user-list::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }
    </style>
</head>
<body>
    <div id="app">
        <!-- 加载遮罩 -->
        <div v-if="loading" style="position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(255,255,255,0.9); display: flex; align-items: center; justify-content: center; z-index: 9999;">
            <el-loading-spinner size="large"></el-loading-spinner>
        </div>

        <!-- 主布局容器 -->
        <div v-else class="layout-container">
            <!-- 侧边栏 -->
            <div class="sidebar" :class="{
                collapsed: isCollapsed,
                expanded: !isCollapsed,
                'mobile-open': mobileMenuOpen
            }">
                <button class="sidebar-toggle" @click="toggleSidebar">
                    {{ isCollapsed ? '→' : '←' }}
                </button>

                <!-- 用户信息 -->
                <div class="user-info" v-if="currentUser">
                    <div class="user-avatar">
                        <img v-if="currentUser.linuxdo_avatar" :src="currentUser.linuxdo_avatar" :alt="currentUser.linuxdo_username">
                        <span v-else>{{ currentUser.email ? currentUser.email.charAt(0).toUpperCase() : '?' }}</span>
                    </div>
                    <div class="user-details" v-show="!isCollapsed">
                        <div class="user-name">
                            {{ currentUser.linuxdo_username || currentUser.email.split('@')[0] }}
                            <span v-if="currentUser.linuxdo_trust_level !== null"
                                  :class="`trust-level trust-level-${currentUser.linuxdo_trust_level}`">
                                {{ getTrustLevelName(currentUser.linuxdo_trust_level) }}
                            </span>
                        </div>
                        <div class="user-email">{{ currentUser.email }}</div>
                    </div>
                </div>

                <!-- 导航菜单 -->
                <el-menu
                    :default-active="activeMenu"
                    :collapse="isCollapsed"
                    :unique-opened="true"
                    background-color="#fff"
                    text-color="#303133"
                    active-text-color="#409eff">

                    <el-menu-item index="home" @click="navigateTo('/')">
                        <el-icon><i class="el-icon-house"></i></el-icon>
                        <span>首页</span>
                    </el-menu-item>

                    <el-menu-item index="profile" @click="navigateTo('/profile')">
                        <el-icon><i class="el-icon-user"></i></el-icon>
                        <span>个人中心</span>
                    </el-menu-item>

                    <el-menu-item index="chat" @click="navigateTo('/chat')">
                        <el-icon><i class="el-icon-chat-dot-round"></i></el-icon>
                        <span>实时聊天</span>
                    </el-menu-item>

                    <el-sub-menu index="api">
                        <template #title>
                            <el-icon><i class="el-icon-document"></i></el-icon>
                            <span>API管理</span>
                        </template>
                        <el-menu-item index="api-docs" @click="navigateTo('/api-docs')">
                            <el-icon><i class="el-icon-reading"></i></el-icon>
                            <span>API文档</span>
                        </el-menu-item>
                        <el-menu-item index="external-test" @click="navigateTo('/external-test')">
                            <el-icon><i class="el-icon-connection"></i></el-icon>
                            <span>外部接口测试</span>
                        </el-menu-item>
                    </el-sub-menu>

                    <el-menu-item index="docs" @click="navigateTo('/docs')">
                        <el-icon><i class="el-icon-folder"></i></el-icon>
                        <span>文档中心</span>
                    </el-menu-item>

                    <!-- 管理后台菜单项（仅管理员可见） -->
                    <el-menu-item v-if="currentUser && currentUser.isAdmin" index="admin" @click="navigateTo('/admin')">
                        <el-icon><i class="el-icon-setting"></i></el-icon>
                        <span>管理后台</span>
                    </el-menu-item>

                    <el-menu-item index="logout" @click="handleLogout">
                        <el-icon><i class="el-icon-switch-button"></i></el-icon>
                        <span>退出登录</span>
                    </el-menu-item>
                </el-menu>
            </div>

            <!-- 主内容区 -->
            <div class="main-content">
                <!-- 内容头部 -->
                <div class="content-header">
                    <div>
                        <el-button v-if="isMobile" @click="toggleMobileSidebar" icon="el-icon-menu" circle></el-button>
                        <span style="font-size: 18px; font-weight: 600; margin-left: 8px;">实时聊天</span>
                    </div>
                    <div class="online-count">
                        🟢 在线 {{ onlineCount }} 人
                    </div>
                </div>

                <!-- 内容主体 -->
                <div class="content-body">
                    <!-- 聊天卡片 -->
                    <div class="chat-card">
                        <!-- 聊天主体 -->
                        <div class="chat-body">
                            <!-- 用户列表 -->
                            <div class="user-list">
                                <div class="user-list-header">
                                    在线用户 ({{ onlineUsers.length }})
                                </div>
                                <div
                                    v-for="user in onlineUsers"
                                    :key="user.user_uuid"
                                    class="user-item">
                                    <div class="user-avatar">
                                        <img v-if="user.avatar_url" :src="user.avatar_url">
                                        <span v-else>{{ user.username ? user.username.charAt(0).toUpperCase() : '?' }}</span>
                                    </div>
                                    <div class="user-info">
                                        <div class="user-name">
                                            {{ user.username || '未知用户' }}
                                            <span v-if="user.is_admin" class="admin-badge">管理员</span>
                                        </div>
                                        <div class="user-status">{{ user.status === 'online' ? '在线' : '离开' }}</div>
                                    </div>
                                </div>
                            </div>

                            <!-- 消息区域 -->
                            <div class="message-area">
                                <!-- 消息列表 -->
                                <div class="message-list" ref="messageList">
                                    <!-- 命令帮助 -->
                                    <div v-if="showCommandHelper" class="command-helper">
                                        💡 快速命令：/help 帮助 | /online 在线用户 | /stats 统计 | 直接发送消息申请Token
                                    </div>

                                    <!-- 消息项 -->
                                    <div
                                        v-for="message in messages"
                                        :key="message.id"
                                        class="message-item"
                                        :class="{
                                            'system-message': message.message_type === 'system',
                                            'admin-message': message.is_admin,
                                            'token-grant-message': message.message_type === 'token_grant',
                                            'token-request-message': message.message_type === 'token_request'
                                        }">
                                        <div class="message-avatar">
                                            <img v-if="message.avatar_url" :src="message.avatar_url">
                                            <span v-else>{{ message.username ? message.username.charAt(0).toUpperCase() : '🤖' }}</span>
                                        </div>
                                        <div class="message-content">
                                            <div class="message-header">
                                                <span class="message-username">
                                                    {{ message.is_admin ? '👑' : '🟢' }} {{ message.username || '系统' }}
                                                </span>
                                                <span class="message-time">{{ formatTime(message.created_at) }}</span>
                                            </div>
                                            <div class="message-text">{{ message.message }}</div>

                                            <!-- 管理员操作按钮 (仅对Token申请消息显示) -->
                                            <div v-if="message.message_type === 'token_request' && currentUser.isAdmin && message.metadata?.request_id" class="admin-actions">
                                                <el-button type="success" size="small" @click="approveTokenRequest(message.metadata.request_id, message.user_uuid)">
                                                    ✅ 同意申请
                                                </el-button>
                                                <el-button type="danger" size="small" @click="rejectTokenRequest(message.metadata.request_id, message.user_uuid)">
                                                    ❌ 拒绝申请
                                                </el-button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 消息输入区域 -->
                                <div class="message-input-area">
                                    <div class="input-container">
                                        <el-input
                                            v-model="newMessage"
                                            type="textarea"
                                            :rows="2"
                                            placeholder="输入消息... (支持 /help 查看命令)"
                                            @keydown.enter.exact.prevent="sendMessage"
                                            @keydown.enter.shift.exact="newMessage += '\n'"
                                            :disabled="!connected"
                                            maxlength="1000"
                                            resize="none">
                                        </el-input>
                                        <el-button
                                            type="primary"
                                            @click="sendMessage"
                                            :disabled="!connected || !newMessage.trim()"
                                            :icon="Position"
                                            circle>
                                        </el-button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 移动端遮罩 -->
        <div v-if="isMobile && mobileMenuOpen"
             style="position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.5); z-index: 999;"
             @click="closeMobileSidebar"></div>
    </div>

    <script>
        const { createApp, ref, reactive, onMounted, onUnmounted, nextTick, watch, computed } = Vue;
        const { Position, User, Message } = ElementPlusIconsVue;

        createApp({
            setup() {
                // 侧边栏相关数据
                const isCollapsed = ref(false);
                const mobileMenuOpen = ref(false);
                const activeMenu = ref('chat');

                // 聊天相关数据
                const loading = ref(true);
                const connected = ref(false);
                const socket = ref(null);
                const currentUser = ref(null);

                const messages = ref([]);
                const onlineUsers = ref([]);
                const onlineCount = ref(0);
                const newMessage = ref('');
                const showCommandHelper = ref(true);

                // DOM引用
                const messageList = ref(null);

                // 计算属性
                const isMobile = computed(() => {
                    return window.innerWidth <= 768;
                });

                // 侧边栏方法
                const toggleSidebar = () => {
                    isCollapsed.value = !isCollapsed.value;
                };

                const toggleMobileSidebar = () => {
                    mobileMenuOpen.value = !mobileMenuOpen.value;
                };

                const closeMobileSidebar = () => {
                    mobileMenuOpen.value = false;
                };

                const navigateTo = (path) => {
                    window.location.href = path;
                };

                const handleLogout = async () => {
                    try {
                        const response = await fetch('/api/logout', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            }
                        });

                        if (response.ok) {
                            localStorage.removeItem('auth_token');
                            window.location.href = '/login';
                        } else {
                            ElementPlus.ElMessage.error('登出失败，请重试');
                        }
                    } catch (error) {
                        console.error('登出错误:', error);
                        ElementPlus.ElMessage.error('网络错误，请重试');
                    }
                };

                const getTrustLevelName = (level) => {
                    const levelNames = {
                        0: '新用户',
                        1: '基础',
                        2: '成员',
                        3: '常规',
                        4: '领导'
                    };
                    return levelNames[level] || '未知';
                };

                // 检查登录状态
                const checkLoginStatus = async () => {
                    try {
                        const response = await fetch('/api/user');
                        if (response.ok) {
                            const result = await response.json();
                            currentUser.value = result.user;
                            return true;
                        } else if (response.status === 401) {
                            window.location.href = '/login';
                            return false;
                        }
                    } catch (error) {
                        console.error('检查登录状态失败:', error);
                        return false;
                    }
                };

                // 初始化Socket连接
                const initSocket = async () => {
                    try {
                        // 获取用户Token
                        const token = getCookie('auth_token');
                        if (!token) {
                            ElementPlus.ElMessage.error('请先登录');
                            window.location.href = '/login.html';
                            return;
                        }

                        // 创建Socket连接
                        socket.value = io();

                        // 连接成功
                        socket.value.on('connect', () => {
                            console.log('Socket连接成功');
                            // 加入聊天室
                            socket.value.emit('join_chat', { token });
                        });

                        // 加入成功
                        socket.value.on('join_success', (data) => {
                            console.log('加入聊天室成功:', data);
                            // 保持使用API获取的用户数据，只更新连接状态
                            connected.value = true;
                            loading.value = false;
                            ElementPlus.ElMessage.success(data.message);
                        });

                        // 接收消息历史
                        socket.value.on('message_history', (data) => {
                            console.log('接收消息历史:', data);
                            messages.value = data.messages || [];
                            nextTick(() => scrollToBottom());
                        });

                        // 接收新消息
                        socket.value.on('new_message', (message) => {
                            console.log('接收新消息:', message);
                            messages.value.push(message);
                            nextTick(() => scrollToBottom());
                        });

                        // 用户加入
                        socket.value.on('user_joined', (data) => {
                            console.log('用户加入:', data);
                            onlineCount.value = data.online_count;
                        });

                        // 用户离开
                        socket.value.on('user_left', (data) => {
                            console.log('用户离开:', data);
                            onlineCount.value = data.online_count;
                        });

                        // 在线用户更新
                        socket.value.on('online_users_update', (data) => {
                            console.log('在线用户更新:', data);
                            onlineUsers.value = data.users || [];
                            onlineCount.value = data.count;
                        });

                        // Token申请成功
                        socket.value.on('token_request_success', (data) => {
                            ElementPlus.ElMessage.success(data.message);
                        });

                        // 命令结果
                        socket.value.on('command_result', (data) => {
                            ElementPlus.ElMessage({
                                message: data.result,
                                type: 'info',
                                duration: 5000,
                                showClose: true
                            });
                        });

                        // 错误处理
                        socket.value.on('error', (data) => {
                            console.error('Socket错误:', data);
                            ElementPlus.ElMessage.error(data.message);
                        });

                        // 连接断开
                        socket.value.on('disconnect', () => {
                            console.log('Socket连接断开');
                            connected.value = false;
                            ElementPlus.ElMessage.warning('连接已断开，正在重连...');
                        });

                        // 重连成功
                        socket.value.on('reconnect', () => {
                            console.log('Socket重连成功');
                            connected.value = true;
                            ElementPlus.ElMessage.success('重连成功');
                        });

                    } catch (error) {
                        console.error('初始化Socket失败:', error);
                        loading.value = false;
                        ElementPlus.ElMessage.error('连接聊天室失败');
                    }
                };

                // 发送消息
                const sendMessage = () => {
                    if (!connected.value || !newMessage.value.trim()) {
                        return;
                    }

                    const message = newMessage.value.trim();

                    // 检查是否为命令
                    if (message.startsWith('/')) {
                        const parts = message.split(' ');
                        const command = parts[0];
                        const params = parts.slice(1);

                        socket.value.emit('chat_command', { command, params });
                    } else if (message.includes('申请') && message.includes('token')) {
                        // Token申请
                        const reason = message.replace(/申请|token/gi, '').trim();
                        socket.value.emit('request_token', { reason });
                    } else {
                        // 普通消息
                        socket.value.emit('send_message', { message });
                    }

                    newMessage.value = '';
                };

                // 滚动到底部
                const scrollToBottom = () => {
                    if (messageList.value) {
                        messageList.value.scrollTop = messageList.value.scrollHeight;
                    }
                };

                // 格式化时间
                const formatTime = (timestamp) => {
                    const date = new Date(timestamp);
                    const now = new Date();
                    const diff = now - date;

                    if (diff < 60000) { // 1分钟内
                        return '刚刚';
                    } else if (diff < 3600000) { // 1小时内
                        return Math.floor(diff / 60000) + '分钟前';
                    } else if (date.toDateString() === now.toDateString()) { // 今天
                        return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
                    } else {
                        return date.toLocaleDateString('zh-CN') + ' ' + 
                               date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
                    }
                };

                // 生命周期
                onMounted(async () => {
                    await checkLoginStatus();
                    initSocket();

                    // 5秒后隐藏命令帮助
                    setTimeout(() => {
                        showCommandHelper.value = false;
                    }, 5000);

                    // 监听窗口大小变化
                    window.addEventListener('resize', () => {
                        if (!isMobile.value && mobileMenuOpen.value) {
                            mobileMenuOpen.value = false;
                        }
                    });
                });

                onUnmounted(() => {
                    if (socket.value) {
                        socket.value.disconnect();
                    }
                });

                // 管理员同意Token申请
                const approveTokenRequest = async (requestId, userUuid) => {
                    try {
                        const { value: responseMessage } = await ElementPlus.ElMessageBox.prompt(
                            '请输入回复消息（可选）',
                            '同意Token申请',
                            {
                                confirmButtonText: '同意',
                                cancelButtonText: '取消',
                                inputPlaceholder: '例如：申请已通过，Token已分配'
                            }
                        );

                        if (!socket.value) {
                            ElementPlus.ElMessage.error('连接未建立');
                            return;
                        }

                        socket.value.emit('admin_grant_token', {
                            request_id: requestId,
                            response_message: responseMessage || '申请已通过，Token已分配'
                        });

                        ElementPlus.ElMessage.success('正在处理申请...');
                    } catch (error) {
                        if (error !== 'cancel') {
                            ElementPlus.ElMessage.error('操作失败');
                        }
                    }
                };

                // 管理员拒绝Token申请
                const rejectTokenRequest = async (requestId, userUuid) => {
                    try {
                        const { value: responseMessage } = await ElementPlus.ElMessageBox.prompt(
                            '请输入拒绝原因',
                            '拒绝Token申请',
                            {
                                confirmButtonText: '拒绝',
                                cancelButtonText: '取消',
                                inputPlaceholder: '请说明拒绝原因...',
                                inputValidator: (value) => {
                                    if (!value || !value.trim()) {
                                        return '请输入拒绝原因';
                                    }
                                    return true;
                                }
                            }
                        );

                        if (!socket.value) {
                            ElementPlus.ElMessage.error('连接未建立');
                            return;
                        }

                        socket.value.emit('admin_reject_token', {
                            request_id: requestId,
                            response_message: responseMessage
                        });

                        ElementPlus.ElMessage.success('正在处理申请...');
                    } catch (error) {
                        if (error !== 'cancel') {
                            ElementPlus.ElMessage.error('操作失败');
                        }
                    }
                };

                return {
                    // 侧边栏相关
                    isCollapsed,
                    mobileMenuOpen,
                    activeMenu,
                    isMobile,
                    toggleSidebar,
                    toggleMobileSidebar,
                    closeMobileSidebar,
                    navigateTo,
                    handleLogout,
                    getTrustLevelName,
                    // 聊天相关
                    loading,
                    connected,
                    currentUser,
                    messages,
                    onlineUsers,
                    onlineCount,
                    newMessage,
                    showCommandHelper,
                    messageList,
                    sendMessage,
                    formatTime,
                    approveTokenRequest,
                    rejectTokenRequest,
                    // 图标组件
                    Position,
                    User,
                    Message
                };
            }
        }).use(ElementPlus).mount('#app');
    </script>
</body>
</html>
