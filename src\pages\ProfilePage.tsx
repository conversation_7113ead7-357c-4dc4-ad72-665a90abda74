import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { 
  User, 
  Settings, 
  Key, 
  BarChart3, 
  Calendar,
  MessageSquare,
  Heart,
  Download
} from 'lucide-react'
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import ProfileForm from '@/components/ProfileForm'
import AccountSettings from '@/components/AccountSettings'
import ApiKeyManager from '@/components/ApiKeyManager'
import { useAuthStore } from '@/stores/authStore'
import { getTrustLevelName } from '@/lib/auth'
import dayjs from 'dayjs'

// 用户统计数据接口
interface UserStats {
  messagesCount: number
  apiCallsCount: number
  joinedDays: number
  lastActiveAt: string
}

const ProfilePage: React.FC = () => {
  const { user } = useAuthStore()
  const [activeTab, setActiveTab] = useState('profile')

  // 模拟用户统计数据
  const userStats: UserStats = {
    messagesCount: 156,
    apiCallsCount: 2340,
    joinedDays: 45,
    lastActiveAt: new Date().toISOString()
  }

  if (!user) {
    return (
      <div className="p-6">
        <div className="text-center">
          <p className="text-gray-500">用户信息加载中...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6 max-w-6xl mx-auto">
      {/* 页面标题 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="mb-8"
      >
        <h1 className="text-3xl font-bold text-gray-900 mb-2">个人中心</h1>
        <p className="text-gray-600">管理您的个人资料、设置和API密钥</p>
      </motion.div>

      {/* 用户概览卡片 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="mb-8"
      >
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-6">
              {/* 头像 */}
              <Avatar className="w-20 h-20">
                <AvatarImage src={user.linuxdo_avatar} />
                <AvatarFallback className="text-2xl">
                  {user.linuxdo_username?.[0]?.toUpperCase() || user.email[0].toUpperCase()}
                </AvatarFallback>
              </Avatar>

              {/* 用户信息 */}
              <div className="flex-1">
                <div className="flex items-center space-x-3 mb-2">
                  <h2 className="text-2xl font-bold text-gray-900">
                    {user.linuxdo_username || user.email}
                  </h2>
                  {user.linuxdo_trust_level !== undefined && (
                    <Badge variant="secondary">
                      {getTrustLevelName(user.linuxdo_trust_level)}
                    </Badge>
                  )}
                  {user.isAdmin && (
                    <Badge variant="destructive">
                      管理员
                    </Badge>
                  )}
                </div>
                <p className="text-gray-600 mb-3">{user.email}</p>
                <div className="flex items-center space-x-6 text-sm text-gray-500">
                  <div className="flex items-center space-x-1">
                    <Calendar className="w-4 h-4" />
                    <span>加入 {userStats.joinedDays} 天</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <MessageSquare className="w-4 h-4" />
                    <span>{userStats.messagesCount} 条消息</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <BarChart3 className="w-4 h-4" />
                    <span>{userStats.apiCallsCount.toLocaleString()} 次API调用</span>
                  </div>
                </div>
              </div>

              {/* 统计数据 */}
              <div className="hidden md:flex space-x-8">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">
                    {userStats.messagesCount}
                  </div>
                  <div className="text-sm text-gray-500">消息数</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {(userStats.apiCallsCount / 1000).toFixed(1)}K
                  </div>
                  <div className="text-sm text-gray-500">API调用</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-600">
                    {userStats.joinedDays}
                  </div>
                  <div className="text-sm text-gray-500">活跃天数</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* 主要内容区域 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
      >
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="profile" className="flex items-center space-x-2">
              <User className="w-4 h-4" />
              <span>个人资料</span>
            </TabsTrigger>
            <TabsTrigger value="settings" className="flex items-center space-x-2">
              <Settings className="w-4 h-4" />
              <span>账户设置</span>
            </TabsTrigger>
            <TabsTrigger value="api-keys" className="flex items-center space-x-2">
              <Key className="w-4 h-4" />
              <span>API密钥</span>
            </TabsTrigger>
            <TabsTrigger value="activity" className="flex items-center space-x-2">
              <BarChart3 className="w-4 h-4" />
              <span>活动统计</span>
            </TabsTrigger>
          </TabsList>

          {/* 个人资料 */}
          <TabsContent value="profile">
            <ProfileForm />
          </TabsContent>

          {/* 账户设置 */}
          <TabsContent value="settings">
            <AccountSettings />
          </TabsContent>

          {/* API密钥管理 */}
          <TabsContent value="api-keys">
            <ApiKeyManager />
          </TabsContent>

          {/* 活动统计 */}
          <TabsContent value="activity">
            <div className="space-y-6">
              {/* 统计概览 */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-sm font-medium text-gray-600">
                      总消息数
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center space-x-2">
                      <MessageSquare className="w-5 h-5 text-blue-500" />
                      <span className="text-2xl font-bold">{userStats.messagesCount}</span>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-sm font-medium text-gray-600">
                      API调用次数
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center space-x-2">
                      <BarChart3 className="w-5 h-5 text-green-500" />
                      <span className="text-2xl font-bold">{userStats.apiCallsCount.toLocaleString()}</span>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-sm font-medium text-gray-600">
                      活跃天数
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center space-x-2">
                      <Calendar className="w-5 h-5 text-purple-500" />
                      <span className="text-2xl font-bold">{userStats.joinedDays}</span>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-sm font-medium text-gray-600">
                      最后活跃
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center space-x-2">
                      <User className="w-5 h-5 text-orange-500" />
                      <span className="text-sm font-medium">
                        {dayjs(userStats.lastActiveAt).fromNow()}
                      </span>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* 活动历史 */}
              <Card>
                <CardHeader>
                  <CardTitle>最近活动</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {/* 模拟活动记录 */}
                    <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                      <MessageSquare className="w-5 h-5 text-blue-500" />
                      <div className="flex-1">
                        <p className="text-sm font-medium">发送了一条聊天消息</p>
                        <p className="text-xs text-gray-500">2小时前</p>
                      </div>
                    </div>

                    <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                      <Key className="w-5 h-5 text-green-500" />
                      <div className="flex-1">
                        <p className="text-sm font-medium">创建了新的API密钥</p>
                        <p className="text-xs text-gray-500">1天前</p>
                      </div>
                    </div>

                    <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                      <Download className="w-5 h-5 text-purple-500" />
                      <div className="flex-1">
                        <p className="text-sm font-medium">下载了API文档</p>
                        <p className="text-xs text-gray-500">3天前</p>
                      </div>
                    </div>

                    <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                      <Heart className="w-5 h-5 text-red-500" />
                      <div className="flex-1">
                        <p className="text-sm font-medium">进行了赞助</p>
                        <p className="text-xs text-gray-500">1周前</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </motion.div>
    </div>
  )
}

export default ProfilePage
