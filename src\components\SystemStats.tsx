import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  BarChart3, 
  Users, 
  MessageSquare, 
  Activity, 
  Server,
  Database,
  Cpu,
  HardDrive,
  RefreshCw,
  TrendingUp,
  TrendingDown
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import StatCard from '@/components/StatCard'

interface SystemMetrics {
  // 用户统计
  totalUsers: number
  activeUsers: number
  newUsersToday: number
  newUsersYesterday: number
  
  // 消息统计
  totalMessages: number
  messagesToday: number
  messagesYesterday: number
  
  // API统计
  totalApiCalls: number
  apiCallsToday: number
  apiCallsYesterday: number
  
  // 系统资源
  cpuUsage: number
  memoryUsage: number
  diskUsage: number
  
  // 在线状态
  onlineUsers: number
  serverUptime: number
}

interface DailyStats {
  date: string
  users: number
  messages: number
  apiCalls: number
}

const SystemStats: React.FC = () => {
  const [metrics, setMetrics] = useState<SystemMetrics | null>(null)
  const [dailyStats, setDailyStats] = useState<DailyStats[]>([])
  const [loading, setLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)

  // 模拟系统指标数据
  const mockMetrics: SystemMetrics = {
    totalUsers: 1247,
    activeUsers: 89,
    newUsersToday: 12,
    newUsersYesterday: 8,
    
    totalMessages: 15678,
    messagesToday: 234,
    messagesYesterday: 189,
    
    totalApiCalls: 89456,
    apiCallsToday: 1234,
    apiCallsYesterday: 1089,
    
    cpuUsage: 45.2,
    memoryUsage: 67.8,
    diskUsage: 34.5,
    
    onlineUsers: 23,
    serverUptime: 15 * 24 * 60 * 60 * 1000 // 15天
  }

  // 模拟每日统计数据
  const mockDailyStats: DailyStats[] = Array.from({ length: 7 }, (_, i) => ({
    date: new Date(Date.now() - i * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    users: Math.floor(Math.random() * 20) + 5,
    messages: Math.floor(Math.random() * 300) + 100,
    apiCalls: Math.floor(Math.random() * 2000) + 800
  })).reverse()

  // 加载系统指标
  const loadMetrics = async () => {
    setLoading(true)
    try {
      // TODO: 调用API获取系统指标
      await new Promise(resolve => setTimeout(resolve, 1000))
      setMetrics(mockMetrics)
      setDailyStats(mockDailyStats)
    } catch (error) {
      console.error('加载系统指标失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 刷新数据
  const handleRefresh = async () => {
    setRefreshing(true)
    await loadMetrics()
    setRefreshing(false)
  }

  // 初始加载
  useEffect(() => {
    loadMetrics()
  }, [])

  // 格式化运行时间
  const formatUptime = (milliseconds: number) => {
    const days = Math.floor(milliseconds / (24 * 60 * 60 * 1000))
    const hours = Math.floor((milliseconds % (24 * 60 * 60 * 1000)) / (60 * 60 * 1000))
    const minutes = Math.floor((milliseconds % (60 * 60 * 1000)) / (60 * 1000))
    
    return `${days}天 ${hours}小时 ${minutes}分钟`
  }

  // 获取趋势
  const getTrend = (today: number, yesterday: number) => {
    if (yesterday === 0) return { value: 0, isPositive: true }
    const change = ((today - yesterday) / yesterday) * 100
    return {
      value: Math.abs(change),
      isPositive: change >= 0
    }
  }

  // 获取资源使用状态颜色
  const getUsageColor = (usage: number) => {
    if (usage < 50) return 'text-green-600'
    if (usage < 80) return 'text-yellow-600'
    return 'text-red-600'
  }

  if (loading || !metrics) {
    return (
      <div className="space-y-6">
        {/* 加载状态 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <BarChart3 className="w-5 h-5" />
              <span>系统统计</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {Array.from({ length: 8 }).map((_, i) => (
                <div key={i} className="animate-pulse">
                  <div className="h-24 bg-gray-200 rounded-lg" />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* 标题和刷新按钮 */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">系统统计</h2>
          <p className="text-gray-600">实时监控系统运行状态和用户活动</p>
        </div>
        <Button
          onClick={handleRefresh}
          disabled={refreshing}
          variant="outline"
          className="flex items-center space-x-2"
        >
          <RefreshCw className={`w-4 h-4 ${refreshing ? 'animate-spin' : ''}`} />
          <span>刷新数据</span>
        </Button>
      </div>

      {/* 核心指标 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="总用户数"
          value={metrics.totalUsers}
          icon={Users}
          color="blue"
        />
        <StatCard
          title="活跃用户"
          value={metrics.activeUsers}
          icon={Activity}
          color="green"
        />
        <StatCard
          title="今日新增用户"
          value={metrics.newUsersToday}
          icon={Users}
          color="purple"
          trend={getTrend(metrics.newUsersToday, metrics.newUsersYesterday)}
        />
        <StatCard
          title="在线用户"
          value={metrics.onlineUsers}
          icon={Activity}
          color="orange"
        />
      </div>

      {/* 消息和API统计 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="总消息数"
          value={metrics.totalMessages.toLocaleString()}
          icon={MessageSquare}
          color="blue"
        />
        <StatCard
          title="今日消息"
          value={metrics.messagesToday}
          icon={MessageSquare}
          color="green"
          trend={getTrend(metrics.messagesToday, metrics.messagesYesterday)}
        />
        <StatCard
          title="总API调用"
          value={`${(metrics.totalApiCalls / 1000).toFixed(1)}K`}
          icon={BarChart3}
          color="purple"
        />
        <StatCard
          title="今日API调用"
          value={metrics.apiCallsToday.toLocaleString()}
          icon={BarChart3}
          color="orange"
          trend={getTrend(metrics.apiCallsToday, metrics.apiCallsYesterday)}
        />
      </div>

      {/* 系统资源监控 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Server className="w-5 h-5" />
            <span>系统资源</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* CPU使用率 */}
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Cpu className="w-4 h-4 text-blue-500" />
                  <span className="text-sm font-medium">CPU使用率</span>
                </div>
                <span className={`text-sm font-bold ${getUsageColor(metrics.cpuUsage)}`}>
                  {metrics.cpuUsage}%
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <motion.div
                  initial={{ width: 0 }}
                  animate={{ width: `${metrics.cpuUsage}%` }}
                  transition={{ duration: 1, ease: "easeOut" }}
                  className={`h-2 rounded-full ${
                    metrics.cpuUsage < 50 ? 'bg-green-500' :
                    metrics.cpuUsage < 80 ? 'bg-yellow-500' : 'bg-red-500'
                  }`}
                />
              </div>
            </div>

            {/* 内存使用率 */}
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Database className="w-4 h-4 text-green-500" />
                  <span className="text-sm font-medium">内存使用率</span>
                </div>
                <span className={`text-sm font-bold ${getUsageColor(metrics.memoryUsage)}`}>
                  {metrics.memoryUsage}%
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <motion.div
                  initial={{ width: 0 }}
                  animate={{ width: `${metrics.memoryUsage}%` }}
                  transition={{ duration: 1, ease: "easeOut", delay: 0.2 }}
                  className={`h-2 rounded-full ${
                    metrics.memoryUsage < 50 ? 'bg-green-500' :
                    metrics.memoryUsage < 80 ? 'bg-yellow-500' : 'bg-red-500'
                  }`}
                />
              </div>
            </div>

            {/* 磁盘使用率 */}
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <HardDrive className="w-4 h-4 text-purple-500" />
                  <span className="text-sm font-medium">磁盘使用率</span>
                </div>
                <span className={`text-sm font-bold ${getUsageColor(metrics.diskUsage)}`}>
                  {metrics.diskUsage}%
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <motion.div
                  initial={{ width: 0 }}
                  animate={{ width: `${metrics.diskUsage}%` }}
                  transition={{ duration: 1, ease: "easeOut", delay: 0.4 }}
                  className={`h-2 rounded-full ${
                    metrics.diskUsage < 50 ? 'bg-green-500' :
                    metrics.diskUsage < 80 ? 'bg-yellow-500' : 'bg-red-500'
                  }`}
                />
              </div>
            </div>
          </div>

          {/* 服务器运行时间 */}
          <div className="mt-6 pt-6 border-t border-gray-200">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Activity className="w-4 h-4 text-green-500" />
                <span className="text-sm font-medium">服务器运行时间</span>
              </div>
              <span className="text-sm font-bold text-green-600">
                {formatUptime(metrics.serverUptime)}
              </span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 趋势图表 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <TrendingUp className="w-5 h-5" />
            <span>7天趋势</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {/* 简化的趋势展示 */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <h4 className="text-sm font-medium text-gray-600 mb-2">新增用户</h4>
                <div className="flex items-center justify-center space-x-1">
                  {dailyStats.slice(-7).map((stat, index) => (
                    <div
                      key={index}
                      className="w-8 h-16 bg-blue-100 rounded flex items-end"
                    >
                      <motion.div
                        initial={{ height: 0 }}
                        animate={{ height: `${(stat.users / 20) * 100}%` }}
                        transition={{ duration: 0.5, delay: index * 0.1 }}
                        className="w-full bg-blue-500 rounded"
                      />
                    </div>
                  ))}
                </div>
                <p className="text-xs text-gray-500 mt-2">过去7天</p>
              </div>

              <div className="text-center">
                <h4 className="text-sm font-medium text-gray-600 mb-2">消息数量</h4>
                <div className="flex items-center justify-center space-x-1">
                  {dailyStats.slice(-7).map((stat, index) => (
                    <div
                      key={index}
                      className="w-8 h-16 bg-green-100 rounded flex items-end"
                    >
                      <motion.div
                        initial={{ height: 0 }}
                        animate={{ height: `${(stat.messages / 400) * 100}%` }}
                        transition={{ duration: 0.5, delay: index * 0.1 }}
                        className="w-full bg-green-500 rounded"
                      />
                    </div>
                  ))}
                </div>
                <p className="text-xs text-gray-500 mt-2">过去7天</p>
              </div>

              <div className="text-center">
                <h4 className="text-sm font-medium text-gray-600 mb-2">API调用</h4>
                <div className="flex items-center justify-center space-x-1">
                  {dailyStats.slice(-7).map((stat, index) => (
                    <div
                      key={index}
                      className="w-8 h-16 bg-purple-100 rounded flex items-end"
                    >
                      <motion.div
                        initial={{ height: 0 }}
                        animate={{ height: `${(stat.apiCalls / 2800) * 100}%` }}
                        transition={{ duration: 0.5, delay: index * 0.1 }}
                        className="w-full bg-purple-500 rounded"
                      />
                    </div>
                  ))}
                </div>
                <p className="text-xs text-gray-500 mt-2">过去7天</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default SystemStats
