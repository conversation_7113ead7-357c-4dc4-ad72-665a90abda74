import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Wifi, 
  WifiOff, 
  Smartphone, 
  Monitor, 
  RotateCcw,
  Zap,
  Activity,
  Settings,
  X,
  ChevronUp,
  ChevronDown
} from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { 
  useBreakpoint, 
  useNetworkStatus, 
  useDeviceOrientation,
  usePerformanceMonitor,
  useVirtualKeyboard
} from '@/hooks/useMobile'

interface MobileOptimizationProps {
  className?: string
}

const MobileOptimization: React.FC<MobileOptimizationProps> = ({ className }) => {
  const [isExpanded, setIsExpanded] = useState(false)
  const [showDebugInfo, setShowDebugInfo] = useState(false)
  
  const { isMobile, isTablet, isDesktop, isTouchDevice } = useBreakpoint()
  const { isOnline, connectionType } = useNetworkStatus()
  const orientation = useDeviceOrientation()
  const { fps, memory } = usePerformanceMonitor()
  const isVirtualKeyboardVisible = useVirtualKeyboard()

  // 只在移动设备上显示
  if (!isMobile && !isTablet) {
    return null
  }

  const getConnectionColor = (type: string) => {
    switch (type) {
      case '4g':
      case '5g':
        return 'text-green-600 bg-green-50'
      case '3g':
        return 'text-yellow-600 bg-yellow-50'
      case '2g':
      case 'slow-2g':
        return 'text-red-600 bg-red-50'
      default:
        return 'text-gray-600 bg-gray-50'
    }
  }

  const getPerformanceColor = (fps: number) => {
    if (fps >= 50) return 'text-green-600'
    if (fps >= 30) return 'text-yellow-600'
    return 'text-red-600'
  }

  return (
    <div className={`fixed bottom-4 right-4 z-50 ${className}`}>
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ opacity: 0, y: 20, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: 20, scale: 0.95 }}
            className="mb-4"
          >
            <Card className="w-80 shadow-lg border-2">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg flex items-center space-x-2">
                    <Smartphone className="w-5 h-5" />
                    <span>移动端状态</span>
                  </CardTitle>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setIsExpanded(false)}
                  >
                    <X className="w-4 h-4" />
                  </Button>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* 设备信息 */}
                <div>
                  <h4 className="text-sm font-medium text-gray-700 mb-2">设备信息</h4>
                  <div className="grid grid-cols-2 gap-2 text-xs">
                    <div className="flex items-center space-x-1">
                      <Monitor className="w-3 h-3" />
                      <span>
                        {isMobile ? '手机' : isTablet ? '平板' : '桌面'}
                      </span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <RotateCcw className="w-3 h-3" />
                      <span>{orientation === 'portrait' ? '竖屏' : '横屏'}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <span>触摸:</span>
                      <Badge variant={isTouchDevice ? 'default' : 'secondary'} className="text-xs">
                        {isTouchDevice ? '支持' : '不支持'}
                      </Badge>
                    </div>
                    <div className="flex items-center space-x-1">
                      <span>键盘:</span>
                      <Badge variant={isVirtualKeyboardVisible ? 'default' : 'secondary'} className="text-xs">
                        {isVirtualKeyboardVisible ? '显示' : '隐藏'}
                      </Badge>
                    </div>
                  </div>
                </div>

                {/* 网络状态 */}
                <div>
                  <h4 className="text-sm font-medium text-gray-700 mb-2">网络状态</h4>
                  <div className="flex items-center space-x-3">
                    {isOnline ? (
                      <Wifi className="w-4 h-4 text-green-600" />
                    ) : (
                      <WifiOff className="w-4 h-4 text-red-600" />
                    )}
                    <span className="text-sm">
                      {isOnline ? '已连接' : '离线'}
                    </span>
                    {isOnline && (
                      <Badge className={`text-xs ${getConnectionColor(connectionType)}`}>
                        {connectionType.toUpperCase()}
                      </Badge>
                    )}
                  </div>
                </div>

                {/* 性能信息 */}
                <div>
                  <h4 className="text-sm font-medium text-gray-700 mb-2">性能信息</h4>
                  <div className="space-y-2 text-xs">
                    <div className="flex items-center justify-between">
                      <span>帧率 (FPS):</span>
                      <span className={`font-mono ${getPerformanceColor(fps)}`}>
                        {fps}
                      </span>
                    </div>
                    {memory && (
                      <div className="flex items-center justify-between">
                        <span>内存使用:</span>
                        <span className="font-mono">
                          {memory.used}MB / {memory.total}MB
                        </span>
                      </div>
                    )}
                  </div>
                </div>

                {/* 调试选项 */}
                <div className="border-t pt-3">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="debug-mode" className="text-sm">
                      调试模式
                    </Label>
                    <Switch
                      id="debug-mode"
                      checked={showDebugInfo}
                      onCheckedChange={setShowDebugInfo}
                    />
                  </div>
                </div>

                {/* 调试信息 */}
                {showDebugInfo && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    exit={{ opacity: 0, height: 0 }}
                    className="border-t pt-3"
                  >
                    <h4 className="text-sm font-medium text-gray-700 mb-2">调试信息</h4>
                    <div className="text-xs space-y-1 font-mono bg-gray-50 p-2 rounded">
                      <div>User Agent: {navigator.userAgent.slice(0, 50)}...</div>
                      <div>Screen: {window.screen.width}x{window.screen.height}</div>
                      <div>Viewport: {window.innerWidth}x{window.innerHeight}</div>
                      <div>Device Pixel Ratio: {window.devicePixelRatio}</div>
                      <div>Touch Points: {navigator.maxTouchPoints}</div>
                    </div>
                  </motion.div>
                )}
              </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>

      {/* 浮动按钮 */}
      <motion.div
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
      >
        <Button
          onClick={() => setIsExpanded(!isExpanded)}
          size="sm"
          className="rounded-full w-12 h-12 shadow-lg"
          variant={isExpanded ? "secondary" : "default"}
        >
          {isExpanded ? (
            <ChevronDown className="w-4 h-4" />
          ) : (
            <div className="relative">
              <Activity className="w-4 h-4" />
              {/* 网络状态指示器 */}
              <div className={`absolute -top-1 -right-1 w-2 h-2 rounded-full ${
                isOnline ? 'bg-green-500' : 'bg-red-500'
              }`} />
            </div>
          )}
        </Button>
      </motion.div>
    </div>
  )
}

export default MobileOptimization
