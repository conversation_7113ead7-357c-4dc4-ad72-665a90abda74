import { useState, useEffect, useCallback } from 'react'
import { 
  isMobile, 
  isTablet, 
  isDesktop, 
  isTouchDevice,
  getViewportSize,
  debounce,
  detectVirtualKeyboard
} from '@/lib/mobile'

// 响应式断点Hook
export const useBreakpoint = () => {
  const [breakpoint, setBreakpoint] = useState({
    isMobile: false,
    isTablet: false,
    isDesktop: false,
    isTouchDevice: false
  })

  useEffect(() => {
    const updateBreakpoint = () => {
      setBreakpoint({
        isMobile: isMobile(),
        isTablet: isTablet(),
        isDesktop: isDesktop(),
        isTouchDevice: isTouchDevice()
      })
    }

    updateBreakpoint()

    const debouncedUpdate = debounce(updateBreakpoint, 100)
    window.addEventListener('resize', debouncedUpdate)

    return () => {
      window.removeEventListener('resize', debouncedUpdate)
    }
  }, [])

  return breakpoint
}

// 视口尺寸Hook
export const useViewportSize = () => {
  const [size, setSize] = useState(getViewportSize())

  useEffect(() => {
    const updateSize = () => {
      setSize(getViewportSize())
    }

    const debouncedUpdate = debounce(updateSize, 100)
    window.addEventListener('resize', debouncedUpdate)

    return () => {
      window.removeEventListener('resize', debouncedUpdate)
    }
  }, [])

  return size
}

// 虚拟键盘检测Hook
export const useVirtualKeyboard = () => {
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    const cleanup = detectVirtualKeyboard(setIsVisible)
    return cleanup
  }, [])

  return isVisible
}

// 触摸手势Hook
export const useSwipeGesture = (
  onSwipe?: (direction: 'left' | 'right' | 'up' | 'down') => void,
  minDistance: number = 50
) => {
  const [startTouch, setStartTouch] = useState<Touch | null>(null)

  const handleTouchStart = useCallback((e: TouchEvent) => {
    setStartTouch(e.touches[0])
  }, [])

  const handleTouchEnd = useCallback((e: TouchEvent) => {
    if (!startTouch || !onSwipe) return

    const endTouch = e.changedTouches[0]
    const deltaX = endTouch.clientX - startTouch.clientX
    const deltaY = endTouch.clientY - startTouch.clientY
    const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY)

    if (distance >= minDistance) {
      let direction: 'left' | 'right' | 'up' | 'down'
      
      if (Math.abs(deltaX) > Math.abs(deltaY)) {
        direction = deltaX > 0 ? 'right' : 'left'
      } else {
        direction = deltaY > 0 ? 'down' : 'up'
      }
      
      onSwipe(direction)
    }

    setStartTouch(null)
  }, [startTouch, onSwipe, minDistance])

  return {
    onTouchStart: handleTouchStart,
    onTouchEnd: handleTouchEnd
  }
}

// 滚动位置Hook
export const useScrollPosition = () => {
  const [scrollPosition, setScrollPosition] = useState({
    x: 0,
    y: 0,
    direction: 'none' as 'up' | 'down' | 'left' | 'right' | 'none'
  })

  useEffect(() => {
    let lastScrollY = window.scrollY
    let lastScrollX = window.scrollX

    const updateScrollPosition = () => {
      const currentScrollY = window.scrollY
      const currentScrollX = window.scrollX

      let direction: 'up' | 'down' | 'left' | 'right' | 'none' = 'none'

      if (Math.abs(currentScrollY - lastScrollY) > Math.abs(currentScrollX - lastScrollX)) {
        direction = currentScrollY > lastScrollY ? 'down' : 'up'
      } else if (currentScrollX !== lastScrollX) {
        direction = currentScrollX > lastScrollX ? 'right' : 'left'
      }

      setScrollPosition({
        x: currentScrollX,
        y: currentScrollY,
        direction
      })

      lastScrollY = currentScrollY
      lastScrollX = currentScrollX
    }

    const debouncedUpdate = debounce(updateScrollPosition, 10)
    window.addEventListener('scroll', debouncedUpdate, { passive: true })

    return () => {
      window.removeEventListener('scroll', debouncedUpdate)
    }
  }, [])

  return scrollPosition
}

// 网络状态Hook
export const useNetworkStatus = () => {
  const [isOnline, setIsOnline] = useState(navigator.onLine)
  const [connectionType, setConnectionType] = useState<string>('unknown')

  useEffect(() => {
    const updateOnlineStatus = () => {
      setIsOnline(navigator.onLine)
    }

    const updateConnectionType = () => {
      const connection = (navigator as any).connection
      if (connection) {
        setConnectionType(connection.effectiveType || 'unknown')
      }
    }

    window.addEventListener('online', updateOnlineStatus)
    window.addEventListener('offline', updateOnlineStatus)

    if ('connection' in navigator) {
      const connection = (navigator as any).connection
      connection.addEventListener('change', updateConnectionType)
      updateConnectionType()
    }

    return () => {
      window.removeEventListener('online', updateOnlineStatus)
      window.removeEventListener('offline', updateOnlineStatus)
      
      if ('connection' in navigator) {
        const connection = (navigator as any).connection
        connection.removeEventListener('change', updateConnectionType)
      }
    }
  }, [])

  return { isOnline, connectionType }
}

// 设备方向Hook
export const useDeviceOrientation = () => {
  const [orientation, setOrientation] = useState<'portrait' | 'landscape'>('portrait')

  useEffect(() => {
    const updateOrientation = () => {
      if (window.innerHeight > window.innerWidth) {
        setOrientation('portrait')
      } else {
        setOrientation('landscape')
      }
    }

    updateOrientation()

    const debouncedUpdate = debounce(updateOrientation, 100)
    window.addEventListener('resize', debouncedUpdate)

    return () => {
      window.removeEventListener('resize', debouncedUpdate)
    }
  }, [])

  return orientation
}

// 长按检测Hook
export const useLongPress = (
  onLongPress: () => void,
  delay: number = 500
) => {
  const [isPressed, setIsPressed] = useState(false)

  const start = useCallback(() => {
    setIsPressed(true)
    const timeout = setTimeout(() => {
      onLongPress()
      setIsPressed(false)
    }, delay)

    const cleanup = () => {
      clearTimeout(timeout)
      setIsPressed(false)
    }

    const handleMouseUp = () => cleanup()
    const handleTouchEnd = () => cleanup()
    const handleMouseLeave = () => cleanup()

    document.addEventListener('mouseup', handleMouseUp)
    document.addEventListener('touchend', handleTouchEnd)
    document.addEventListener('mouseleave', handleMouseLeave)

    return () => {
      clearTimeout(timeout)
      document.removeEventListener('mouseup', handleMouseUp)
      document.removeEventListener('touchend', handleTouchEnd)
      document.removeEventListener('mouseleave', handleMouseLeave)
    }
  }, [onLongPress, delay])

  const handlers = {
    onMouseDown: start,
    onTouchStart: start
  }

  return { isPressed, ...handlers }
}

// 性能监控Hook
export const usePerformanceMonitor = () => {
  const [metrics, setMetrics] = useState({
    fps: 0,
    memory: null as { used: number; total: number; limit: number } | null,
    loadTime: 0
  })

  useEffect(() => {
    // FPS监控
    let frameCount = 0
    let lastTime = performance.now()

    const measureFPS = () => {
      frameCount++
      const currentTime = performance.now()
      
      if (currentTime - lastTime >= 1000) {
        setMetrics(prev => ({
          ...prev,
          fps: Math.round((frameCount * 1000) / (currentTime - lastTime))
        }))
        frameCount = 0
        lastTime = currentTime
      }
      
      requestAnimationFrame(measureFPS)
    }

    requestAnimationFrame(measureFPS)

    // 内存监控
    const updateMemory = () => {
      if ('memory' in performance) {
        const memory = (performance as any).memory
        setMetrics(prev => ({
          ...prev,
          memory: {
            used: Math.round(memory.usedJSHeapSize / 1048576),
            total: Math.round(memory.totalJSHeapSize / 1048576),
            limit: Math.round(memory.jsHeapSizeLimit / 1048576)
          }
        }))
      }
    }

    updateMemory()
    const memoryInterval = setInterval(updateMemory, 5000)

    // 页面加载时间
    if ('navigation' in performance) {
      const navigation = performance.navigation as any
      setMetrics(prev => ({
        ...prev,
        loadTime: navigation.loadEventEnd - navigation.navigationStart
      }))
    }

    return () => {
      clearInterval(memoryInterval)
    }
  }, [])

  return metrics
}
