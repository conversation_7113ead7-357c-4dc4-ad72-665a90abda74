<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理后台 - AugmentAPI</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>🔐</text></svg>">

    <!-- 引入Element Plus CSS - 本地资源 -->
    <link rel="stylesheet" href="/lib/element-plus/index.min.css">

    <!-- 引入Vue 3 - 本地资源 -->
    <script src="/lib/vue/vue.global.min.js"></script>
    <!-- 引入Element Plus - 本地资源 -->
    <script src="/lib/element-plus/index.full.min.js"></script>
    <!-- 引入Element Plus图标 - 本地资源 -->
    <script src="/lib/element-plus/icons.iife.min.js"></script>
    <!-- 引入Socket.IO客户端 - 本地资源 -->
    <script src="/lib/socket.io/socket.io.min.js"></script>

    <!-- 认证工具函数 -->
    <script src="/js/auth-utils.js"></script>
    <!-- 静默控制台（生产环境隐藏console输出；加 ?debug=1 显示） -->
    <script src="/js/quiet-console.js"></script>

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: #f5f7fa;
        }

        /* 修复下拉框显示问题 */
        .custom-select-dropdown {
            z-index: 99999 !important;
            max-height: 300px !important;
            min-width: 200px !important;
        }

        .el-select-dropdown {
            z-index: 99999 !important;
            max-height: 300px !important;
            min-width: 200px !important;
        }

        .el-popper {
            z-index: 99999 !important;
        }

        .el-select-dropdown__item {
            padding: 8px 12px !important;
            line-height: 1.5 !important;
        }

        /* 按钮组样式优化 */
        .el-radio-group {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .el-radio-button {
            margin-right: 0 !important;
        }

        .el-radio-button__inner {
            border-radius: 6px !important;
            padding: 8px 16px !important;
            font-size: 14px !important;
        }

        .layout-container {
            height: 100vh;
            display: flex;
        }

        .sidebar {
            background: #fff;
            box-shadow: 2px 0 6px rgba(0,21,41,.35);
            transition: width 0.3s;
            overflow: hidden;
            position: relative;
        }

        .sidebar.collapsed {
            width: 64px !important;
        }

        .sidebar.collapsed .user-info {
            justify-content: center;
            padding: 0;
        }

        .sidebar.collapsed .user-details {
            display: none;
        }

        .sidebar.collapsed .user-avatar {
            margin-right: 0;
            flex-shrink: 0;
        }

        .sidebar.collapsed .el-menu-item span,
        .sidebar.collapsed .el-sub-menu .el-sub-menu__title span {
            display: none;
        }

        .sidebar.collapsed .el-sub-menu .el-sub-menu__icon-arrow {
            display: none;
        }

        .sidebar.expanded {
            width: 250px !important;
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .content-header {
            background: #fff;
            padding: 16px 24px;
            border-bottom: 1px solid #e8eaec;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .content-body {
            flex: 1;
            padding: 24px;
            overflow-y: auto;
            background: #f5f7fa;
        }

        .user-info {
            display: flex;
            align-items: center;
            padding: 16px;
            border-bottom: 1px solid #e8eaec;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin-right: 12px;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #409eff;
            color: white;
            font-weight: bold;
        }

        .user-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .user-avatar:has(img) {
            background: transparent;
        }

        /* 公告铃铛按钮样式 */
        .bell-button {
            position: relative;
            padding: 8px 12px !important;
            border-radius: 50% !important;
            font-size: 18px !important;
            transition: all 0.3s ease !important;
            border: none !important;
        }

        .bell-button:hover {
            background-color: rgba(64, 158, 255, 0.1) !important;
            transform: scale(1.1);
        }

        .has-unread {
            position: relative;
            animation: bell-shake 2s infinite;
            color: #409eff !important;
        }

        .has-unread::after {
            content: '';
            position: absolute;
            top: 2px;
            right: 2px;
            width: 8px;
            height: 8px;
            background: #f56c6c;
            border-radius: 50%;
            border: 2px solid #fff;
            z-index: 1;
        }

        @keyframes bell-shake {
            0%, 50%, 100% { transform: rotate(0deg); }
            10%, 30% { transform: rotate(-10deg); }
            20%, 40% { transform: rotate(10deg); }
        }

        .user-details {
            flex: 1;
            min-width: 0;
        }

        .user-name {
            font-weight: 600;
            color: #303133;
            font-size: 14px;
            margin-bottom: 4px;
            display: flex;
            align-items: center;
        }

        .user-email {
            color: #909399;
            font-size: 12px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .trust-level {
            margin-left: 8px;
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 10px;
            font-weight: 600;
        }

        .trust-level-0 { background: #f4f4f5; color: #909399; }
        .trust-level-1 { background: #ecf5ff; color: #409eff; }
        .trust-level-2 { background: #f0f9ff; color: #67c23a; }
        .trust-level-3 { background: #fdf6ec; color: #e6a23c; }
        .trust-level-4 { background: #fef0f0; color: #f56c6c; }

        .sidebar-toggle {
            position: absolute;
            top: 16px;
            right: -12px;
            width: 24px;
            height: 24px;
            background: #409eff;
            border: none;
            border-radius: 50%;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            z-index: 1000;
        }

        .sidebar-toggle:hover {
            background: #337ecc;
        }

        /* 管理后台特定样式 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
            gap: 16px;
            margin-bottom: 24px;
        }

        .search-bar {
            margin-bottom: 16px;
            display: flex;
            gap: 16px;
            align-items: center;
        }

        .pagination-wrapper {
            margin-top: 16px;
            display: flex;
            justify-content: center;
        }

        /* 确保分页组件样式正确显示 */
        .pagination-wrapper .el-pagination {
            display: flex !important;
            justify-content: center !important;
            align-items: center !important;
            flex-wrap: wrap !important;
            gap: 8px !important;
        }

        .pagination-wrapper .el-pagination .el-pager,
        .pagination-wrapper .el-pagination .btn-prev,
        .pagination-wrapper .el-pagination .btn-next,
        .pagination-wrapper .el-pagination .el-pagination__sizes,
        .pagination-wrapper .el-pagination .el-pagination__total,
        .pagination-wrapper .el-pagination .el-pagination__jump {
            display: inline-block !important;
            margin: 0 4px !important;
            visibility: visible !important;
            opacity: 1 !important;
        }

        /* 强制显示分页按钮 */
        .el-pagination .el-pager li {
            display: inline-block !important;
            min-width: 30px !important;
            height: 30px !important;
            line-height: 30px !important;
            text-align: center !important;
            margin: 0 2px !important;
            background: #fff !important;
            border: 1px solid #dcdfe6 !important;
            border-radius: 4px !important;
            cursor: pointer !important;
        }

        .el-pagination .btn-prev,
        .el-pagination .btn-next {
            display: inline-block !important;
            min-width: 30px !important;
            height: 30px !important;
            line-height: 30px !important;
            text-align: center !important;
            background: #fff !important;
            border: 1px solid #dcdfe6 !important;
            border-radius: 4px !important;
            cursor: pointer !important;
        }

        /* 移动端适配 */
        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                left: 0;
                top: 0;
                height: 100vh;
                z-index: 1000;
                transform: translateX(-100%);
                transition: transform 0.3s;
            }

            .sidebar.mobile-open {
                transform: translateX(0);
            }

            .main-content {
                width: 100%;
            }

            .content-body {
                padding: 16px;
            }
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="layout-container">
            <!-- 侧边栏 -->
            <div class="sidebar" :class="{
                collapsed: isCollapsed,
                expanded: !isCollapsed,
                'mobile-open': mobileMenuOpen
            }">
                <button class="sidebar-toggle" @click="toggleSidebar">
                    {{ isCollapsed ? '→' : '←' }}
                </button>

                <!-- 用户信息 -->
                <div class="user-info" v-if="currentUser">
                    <div class="user-avatar">
                        <img v-if="currentUser.linuxdo_avatar" :src="currentUser.linuxdo_avatar" :alt="currentUser.linuxdo_username">
                        <span v-else>{{ currentUser.email ? currentUser.email.charAt(0).toUpperCase() : '?' }}</span>
                    </div>
                    <div class="user-details" v-show="!isCollapsed">
                        <div class="user-name">
                            {{ currentUser.linuxdo_username || currentUser.email.split('@')[0] }}
                            <span v-if="currentUser.linuxdo_trust_level !== null"
                                  :class="`trust-level trust-level-${currentUser.linuxdo_trust_level}`">
                                {{ getTrustLevelName(currentUser.linuxdo_trust_level) }}
                            </span>
                        </div>
                        <div class="user-email">{{ currentUser.email }}</div>
                    </div>
                </div>

                <!-- 导航菜单 -->
                <el-menu
                    :default-active="activeMenu"
                    :collapse="isCollapsed"
                    :unique-opened="true"
                    background-color="#fff"
                    text-color="#303133"
                    active-text-color="#409eff">

                    <el-menu-item index="home" @click="navigateTo('/')">
                        <el-icon><i class="el-icon-house"></i></el-icon>
                        <span>首页</span>
                    </el-menu-item>

                    <el-menu-item index="profile" @click="navigateTo('/profile')">
                        <el-icon><i class="el-icon-user"></i></el-icon>
                        <span>个人中心</span>
                    </el-menu-item>

                    <el-menu-item index="chat" @click="navigateTo('/chat')">
                        <el-icon><i class="el-icon-chat-dot-round"></i></el-icon>
                        <span>实时聊天</span>
                    </el-menu-item>

                    <el-sub-menu index="api">
                        <template #title>
                            <el-icon><i class="el-icon-document"></i></el-icon>
                            <span>API管理</span>
                        </template>
                        <el-menu-item index="api-docs" @click="navigateTo('/api-docs')">
                            <el-icon><i class="el-icon-reading"></i></el-icon>
                            <span>API文档</span>
                        </el-menu-item>
                        <el-menu-item index="external-test" @click="navigateTo('/external-test')">
                            <el-icon><i class="el-icon-connection"></i></el-icon>
                            <span>外部接口测试</span>
                        </el-menu-item>
                    </el-sub-menu>

                        <el-menu-item index="docs" @click="navigateTo('/docs')">
                            <el-icon><i class="el-icon-folder"></i></el-icon>
                            <span>文档中心</span>
                        </el-menu-item>


                    <!-- 管理后台菜单项（当前页面） -->
                    <el-menu-item index="admin" @click="navigateTo('/admin')">
                        <el-icon><i class="el-icon-setting"></i></el-icon>
                        <span>管理后台</span>
                    </el-menu-item>

                    <el-menu-item index="logout" @click="handleLogout">
                        <el-icon><i class="el-icon-switch-button"></i></el-icon>
                        <span>退出登录</span>
                    </el-menu-item>
                </el-menu>
            </div>

            <!-- 主内容区 -->
            <div class="main-content">
                <!-- 内容头部 -->
                <div class="content-header">
                    <div>
                        <el-button v-if="isMobile" @click="toggleMobileSidebar" icon="el-icon-menu" circle></el-button>
                        <span style="font-size: 18px; font-weight: 600; margin-left: 8px;">管理后台</span>
                    </div>
                    <div>
                        <el-button @click="showAnnouncementDialog" :class="{ 'has-unread': hasUnreadAnnouncements, 'bell-button': true }" text>
                            🔔
                        </el-button>
                    </div>
                </div>

                <!-- 内容主体 -->
                <div class="content-body">
                    <!-- 错误提示 -->
                    <el-alert v-if="error" :title="error" type="error" :closable="false" style="margin-bottom: 16px;"></el-alert>

                    <!-- 成功提示 -->
                    <el-alert v-if="success" :title="success" type="success" :closable="false" style="margin-bottom: 16px;"></el-alert>

                    <!-- 管理后台标签页 -->
                    <el-tabs v-model="activeTab" type="card" @tab-change="handleTabChange">
                        <!-- 仪表板 -->
                        <el-tab-pane label="📊 仪表板" name="dashboard">
                            <div style="background: white; border-radius: 8px; padding: 24px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                                <h2 style="margin-bottom: 24px;">📊 系统概览</h2>
                                <div class="stats-grid">
                                    <el-card shadow="hover">
                                        <div style="text-align: center;">
                                            <div style="font-size: 36px; font-weight: bold; color: #409EFF; margin-bottom: 8px;">
                                                {{ stats.totalUsers || 0 }}
                                            </div>
                                            <div style="color: #606266; font-size: 16px; margin-bottom: 4px;">总用户数</div>
                                            <div style="color: #909399; font-size: 12px;">
                                                <i class="el-icon-user"></i> 注册用户
                                            </div>
                                        </div>
                                    </el-card>

                                    <el-card shadow="hover">
                                        <div style="text-align: center;">
                                            <div style="font-size: 36px; font-weight: bold; color: #67C23A; margin-bottom: 8px;">
                                                {{ stats.totalTokens || 0 }}
                                            </div>
                                            <div style="color: #606266; font-size: 16px; margin-bottom: 4px;">总Token数</div>
                                            <div style="color: #909399; font-size: 12px;">
                                                <i class="el-icon-key"></i> 所有Token
                                            </div>
                                        </div>
                                    </el-card>

                                    <el-card shadow="hover">
                                        <div style="text-align: center;">
                                            <div style="font-size: 36px; font-weight: bold; color: #E6A23C; margin-bottom: 8px;">
                                                {{ stats.availableTokens || 0 }}
                                            </div>
                                            <div style="color: #606266; font-size: 16px; margin-bottom: 4px;">可用Token</div>
                                            <div style="color: #909399; font-size: 12px;">
                                                <i class="el-icon-circle-check"></i> 未分配
                                            </div>
                                        </div>
                                    </el-card>

                                    <el-card shadow="hover">
                                        <div style="text-align: center;">
                                            <div style="font-size: 36px; font-weight: bold; color: #F56C6C; margin-bottom: 8px;">
                                                {{ stats.usedTokens || 0 }}
                                            </div>
                                            <div style="color: #606266; font-size: 16px; margin-bottom: 4px;">已使用Token</div>
                                            <div style="color: #909399; font-size: 12px;">
                                                <i class="el-icon-circle-close"></i> 已分配
                                            </div>
                                        </div>
                                    </el-card>
                                </div>
                            </div>
                        </el-tab-pane>

                        <!-- 用户管理 -->
                        <el-tab-pane label="👥 用户管理" name="users">
                            <div style="background: white; border-radius: 8px; padding: 24px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 24px;">
                                    <h2>👥 用户管理</h2>
                                    <div style="display: flex; gap: 8px;">
                                        <el-button
                                            type="success"
                                            @click="batchAssignTokens"
                                            :disabled="selectedUsers.length === 0"
                                            :loading="loading">
                                            批量分配Token ({{ selectedUsers.length }})
                                        </el-button>
                                        <el-button
                                            type="warning"
                                            @click="batchRevokeTokens"
                                            :disabled="selectedUsers.length === 0"
                                            :loading="loading">
                                            批量取消Token ({{ selectedUsers.length }})
                                        </el-button>
                                        <el-button type="primary" @click="loadUsers" :loading="loading">刷新用户列表</el-button>
                                    </div>
                                </div>

                                <!-- 搜索和筛选栏 -->
                                <div class="search-bar" style="margin-bottom: 16px;">
                                    <div style="display: flex; gap: 12px; align-items: center; flex-wrap: wrap;">
                                        <!-- 搜索框 -->
                                        <el-input
                                            v-model="userSearch"
                                            placeholder="搜索用户邮箱或用户名"
                                            style="width: 250px;"
                                            clearable
                                            @input="handleUserSearch">
                                            <template #prefix>
                                                <i class="el-icon-search"></i>
                                            </template>
                                        </el-input>

                                        <!-- Token状态筛选 -->
                                        <el-select
                                            v-model="userFilters.tokenStatus"
                                            placeholder="Token状态"
                                            style="width: 140px;"
                                            clearable
                                            @change="handleUserFilterChange">
                                            <el-option label="全部" value=""></el-option>
                                            <el-option label="已分配Token" value="assigned"></el-option>
                                            <el-option label="未分配Token" value="unassigned"></el-option>
                                        </el-select>

                                        <!-- 信任等级筛选 -->
                                        <el-select
                                            v-model="userFilters.trustLevel"
                                            placeholder="信任等级"
                                            style="width: 120px;"
                                            clearable
                                            @change="handleUserFilterChange">
                                            <el-option label="全部" value=""></el-option>
                                            <el-option label="基础" value="0"></el-option>
                                            <el-option label="成员" value="1"></el-option>
                                            <el-option label="常规" value="2"></el-option>
                                            <el-option label="领袖" value="3"></el-option>
                                            <el-option label="长老" value="4"></el-option>
                                        </el-select>

                                        <!-- 排序选择 -->
                                        <el-select
                                            v-model="userFilters.sortBy"
                                            placeholder="排序字段"
                                            style="width: 120px;"
                                            @change="handleUserFilterChange">
                                            <el-option label="注册时间" value="created_at"></el-option>
                                            <el-option label="Token数量" value="token_count"></el-option>
                                            <el-option label="邮箱" value="email"></el-option>
                                            <el-option label="用户名" value="linuxdo_username"></el-option>
                                            <el-option label="信任等级" value="linuxdo_trust_level"></el-option>
                                        </el-select>

                                        <el-select
                                            v-model="userFilters.sortOrder"
                                            style="width: 80px;"
                                            @change="handleUserFilterChange">
                                            <el-option label="降序" value="desc"></el-option>
                                            <el-option label="升序" value="asc"></el-option>
                                        </el-select>

                                        <el-button @click="resetUserFilters">重置筛选</el-button>
                                    </div>
                                </div>
                                <div v-if="loading" style="text-align: center; padding: 40px; color: #909399;">加载中...</div>
                                <el-table
                                    v-else
                                    :data="users"
                                    style="width: 100%"
                                    stripe
                                    @selection-change="handleUserSelectionChange">
                                    <el-table-column type="selection" width="55"></el-table-column>
                                    <el-table-column label="头像" width="80">
                                        <template #default="scope">
                                            <el-avatar v-if="scope.row.linuxdo_avatar" :src="scope.row.linuxdo_avatar" :size="32"></el-avatar>
                                            <el-avatar v-else :size="32">{{ scope.row.email.charAt(0).toUpperCase() }}</el-avatar>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="uuid" label="UUID" width="120" sortable>
                                        <template #default="scope">
                                            <span style="font-family: monospace; font-size: 12px;">{{ scope.row.uuid.substring(0, 8) }}...</span>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="email" label="邮箱" min-width="200" sortable></el-table-column>
                                    <el-table-column label="LinuxDo用户名" min-width="150" sortable>
                                        <template #default="scope">
                                            {{ scope.row.linuxdo_username || '-' }}
                                        </template>
                                    </el-table-column>
                                    <el-table-column label="LinuxDo ID" width="100">
                                        <template #default="scope">
                                            {{ scope.row.linuxdo_id || '-' }}
                                        </template>
                                    </el-table-column>
                                    <el-table-column label="信任等级" width="100">
                                        <template #default="scope">
                                            <el-tag v-if="scope.row.linuxdo_trust_level !== null"
                                                    :type="getTrustLevelType(scope.row.linuxdo_trust_level)" size="small">
                                                {{ getTrustLevelName(scope.row.linuxdo_trust_level) }}
                                            </el-tag>
                                            <span v-else>-</span>
                                        </template>
                                    </el-table-column>
                                    <el-table-column label="Token数量" width="100">
                                        <template #default="scope">
                                            <el-tag type="primary" size="small">{{ scope.row.token_count || 0 }}</el-tag>
                                        </template>
                                    </el-table-column>
                                    <el-table-column label="API Token状态" width="120">
                                        <template #default="scope">
                                            <el-tag v-if="scope.row.auth_token" type="success" size="small">
                                                <i class="el-icon-check"></i> 已分配
                                            </el-tag>
                                            <el-tag v-else type="info" size="small">
                                                <i class="el-icon-close"></i> 未分配
                                            </el-tag>
                                        </template>
                                    </el-table-column>
                                    <el-table-column label="API Token" min-width="250">
                                        <template #default="scope">
                                            <div v-if="scope.row.auth_token" style="display: flex; align-items: center; gap: 8px;">
                                                <span style="font-family: monospace; font-size: 11px; flex: 1;">
                                                    {{ scope.row.auth_token }}
                                                </span>
                                                <el-button @click="copyToClipboard(scope.row.auth_token)" size="small" type="primary" text>复制</el-button>
                                            </div>
                                            <span v-else style="color: #909399;">未分配API Token</span>
                                        </template>
                                    </el-table-column>
                                    <el-table-column label="注册时间" width="180" sortable>
                                        <template #default="scope">
                                            {{ new Date(scope.row.created_at).toLocaleString() }}
                                        </template>
                                    </el-table-column>
                                    <el-table-column label="操作" width="200" fixed="right">
                                        <template #default="scope">
                                            <div style="display: flex; gap: 4px;">
                                                <el-button
                                                    v-if="!scope.row.auth_token"
                                                    type="primary"
                                                    size="small"
                                                    @click="assignUserToken(scope.row.uuid)"
                                                    :loading="loading">
                                                    分配Token
                                                </el-button>
                                                <el-button
                                                    v-else
                                                    type="warning"
                                                    size="small"
                                                    @click="revokeUserToken(scope.row.uuid)"
                                                    :loading="loading">
                                                    取消Token
                                                </el-button>
                                                <el-button type="danger" size="small" @click="deleteUser(scope.row.uuid)">删除</el-button>
                                            </div>
                                        </template>
                                    </el-table-column>
                                </el-table>

                                <!-- 用户分页 -->
                                <div class="pagination-wrapper" style="margin-top: 20px; padding: 16px; background: #f8f9fa; border-radius: 8px;">

                                    <!-- 自定义分页控件 -->
                                    <div style="display: flex; justify-content: center; align-items: center; gap: 8px; flex-wrap: wrap;">
                                        <!-- 每页数量选择 -->
                                        <el-select v-model="userPagination.pageSize" @change="handleUserPageSizeChange" style="width: 80px;">
                                            <el-option label="10" :value="10"></el-option>
                                            <el-option label="20" :value="20"></el-option>
                                            <el-option label="50" :value="50"></el-option>
                                            <el-option label="100" :value="100"></el-option>
                                        </el-select>

                                        <!-- 上一页按钮 -->
                                        <el-button
                                            :disabled="userPagination.currentPage <= 1"
                                            @click="handleUserPageChange(userPagination.currentPage - 1)"
                                            size="small">
                                            上一页
                                        </el-button>

                                        <!-- 页码显示 -->
                                        <span style="margin: 0 8px;">
                                            第 {{ userPagination.currentPage }} 页 / 共 {{ Math.ceil(userPagination.total / userPagination.pageSize) }} 页
                                        </span>

                                        <!-- 下一页按钮 -->
                                        <el-button
                                            :disabled="userPagination.currentPage >= Math.ceil(userPagination.total / userPagination.pageSize)"
                                            @click="handleUserPageChange(userPagination.currentPage + 1)"
                                            size="small">
                                            下一页
                                        </el-button>

                                        <!-- 跳转输入 -->
                                        <span style="margin-left: 16px;">跳转到</span>
                                        <el-input-number
                                            v-model="jumpToPage"
                                            :min="1"
                                            :max="Math.max(1, Math.ceil(userPagination.total / userPagination.pageSize))"
                                            size="small"
                                            style="width: 80px;"
                                            @change="handleUserPageChange(jumpToPage)">
                                        </el-input-number>
                                        <span>页</span>
                                    </div>
                                </div>
                            </div>
                        </el-tab-pane>

                        <!-- Token管理 -->
                        <el-tab-pane label="🔑 Token管理" name="tokens">
                            <div style="background: white; border-radius: 8px; padding: 24px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 24px;">
                                    <h2>🔑 Token管理</h2>
                                    <el-button type="primary" @click="loadTokens" :loading="loading">刷新Token列表</el-button>
                                </div>

                                <!-- 搜索栏 -->
                                <div class="search-bar">
                                    <el-input
                                        v-model="tokenSearch"
                                        placeholder="搜索Token、创建者邮箱或租户URL"
                                        style="width: 350px;"
                                        clearable
                                        @input="handleTokenSearch">
                                        <template #prefix>
                                            <i class="el-icon-search"></i>
                                        </template>
                                    </el-input>
                                    <el-button @click="resetTokenSearch">重置</el-button>
                                    <el-select v-model="tokenStatusFilter" placeholder="状态筛选" style="width: 120px;" @change="handleTokenSearch">
                                        <el-option label="全部" value=""></el-option>
                                        <el-option label="可用" value="available"></el-option>
                                        <el-option label="已使用" value="used"></el-option>
                                        <el-option label="已销毁" value="destroyed"></el-option>
                                    </el-select>
                                </div>
                                <div v-if="loading" style="text-align: center; padding: 40px; color: #909399;">加载中...</div>
                                <el-table v-else :data="tokens" style="width: 100%" stripe>
                                    <el-table-column prop="id" label="ID" width="80" sortable></el-table-column>
                                    <el-table-column prop="creator_email" label="创建者邮箱" min-width="200" sortable></el-table-column>
                                    <el-table-column label="创建者用户名" min-width="150">
                                        <template #default="scope">
                                            {{ scope.row.creator_username || '-' }}
                                        </template>
                                    </el-table-column>
                                    <el-table-column label="完整Token" min-width="400">
                                        <template #default="scope">
                                            <div style="display: flex; align-items: center; gap: 8px;">
                                                <el-input
                                                    :value="scope.row.token"
                                                    readonly
                                                    size="small"
                                                    style="font-family: monospace; font-size: 11px; flex: 1;">
                                                </el-input>
                                                <el-button @click="copyToClipboard(scope.row.token)" size="small" type="primary">复制</el-button>
                                            </div>
                                        </template>
                                    </el-table-column>
                                    <el-table-column label="租户URL" min-width="250">
                                        <template #default="scope">
                                            <span style="font-family: monospace; font-size: 12px;">{{ scope.row.tenant_url }}</span>
                                        </template>
                                    </el-table-column>
                                    <el-table-column label="使用者CK" min-width="150">
                                        <template #default="scope">
                                            <span v-if="scope.row.user_ck" style="font-family: monospace; font-size: 12px;">{{ scope.row.user_ck }}</span>
                                            <el-tag v-else type="info" size="small">未使用</el-tag>
                                        </template>
                                    </el-table-column>
                                    <el-table-column label="状态" width="100">
                                        <template #default="scope">
                                            <el-tag v-if="scope.row.destroy_time" type="danger" size="small">已销毁</el-tag>
                                            <el-tag v-else-if="scope.row.user_ck" type="success" size="small">已使用</el-tag>
                                            <el-tag v-else type="info" size="small">可用</el-tag>
                                        </template>
                                    </el-table-column>
                                    <el-table-column label="使用时间" width="180">
                                        <template #default="scope">
                                            {{ scope.row.use_time ? new Date(scope.row.use_time).toLocaleString() : '-' }}
                                        </template>
                                    </el-table-column>
                                    <el-table-column label="创建时间" width="180" sortable>
                                        <template #default="scope">
                                            {{ new Date(scope.row.created_at).toLocaleString() }}
                                        </template>
                                    </el-table-column>
                                    <el-table-column label="操作" width="120" fixed="right">
                                        <template #default="scope">
                                            <el-button type="danger" size="small" @click="deleteToken(scope.row.id)">删除</el-button>
                                        </template>
                                    </el-table-column>
                                </el-table>

                                <!-- Token分页 -->
                                <div class="pagination-wrapper" style="margin-top: 20px; padding: 16px; background: #f8f9fa; border-radius: 8px;">

                                    <!-- 自定义分页控件 -->
                                    <div style="display: flex; justify-content: center; align-items: center; gap: 8px; flex-wrap: wrap;">
                                        <!-- 每页数量选择 -->
                                        <el-select v-model="tokenPagination.pageSize" @change="handleTokenPageSizeChange" style="width: 80px;">
                                            <el-option label="10" :value="10"></el-option>
                                            <el-option label="20" :value="20"></el-option>
                                            <el-option label="50" :value="50"></el-option>
                                            <el-option label="100" :value="100"></el-option>
                                        </el-select>

                                        <!-- 上一页按钮 -->
                                        <el-button
                                            :disabled="tokenPagination.currentPage <= 1"
                                            @click="handleTokenPageChange(tokenPagination.currentPage - 1)"
                                            size="small">
                                            上一页
                                        </el-button>

                                        <!-- 页码显示 -->
                                        <span style="margin: 0 8px;">
                                            第 {{ tokenPagination.currentPage }} 页 / 共 {{ Math.ceil(tokenPagination.total / tokenPagination.pageSize) }} 页
                                        </span>

                                        <!-- 下一页按钮 -->
                                        <el-button
                                            :disabled="tokenPagination.currentPage >= Math.ceil(tokenPagination.total / tokenPagination.pageSize)"
                                            @click="handleTokenPageChange(tokenPagination.currentPage + 1)"
                                            size="small">
                                            下一页
                                        </el-button>

                                        <!-- 跳转输入 -->
                                        <span style="margin-left: 16px;">跳转到</span>
                                        <el-input-number
                                            v-model="jumpToTokenPage"
                                            :min="1"
                                            :max="Math.max(1, Math.ceil(tokenPagination.total / tokenPagination.pageSize))"
                                            size="small"
                                            style="width: 80px;"
                                            @change="handleTokenPageChange(jumpToTokenPage)">
                                        </el-input-number>
                                        <span>页</span>
                                    </div>
                                </div>
                            </div>
                        </el-tab-pane>

                        <!-- 公告管理 -->
                        <el-tab-pane label="📢 公告管理" name="announcements">
                            <div style="background: white; border-radius: 8px; padding: 24px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 24px;">
                                    <h2>📢 系统公告管理</h2>
                                    <el-button type="primary" @click="showCreateAnnouncementDialog">
                                        <el-icon><Plus /></el-icon>
                                        新建公告
                                    </el-button>
                                </div>

                                <!-- 公告列表 -->
                                <el-table :data="announcements" style="width: 100%" v-loading="announcementsLoading">
                                    <el-table-column prop="title" label="标题" min-width="200">
                                        <template #default="scope">
                                            <div style="display: flex; align-items: center; gap: 8px;">
                                                <el-tag v-if="scope.row.is_pinned" type="warning" size="small">置顶</el-tag>
                                                <span>{{ scope.row.title }}</span>
                                            </div>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="type" label="类型" width="100">
                                        <template #default="scope">
                                            <el-tag :type="getAnnouncementTypeColor(scope.row.type)" size="small">
                                                {{ getAnnouncementTypeText(scope.row.type) }}
                                            </el-tag>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="status" label="状态" width="100">
                                        <template #default="scope">
                                            <el-tag :type="scope.row.status === 'active' ? 'success' : 'info'" size="small">
                                                {{ scope.row.status === 'active' ? '激活' : '停用' }}
                                            </el-tag>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="author_name" label="发布人" width="120" />
                                    <el-table-column prop="created_at" label="创建时间" width="180">
                                        <template #default="scope">
                                            {{ formatDateTime(scope.row.created_at) }}
                                        </template>
                                    </el-table-column>
                                    <el-table-column label="操作" width="280" fixed="right">
                                        <template #default="scope">
                                            <el-button size="small" @click="editAnnouncement(scope.row)">编辑</el-button>
                                            <el-button
                                                size="small"
                                                :type="scope.row.status === 'active' ? 'warning' : 'success'"
                                                @click="toggleAnnouncementStatus(scope.row)">
                                                {{ scope.row.status === 'active' ? '停用' : '激活' }}
                                            </el-button>
                                            <el-button
                                                size="small"
                                                :type="scope.row.is_pinned ? 'info' : 'warning'"
                                                @click="toggleAnnouncementPin(scope.row)">
                                                {{ scope.row.is_pinned ? '取消置顶' : '置顶' }}
                                            </el-button>
                                            <el-button size="small" type="danger" @click="deleteAnnouncement(scope.row)">删除</el-button>
                                        </template>
                                    </el-table-column>
                                </el-table>

                                <!-- 分页 -->
                                <div style="margin-top: 20px; text-align: right;">
                                    <el-pagination
                                        v-model:current-page="announcementPagination.page"
                                        v-model:page-size="announcementPagination.limit"
                                        :page-sizes="[10, 20, 50, 100]"
                                        :total="announcementPagination.total"
                                        layout="total, sizes, prev, pager, next, jumper"
                                        @size-change="handleAnnouncementPageSizeChange"
                                        @current-change="handleAnnouncementPageChange">
                                    </el-pagination>
                                </div>
                            </div>
                        </el-tab-pane>



                        <!-- 多文档管理 -->
                        <el-tab-pane label="📚 文档库" name="documents">
                            <div style="background: white; border-radius: 8px; padding: 24px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 24px;">
                                    <h2>📚 文档管理</h2>
                                    <div>
                                        <el-button @click="migrateHelpDoc" type="warning" size="small">
                                            <el-icon><Upload /></el-icon>
                                            迁移帮助文档
                                        </el-button>
                                        <el-button @click="fixDocumentSlugs" type="info" size="small">
                                            <el-icon><Tools /></el-icon>
                                            修复URL标识
                                        </el-button>
                                        <el-button type="primary" @click="showCreateDocumentDialog">
                                            <el-icon><Plus /></el-icon>
                                            新建文档
                                        </el-button>
                                    </div>
                                </div>

                                <!-- 搜索和筛选 -->
                                <div style="margin-bottom: 16px; display: flex; gap: 16px; align-items: center; flex-wrap: wrap;">
                                    <el-input
                                        v-model="documentSearch"
                                        placeholder="搜索文档标题或内容..."
                                        style="width: 300px;"
                                        @input="handleDocumentSearch">
                                        <template #prefix>
                                            <el-icon><Search /></el-icon>
                                        </template>
                                    </el-input>
                                    <el-select v-model="documentCategoryFilter" placeholder="选择分类" style="width: 150px;" @change="handleDocumentCategoryFilter" :teleported="true">
                                        <el-option label="全部分类" value=""></el-option>
                                        <el-option label="用户指南" value="guide"></el-option>
                                        <el-option label="API文档" value="api"></el-option>
                                        <el-option label="常见问题" value="faq"></el-option>
                                        <el-option label="其他" value="general"></el-option>
                                    </el-select>
                                    <el-select v-model="documentStatusFilter" placeholder="选择状态" style="width: 120px;" @change="handleDocumentStatusFilter" :teleported="true">
                                        <el-option label="全部状态" value=""></el-option>
                                        <el-option label="已发布" value="published"></el-option>
                                        <el-option label="草稿" value="draft"></el-option>
                                        <el-option label="已归档" value="archived"></el-option>
                                    </el-select>
                                    <el-button @click="resetDocumentSearch">重置</el-button>
                                </div>

                                <!-- 文档列表 -->
                                <el-table :data="documents" style="width: 100%" v-loading="documentsLoading">
                                    <el-table-column prop="title" label="标题" min-width="200" />
                                    <el-table-column prop="category" label="分类" width="120">
                                        <template #default="scope">
                                            <el-tag :type="getDocumentCategoryColor(scope.row.category)" size="small">
                                                {{ getDocumentCategoryText(scope.row.category) }}
                                            </el-tag>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="status" label="状态" width="100">
                                        <template #default="scope">
                                            <el-tag :type="getDocumentStatusColor(scope.row.status)" size="small">
                                                {{ getDocumentStatusText(scope.row.status) }}
                                            </el-tag>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="slug" label="URL标识" width="150">
                                        <template #default="scope">
                                            <el-link :href="`/docs/${scope.row.slug}`" target="_blank" type="primary">
                                                {{ scope.row.slug }}
                                            </el-link>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="author_name" label="作者" width="120" />
                                    <el-table-column prop="created_at" label="创建时间" width="180">
                                        <template #default="scope">
                                            {{ formatDateTime(scope.row.created_at) }}
                                        </template>
                                    </el-table-column>
                                    <el-table-column label="操作" width="200" fixed="right">
                                        <template #default="scope">
                                            <el-button size="small" @click="editDocument(scope.row)">编辑</el-button>
                                            <el-button size="small" type="info" @click="previewDocument(scope.row)">预览</el-button>
                                            <el-button size="small" type="danger" @click="deleteDocument(scope.row)">删除</el-button>
                                        </template>
                                    </el-table-column>
                                </el-table>

                                <!-- 分页 -->
                                <div style="margin-top: 20px; text-align: right;">
                                    <el-pagination
                                        v-model:current-page="documentPagination.page"
                                        v-model:page-size="documentPagination.limit"
                                        :page-sizes="[10, 20, 50, 100]"
                                        :total="documentPagination.total"
                                        layout="total, sizes, prev, pager, next, jumper"
                                        @size-change="handleDocumentPageSizeChange"
                                        @current-change="handleDocumentPageChange">
                                    </el-pagination>
                                </div>
                            </div>
                        </el-tab-pane>

                        <!-- 系统信息 -->
                        <el-tab-pane label="⚙️ 系统信息" name="system">
                            <div style="background: white; border-radius: 8px; padding: 24px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                                <h2 style="margin-bottom: 24px;">⚙️ 系统信息</h2>
                                <el-descriptions :column="2" border>
                                    <el-descriptions-item label="Node.js版本">v22.15.1</el-descriptions-item>
                                    <el-descriptions-item label="运行平台">win32</el-descriptions-item>
                                    <el-descriptions-item label="运行时长">0天0时1分</el-descriptions-item>
                                    <el-descriptions-item label="内存使用">17.2MB</el-descriptions-item>
                                </el-descriptions>
                            </div>
                        </el-tab-pane>
                    </el-tabs>
                </div>
            </div>
        </div>

        <!-- 公告创建/编辑对话框 -->
        <el-dialog
            v-model="announcementDialogVisible"
            :title="announcementDialogMode === 'create' ? '新建公告' : '编辑公告'"
            width="600px"
            @close="resetAnnouncementForm">
            <el-form :model="announcementForm" label-width="80px">
                <el-form-item label="标题" required>
                    <el-input v-model="announcementForm.title" placeholder="请输入公告标题" />
                </el-form-item>
                <el-form-item label="内容" required>
                    <el-input
                        v-model="announcementForm.content"
                        type="textarea"
                        :rows="6"
                        placeholder="请输入公告内容" />
                </el-form-item>
                <el-form-item label="类型">
                    <el-select v-model="announcementForm.type" placeholder="选择公告类型">
                        <el-option label="信息" value="info" />
                        <el-option label="警告" value="warning" />
                        <el-option label="成功" value="success" />
                        <el-option label="错误" value="error" />
                    </el-select>
                </el-form-item>
                <el-form-item label="状态">
                    <el-select v-model="announcementForm.status" placeholder="选择公告状态">
                        <el-option label="激活" value="active" />
                        <el-option label="停用" value="inactive" />
                    </el-select>
                </el-form-item>
                <el-form-item label="置顶">
                    <el-switch v-model="announcementForm.is_pinned" />
                </el-form-item>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="announcementDialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="saveAnnouncement" :loading="announcementSaving">
                        {{ announcementDialogMode === 'create' ? '创建' : '保存' }}
                    </el-button>
                </span>
            </template>
        </el-dialog>

        <!-- 文档创建/编辑对话框 -->
        <el-dialog
            v-model="documentDialogVisible"
            :title="documentDialogMode === 'create' ? '新建文档' : '编辑文档'"
            width="800px"
            :append-to-body="true"
            :modal-append-to-body="true"
            @close="resetDocumentForm">
            <el-form :model="documentForm" label-width="80px">
                <el-form-item label="标题" required>
                    <el-input v-model="documentForm.title" placeholder="请输入文档标题" />
                </el-form-item>
                <el-form-item label="URL标识" required>
                    <el-input v-model="documentForm.slug" placeholder="请输入URL标识符，如：user-guide" />
                    <div style="font-size: 12px; color: #909399; margin-top: 4px;">
                        URL标识符用于生成文档访问链接，只能包含字母、数字、连字符，如：user-guide、api-docs
                    </div>
                </el-form-item>
                <el-form-item label="分类">
                    <el-radio-group v-model="documentForm.category" style="width: 100%;">
                        <el-radio-button label="guide">用户指南</el-radio-button>
                        <el-radio-button label="api">API文档</el-radio-button>
                        <el-radio-button label="faq">常见问题</el-radio-button>
                        <el-radio-button label="general">其他</el-radio-button>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="状态">
                    <el-radio-group v-model="documentForm.status" style="width: 100%;">
                        <el-radio-button label="published">已发布</el-radio-button>
                        <el-radio-button label="draft">草稿</el-radio-button>
                        <el-radio-button label="archived">已归档</el-radio-button>
                    </el-radio-group>
                </el-form-item>
                <el-form-item label="排序权重">
                    <el-input-number v-model="documentForm.sort_order" :min="0" :max="999" />
                </el-form-item>
                <el-form-item label="内容" required>
                    <div style="border: 1px solid #dcdfe6; border-radius: 4px;">
                        <div style="background: #f5f7fa; padding: 8px 16px; border-bottom: 1px solid #dcdfe6; font-size: 14px; color: #606266;">
                            Markdown 编辑器 - 支持标准 Markdown 语法
                        </div>
                        <el-input
                            v-model="documentForm.content"
                            type="textarea"
                            :rows="15"
                            placeholder="请输入文档内容，支持 Markdown 语法..."
                            style="border: none;"
                            resize="vertical">
                        </el-input>
                    </div>
                </el-form-item>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="documentDialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="saveDocument" :loading="documentSaving">
                        {{ documentDialogMode === 'create' ? '创建' : '保存' }}
                    </el-button>
                </span>
            </template>
        </el-dialog>

        <!-- 系统公告弹窗 -->
        <el-dialog
            v-model="notificationDialogVisible"
            width="500px"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
            :show-close="true"
            class="announcement-dialog">

            <template #header>
                <span style="font-size: 18px; font-weight: 600;">系统公告</span>
            </template>

            <div v-if="currentNotificationAnnouncement" class="announcement-dialog-content">
                <div class="announcement-header">
                    <div class="announcement-title">
                        <el-tag v-if="currentNotificationAnnouncement.is_pinned" type="warning" size="small" style="margin-right: 8px;">置顶</el-tag>
                        <el-tag :type="getAnnouncementTypeColor(currentNotificationAnnouncement.type)" size="small" style="margin-right: 8px;">
                            {{ getAnnouncementTypeText(currentNotificationAnnouncement.type) }}
                        </el-tag>
                        <span style="font-size: 16px; font-weight: 600;">{{ currentNotificationAnnouncement.title }}</span>
                    </div>
                    <div class="announcement-meta" style="margin-top: 8px; color: #909399; font-size: 12px;">
                        发布时间：{{ formatDate(currentNotificationAnnouncement.created_at) }}
                        <span v-if="currentNotificationAnnouncement.author_name" style="margin-left: 16px;">
                            发布人：{{ currentNotificationAnnouncement.author_name }}
                        </span>
                    </div>
                    <el-divider />
                </div>
                <div class="announcement-content" style="line-height: 1.8; white-space: pre-wrap;">
                    {{ currentNotificationAnnouncement.content }}
                </div>
            </div>

            <template #footer>
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <div style="color: #909399; font-size: 12px;">
                        {{ currentNotificationIndex + 1 }} / {{ notificationAnnouncements.length }}
                    </div>
                    <div>
                        <el-button type="primary" @click="notificationDialogVisible = false">
                            知道了
                        </el-button>
                    </div>
                </div>
            </template>
        </el-dialog>

        <!-- 移动端遮罩 -->
        <div v-if="isMobile && mobileMenuOpen"
             style="position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.5); z-index: 999;"
             @click="closeMobileSidebar"></div>
    </div>

    <!-- CDN资源已由服务端自动注入到head部分 -->

    <script>
        const { createApp, ref, reactive, onMounted, computed } = Vue;

        createApp({
            setup() {
                // 响应式数据
                const isCollapsed = ref(false);
                const mobileMenuOpen = ref(false);
                const currentUser = ref(null);
                const activeMenu = ref('admin');

                // 公告通知数据（与管理功能分开）
                const notificationAnnouncements = ref([]);
                const notificationDialogVisible = ref(false);
                const currentNotificationAnnouncement = ref(null);
                const currentNotificationIndex = ref(0);
                const hasUnreadAnnouncements = ref(false);
                const activeTab = ref('dashboard');
                const loading = ref(false);
                const error = ref('');
                const success = ref('');
                const adminInfo = ref(null);
                const stats = ref({});
                const users = ref([]);
                const tokens = ref([]);

                // 使用统一的认证工具
                const { getAuthToken, authenticatedFetch, clearAuthToken } = window.AuthUtils;

                // 公告管理相关数据
                const announcements = ref([]);
                const announcementsLoading = ref(false);
                const announcementPagination = ref({
                    page: 1,
                    limit: 10,
                    total: 0
                });
                const announcementDialogVisible = ref(false);
                const announcementDialogMode = ref('create'); // 'create' or 'edit'
                const announcementSaving = ref(false);
                const announcementForm = ref({
                    uuid: '',
                    title: '',
                    content: '',
                    type: 'info',
                    status: 'active',
                    is_pinned: false
                });
                const selectedUsers = ref([]);

                // 移除旧的帮助文档管理数据


                // 多文档管理相关数据
                const documents = ref([]);
                const documentsLoading = ref(false);
                const documentPagination = ref({
                    page: 1,
                    limit: 20,
                    total: 0
                });
                const documentSearch = ref('');
                const documentCategoryFilter = ref('');
                const documentStatusFilter = ref('');
                const documentDialogVisible = ref(false);
                const documentDialogMode = ref('create'); // 'create' or 'edit'
                const documentSaving = ref(false);
                const documentForm = ref({
                    uuid: '',
                    title: '',
                    slug: '',
                    content: '',
                    category: 'general',
                    status: 'published',
                    sort_order: 0
                });

                // 用户管理分页和搜索
                const userSearch = ref('');
                const userFilters = ref({
                    tokenStatus: '', // all, assigned, unassigned
                    trustLevel: '', // 0,1,2,3,4
                    sortBy: 'created_at', // created_at, token_count, email, linuxdo_username, linuxdo_trust_level
                    sortOrder: 'desc' // asc, desc
                });
                const userPagination = ref({
                    currentPage: 1,
                    pageSize: 20,
                    total: 0
                });
                const jumpToPage = ref(1);

                // Token管理分页和搜索
                const tokenSearch = ref('');
                const tokenStatusFilter = ref('');
                const tokenPagination = ref({
                    currentPage: 1,
                    pageSize: 20,
                    total: 0
                });
                const jumpToTokenPage = ref(1);

                // 分页相关（移除重复的分页变量，使用上面已定义的userPagination和tokenPagination）

                // 计算属性
                const isMobile = computed(() => {
                    return window.innerWidth <= 768;
                });

                // 基础方法
                const toggleSidebar = () => {
                    isCollapsed.value = !isCollapsed.value;
                };

                const toggleMobileSidebar = () => {
                    mobileMenuOpen.value = !mobileMenuOpen.value;
                };

                const closeMobileSidebar = () => {
                    mobileMenuOpen.value = false;
                };

                const navigateTo = (path) => {
                    window.location.href = path;
                };

                const refreshPage = () => {
                    window.location.reload();
                };

                const handleLogout = async () => {
                    try {
                        const response = await fetch('/api/logout', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            }
                        });

                        if (response.ok) {
                            clearAuthToken();
                            window.location.href = '/login';
                        } else {
                            ElementPlus.ElMessage.error('登出失败，请重试');
                        }
                    } catch (error) {
                        console.error('登出错误:', error);
                        ElementPlus.ElMessage.error('网络错误，请重试');
                    }
                };

                const getTrustLevelName = (level) => {
                    const levelNames = {
                        0: '新用户',
                        1: '基础',
                        2: '成员',
                        3: '常规',
                        4: '领导'
                    };
                    return levelNames[level] || '未知';
                };

                const getTrustLevelType = (level) => {
                    const levelTypes = {
                        0: '',
                        1: 'info',
                        2: 'success',
                        3: 'warning',
                        4: 'danger'
                    };
                    return levelTypes[level] || '';
                };

                const checkLoginStatus = async () => {
                    try {
                        const token = getAuthToken();
                        if (!token) {
                            window.location.href = '/login';
                            return false;
                        }

                        const response = await fetch('/api/user', {
                            headers: {
                                'Authorization': `Bearer ${token}`,
                                'Content-Type': 'application/json'
                            }
                        });

                        if (response.ok) {
                            const result = await response.json();
                            currentUser.value = result.user;
                            return true;
                        } else if (response.status === 401) {
                            clearAuthToken();
                            window.location.href = '/login';
                            return false;
                        }
                    } catch (error) {
                        console.error('检查登录状态失败:', error);
                        return false;
                    }
                };

                const showMessage = (message, type = 'success') => {
                    if (type === 'success') {
                        success.value = message;
                        error.value = '';
                    } else {
                        error.value = message;
                        success.value = '';
                    }

                    // 3秒后清除消息
                    setTimeout(() => {
                        success.value = '';
                        error.value = '';
                    }, 3000);
                };

                const checkAdminAccess = async () => {
                    try {
                        const token = getAuthToken();
                        if (!token) {
                            showMessage('未找到认证token，请先登录', 'error');
                            setTimeout(() => {
                                window.location.href = '/login';
                            }, 2000);
                            return false;
                        }

                        const response = await fetch('/api/admin/me', {
                            headers: {
                                'Authorization': `Bearer ${token}`,
                                'Content-Type': 'application/json'
                            }
                        });

                        if (response.ok) {
                            const result = await response.json();
                            if (result.success) {
                                adminInfo.value = result.admin;
                                return true;
                            }
                        } else if (response.status === 401) {
                            clearAuthToken();
                            showMessage('认证失败，请重新登录', 'error');
                            setTimeout(() => {
                                window.location.href = '/login';
                            }, 2000);
                            return false;
                        } else if (response.status === 403) {
                            showMessage('权限不足，您不是管理员', 'error');
                            setTimeout(() => {
                                window.location.href = '/profile';
                            }, 2000);
                            return false;
                        }
                        throw new Error('权限验证失败');
                    } catch (error) {
                        showMessage('权限验证失败，请重试', 'error');
                        setTimeout(() => {
                            window.location.href = '/profile';
                        }, 2000);
                        return false;
                    }
                };

                const loadStats = async () => {
                    try {
                        const response = await authenticatedFetch('/api/admin/stats');

                        if (response.ok) {
                            const result = await response.json();
                            if (result.success) {
                                stats.value = result.stats;
                            }
                        }
                    } catch (error) {
                        console.error('加载统计数据失败:', error);
                        if (error.message !== '认证失败') {
                            ElementPlus.ElMessage.error('加载统计数据失败');
                        }
                    }
                };

                const loadUsers = async () => {
                    loading.value = true;
                    try {
                        console.log('开始加载用户列表...');

                        // 构建查询参数
                        const params = new URLSearchParams({
                            limit: userPagination.value.pageSize,
                            offset: (userPagination.value.currentPage - 1) * userPagination.value.pageSize
                        });

                        if (userSearch.value.trim()) {
                            params.append('search', userSearch.value.trim());
                        }

                        // 添加筛选参数
                        if (userFilters.value.tokenStatus) {
                            params.append('token_status', userFilters.value.tokenStatus);
                        }
                        if (userFilters.value.trustLevel !== '') {
                            params.append('trust_level', userFilters.value.trustLevel);
                        }
                        if (userFilters.value.sortBy) {
                            params.append('sort_by', userFilters.value.sortBy);
                        }
                        if (userFilters.value.sortOrder) {
                            params.append('sort_order', userFilters.value.sortOrder);
                        }

                        const response = await authenticatedFetch(`/api/admin/users?${params}`);
                        console.log('用户API响应状态:', response.status);

                        if (response.ok) {
                            const result = await response.json();
                            console.log('用户API响应数据:', result);

                            if (result.success) {
                                users.value = result.users;
                                userPagination.value.total = result.pagination.total;
                                console.log('用户数据已设置:', users.value.length, '条记录，总计', result.pagination.total);
                                console.log('前几个用户的UUID:', result.users.slice(0, 3).map(u => ({ email: u.email, uuid: u.uuid, type: typeof u.uuid })));
                                showMessage(`成功加载${result.users.length}个用户`);
                            } else {
                                throw new Error(result.error || '加载用户列表失败');
                            }
                        } else {
                            const errorText = await response.text();
                            console.error('用户API错误响应:', errorText);
                            throw new Error(`HTTP ${response.status}: ${errorText}`);
                        }
                    } catch (error) {
                        console.error('加载用户失败:', error);
                        showMessage(error.message, 'error');
                    } finally {
                        loading.value = false;
                    }
                };

                const loadTokens = async () => {
                    loading.value = true;
                    try {
                        console.log('开始加载Token列表...');

                        // 构建查询参数
                        const params = new URLSearchParams({
                            limit: tokenPagination.value.pageSize,
                            offset: (tokenPagination.value.currentPage - 1) * tokenPagination.value.pageSize
                        });

                        if (tokenSearch.value.trim()) {
                            params.append('search', tokenSearch.value.trim());
                        }

                        if (tokenStatusFilter.value) {
                            params.append('status', tokenStatusFilter.value);
                        }

                        const response = await authenticatedFetch(`/api/admin/tokens?${params}`);
                        console.log('Token API响应状态:', response.status);

                        if (response.ok) {
                            const result = await response.json();
                            console.log('Token API响应数据:', result);

                            if (result.success) {
                                tokens.value = result.tokens;
                                tokenPagination.value.total = result.pagination.total;
                                console.log('Token数据已设置:', tokens.value.length, '条记录，总计', result.pagination.total);
                                showMessage(`成功加载${result.tokens.length}个Token`);
                            } else {
                                throw new Error(result.error || '加载Token列表失败');
                            }
                        } else {
                            const errorText = await response.text();
                            console.error('Token API错误响应:', errorText);
                            throw new Error(`HTTP ${response.status}: ${errorText}`);
                        }
                    } catch (error) {
                        console.error('加载Token失败:', error);
                        showMessage(error.message, 'error');
                    } finally {
                        loading.value = false;
                    }
                };

                const refreshData = async () => {
                    await loadStats();
                    if (activeTab.value === 'users') {
                        await loadUsers();
                    } else if (activeTab.value === 'tokens') {
                        await loadTokens();
                    }
                    showMessage('数据已刷新');
                };

                const deleteUser = async (userUuid) => {
                    try {
                        console.log('删除用户，UUID:', userUuid, '类型:', typeof userUuid);
                        const confirmed = await ElementPlus.ElMessageBox.confirm(
                            '确定要删除这个用户吗？此操作不可恢复。',
                            '确认删除',
                            {
                                confirmButtonText: '确定',
                                cancelButtonText: '取消',
                                type: 'warning'
                            }
                        );

                        if (confirmed) {
                            const response = await authenticatedFetch(`/api/admin/users/${userUuid}`, {
                                method: 'DELETE'
                            });

                            if (response.ok) {
                                showMessage('用户删除成功');
                                await loadUsers();
                            } else {
                                const errorData = await response.json().catch(() => ({}));
                                const errorMessage = errorData.error || `删除用户失败 (${response.status})`;
                                throw new Error(errorMessage);
                            }
                        }
                    } catch (error) {
                        if (error !== 'cancel') {
                            showMessage(error.message || '删除用户失败', 'error');
                        }
                    }
                };

                const deleteToken = async (tokenId) => {
                    try {
                        const confirmed = await ElementPlus.ElMessageBox.confirm(
                            '确定要删除这个Token吗？此操作不可恢复。',
                            '确认删除',
                            {
                                confirmButtonText: '确定',
                                cancelButtonText: '取消',
                                type: 'warning'
                            }
                        );

                        if (confirmed) {
                            const response = await fetch(`/api/admin/tokens/${tokenId}`, {
                                method: 'DELETE'
                            });

                            if (response.ok) {
                                showMessage('Token删除成功');
                                await loadTokens();
                            } else {
                                throw new Error('删除Token失败');
                            }
                        }
                    } catch (error) {
                        if (error !== 'cancel') {
                            showMessage(error.message || '删除Token失败', 'error');
                        }
                    }
                };

                const copyToClipboard = async (text) => {
                    try {
                        await navigator.clipboard.writeText(text);
                        ElementPlus.ElMessage.success('已复制到剪贴板');
                    } catch (error) {
                        // 降级方案
                        const textArea = document.createElement('textarea');
                        textArea.value = text;
                        document.body.appendChild(textArea);
                        textArea.select();
                        document.execCommand('copy');
                        document.body.removeChild(textArea);
                        ElementPlus.ElMessage.success('已复制到剪贴板');
                    }
                };

                // 用户选择处理
                const handleUserSelectionChange = (selection) => {
                    selectedUsers.value = selection;
                };

                // 为用户分配API Token
                const assignUserToken = async (userUuid) => {
                    try {
                        const confirmed = await ElementPlus.ElMessageBox.confirm(
                            '确定要为这个用户分配API Token吗？',
                            '确认分配',
                            {
                                confirmButtonText: '确定',
                                cancelButtonText: '取消',
                                type: 'info'
                            }
                        );

                        if (confirmed) {
                            const response = await fetch(`/api/admin/users/${userUuid}/assign-token`, {
                                method: 'POST'
                            });

                            if (response.ok) {
                                const result = await response.json();
                                if (result.success) {
                                    showMessage(`API Token分配成功: ${result.token}`);
                                    await loadUsers(); // 刷新用户列表
                                } else {
                                    throw new Error(result.error);
                                }
                            } else {
                                throw new Error('分配API Token失败');
                            }
                        }
                    } catch (error) {
                        if (error !== 'cancel') {
                            showMessage(error.message || '分配API Token失败', 'error');
                        }
                    }
                };

                // 取消用户的API Token
                const revokeUserToken = async (userUuid) => {
                    try {
                        const confirmed = await ElementPlus.ElMessageBox.confirm(
                            '确定要取消这个用户的API Token吗？取消后用户将无法使用API。',
                            '确认取消',
                            {
                                confirmButtonText: '确定',
                                cancelButtonText: '取消',
                                type: 'warning'
                            }
                        );

                        if (confirmed) {
                            const response = await fetch(`/api/admin/users/${userUuid}/revoke-token`, {
                                method: 'DELETE'
                            });

                            if (response.ok) {
                                const result = await response.json();
                                if (result.success) {
                                    showMessage('API Token已取消');
                                    await loadUsers(); // 刷新用户列表
                                } else {
                                    throw new Error(result.error);
                                }
                            } else {
                                throw new Error('取消API Token失败');
                            }
                        }
                    } catch (error) {
                        if (error !== 'cancel') {
                            showMessage(error.message || '取消API Token失败', 'error');
                        }
                    }
                };

                // 批量分配API Token
                const batchAssignTokens = async () => {
                    if (selectedUsers.value.length === 0) {
                        showMessage('请先选择要分配Token的用户', 'warning');
                        return;
                    }

                    try {
                        const confirmed = await ElementPlus.ElMessageBox.confirm(
                            `确定要为选中的 ${selectedUsers.value.length} 个用户批量分配API Token吗？`,
                            '确认批量分配',
                            {
                                confirmButtonText: '确定',
                                cancelButtonText: '取消',
                                type: 'info'
                            }
                        );

                        if (confirmed) {
                            const userUuids = selectedUsers.value.map(user => user.uuid);

                            const response = await fetch('/api/admin/users/batch-assign-tokens', {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json'
                                },
                                body: JSON.stringify({ userUuids })
                            });

                            if (response.ok) {
                                const result = await response.json();
                                if (result.success) {
                                    showMessage(result.message);
                                    selectedUsers.value = []; // 清空选择
                                    await loadUsers(); // 刷新用户列表
                                } else {
                                    throw new Error(result.error);
                                }
                            } else {
                                throw new Error('批量分配API Token失败');
                            }
                        }
                    } catch (error) {
                        if (error !== 'cancel') {
                            showMessage(error.message || '批量分配API Token失败', 'error');
                        }
                    }
                };

                // 批量取消API Token
                const batchRevokeTokens = async () => {
                    if (selectedUsers.value.length === 0) {
                        showMessage('请先选择要取消Token的用户', 'warning');
                        return;
                    }

                    try {
                        const confirmed = await ElementPlus.ElMessageBox.confirm(
                            `确定要为选中的 ${selectedUsers.value.length} 个用户批量取消API Token吗？`,
                            '确认批量取消',
                            {
                                confirmButtonText: '确定',
                                cancelButtonText: '取消',
                                type: 'warning'
                            }
                        );

                        if (confirmed) {
                            loading.value = true;
                            const userUuids = selectedUsers.value.map(user => user.uuid);

                            const response = await authenticatedFetch('/api/admin/users/batch-revoke-tokens', {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json'
                                },
                                body: JSON.stringify({ userUuids })
                            });

                            if (response.ok) {
                                const result = await response.json();
                                if (result.success) {
                                    showMessage(result.message);
                                    selectedUsers.value = []; // 清空选择
                                    await loadUsers(); // 刷新用户列表
                                } else {
                                    throw new Error(result.error);
                                }
                            } else {
                                throw new Error('批量取消API Token失败');
                            }
                        }
                    } catch (error) {
                        if (error !== 'cancel') {
                            showMessage(error.message || '批量取消API Token失败', 'error');
                        }
                    } finally {
                        loading.value = false;
                    }
                };

                // 用户搜索和分页处理
                const handleUserSearch = debounce(() => {
                    userPagination.value.currentPage = 1;
                    loadUsers();
                }, 500);

                const resetUserSearch = () => {
                    userSearch.value = '';
                    userPagination.value.currentPage = 1;
                    loadUsers();
                };

                // 筛选处理函数
                const handleUserFilterChange = () => {
                    userPagination.value.currentPage = 1;
                    loadUsers();
                };

                const resetUserFilters = () => {
                    userSearch.value = '';
                    userFilters.value = {
                        tokenStatus: '',
                        trustLevel: '',
                        sortBy: 'created_at',
                        sortOrder: 'desc'
                    };
                    userPagination.value.currentPage = 1;
                    loadUsers();
                };

                const handleUserPageChange = (page) => {
                    if (page && page >= 1 && page <= Math.ceil(userPagination.value.total / userPagination.value.pageSize)) {
                        userPagination.value.currentPage = page;
                        jumpToPage.value = page;
                        loadUsers();
                    }
                };

                const handleUserPageSizeChange = (size) => {
                    userPagination.value.pageSize = size;
                    userPagination.value.currentPage = 1;
                    jumpToPage.value = 1;
                    loadUsers();
                };

                // Token搜索和分页处理
                const handleTokenSearch = debounce(() => {
                    tokenPagination.value.currentPage = 1;
                    loadTokens();
                }, 500);

                const resetTokenSearch = () => {
                    tokenSearch.value = '';
                    tokenStatusFilter.value = '';
                    tokenPagination.value.currentPage = 1;
                    loadTokens();
                };

                const handleTokenPageChange = (page) => {
                    if (page && page >= 1 && page <= Math.ceil(tokenPagination.value.total / tokenPagination.value.pageSize)) {
                        tokenPagination.value.currentPage = page;
                        jumpToTokenPage.value = page;
                        loadTokens();
                    }
                };

                const handleTokenPageSizeChange = (size) => {
                    tokenPagination.value.pageSize = size;
                    tokenPagination.value.currentPage = 1;
                    jumpToTokenPage.value = 1;
                    loadTokens();
                };

                // 防抖函数
                function debounce(func, wait) {
                    let timeout;
                    return function executedFunction(...args) {
                        const later = () => {
                            clearTimeout(timeout);
                            func(...args);
                        };
                        clearTimeout(timeout);
                        timeout = setTimeout(later, wait);
                    };
                }

                // 公告管理方法
                const loadAnnouncements = async () => {
                    try {
                        announcementsLoading.value = true;

                        const response = await authenticatedFetch(`/api/announcements?page=${announcementPagination.value.page}&limit=${announcementPagination.value.limit}`);

                        if (response.ok) {
                            const data = await response.json();
                            announcements.value = data.data || [];
                            announcementPagination.value = data.pagination || {
                                page: 1,
                                limit: 10,
                                total: 0
                            };
                        } else {
                            const errorData = await response.json().catch(() => ({}));
                            ElementPlus.ElMessage.error(errorData.error || '加载公告失败');
                        }
                    } catch (error) {
                        console.error('加载公告错误:', error);
                        if (error.message !== '认证失败') {
                            ElementPlus.ElMessage.error('网络错误');
                        }
                    } finally {
                        announcementsLoading.value = false;
                    }
                };

                const showCreateAnnouncementDialog = () => {
                    announcementDialogMode.value = 'create';
                    resetAnnouncementForm();
                    announcementDialogVisible.value = true;
                };

                const editAnnouncement = (announcement) => {
                    announcementDialogMode.value = 'edit';
                    announcementForm.value = {
                        uuid: announcement.uuid,
                        title: announcement.title,
                        content: announcement.content,
                        type: announcement.type,
                        status: announcement.status,
                        is_pinned: announcement.is_pinned
                    };
                    announcementDialogVisible.value = true;
                };

                const resetAnnouncementForm = () => {
                    announcementForm.value = {
                        uuid: '',
                        title: '',
                        content: '',
                        type: 'info',
                        status: 'active',
                        is_pinned: false
                    };
                };

                const saveAnnouncement = async () => {
                    try {
                        if (!announcementForm.value.title || !announcementForm.value.content) {
                            ElementPlus.ElMessage.error('标题和内容不能为空');
                            return;
                        }

                        announcementSaving.value = true;
                        const isEdit = announcementDialogMode.value === 'edit';
                        const url = isEdit ? `/api/announcements/${announcementForm.value.uuid}` : '/api/announcements';
                        const method = isEdit ? 'PUT' : 'POST';

                        const response = await authenticatedFetch(url, {
                            method,
                            body: JSON.stringify(announcementForm.value)
                        });

                        if (response.ok) {
                            ElementPlus.ElMessage.success(isEdit ? '公告更新成功' : '公告创建成功');
                            announcementDialogVisible.value = false;
                            await loadAnnouncements();
                        } else {
                            const error = await response.json();
                            ElementPlus.ElMessage.error(error.error || '操作失败');
                        }
                    } catch (error) {
                        console.error('保存公告错误:', error);
                        ElementPlus.ElMessage.error('网络错误');
                    } finally {
                        announcementSaving.value = false;
                    }
                };

                const deleteAnnouncement = async (announcement) => {
                    try {
                        await ElementPlus.ElMessageBox.confirm(
                            `确定要删除公告"${announcement.title}"吗？`,
                            '确认删除',
                            {
                                confirmButtonText: '确定',
                                cancelButtonText: '取消',
                                type: 'warning'
                            }
                        );

                        const response = await authenticatedFetch(`/api/announcements/${announcement.uuid}`, {
                            method: 'DELETE'
                        });

                        if (response.ok) {
                            ElementPlus.ElMessage.success('公告删除成功');
                            await loadAnnouncements();
                        } else {
                            ElementPlus.ElMessage.error('删除失败');
                        }
                    } catch (error) {
                        if (error !== 'cancel') {
                            console.error('删除公告错误:', error);
                            ElementPlus.ElMessage.error('网络错误');
                        }
                    }
                };

                const toggleAnnouncementStatus = async (announcement) => {
                    try {
                        const response = await authenticatedFetch(`/api/announcements/${announcement.uuid}/status`, {
                            method: 'PATCH'
                        });

                        if (response.ok) {
                            const data = await response.json();
                            ElementPlus.ElMessage.success(data.message);
                            await loadAnnouncements();
                        } else {
                            ElementPlus.ElMessage.error('操作失败');
                        }
                    } catch (error) {
                        console.error('切换公告状态错误:', error);
                        ElementPlus.ElMessage.error('网络错误');
                    }
                };

                const toggleAnnouncementPin = async (announcement) => {
                    try {
                        const response = await authenticatedFetch(`/api/announcements/${announcement.uuid}/pin`, {
                            method: 'PATCH'
                        });

                        if (response.ok) {
                            const data = await response.json();
                            ElementPlus.ElMessage.success(data.message);
                            await loadAnnouncements();
                        } else {
                            ElementPlus.ElMessage.error('操作失败');
                        }
                    } catch (error) {
                        console.error('切换公告置顶状态错误:', error);
                        ElementPlus.ElMessage.error('网络错误');
                    }
                };

                const handleAnnouncementPageChange = (page) => {
                    announcementPagination.value.page = page;
                    loadAnnouncements();
                };

                const handleAnnouncementPageSizeChange = (size) => {
                    announcementPagination.value.limit = size;
                    announcementPagination.value.page = 1;
                    loadAnnouncements();
                };

                const getAnnouncementTypeColor = (type) => {
                    const colors = {
                        info: '',
                        warning: 'warning',
                        success: 'success',
                        error: 'danger'
                    };
                    return colors[type] || '';
                };

                const getAnnouncementTypeText = (type) => {
                    const texts = {
                        info: '信息',
                        warning: '警告',
                        success: '成功',
                        error: '错误'
                    };
                    return texts[type] || type;
                };

                const formatDateTime = (dateString) => {
                    return new Date(dateString).toLocaleString('zh-CN');
                };

                // （已移除）旧版帮助文档编辑器代码

                // ==================== 多文档管理函数 ====================

                // 加载文档列表
                const loadDocuments = async () => {
                    try {
                        documentsLoading.value = true;

                        const params = new URLSearchParams({
                            page: documentPagination.value.page,
                            limit: documentPagination.value.limit
                        });

                        if (documentSearch.value) {
                            params.append('search', documentSearch.value);
                        }
                        if (documentCategoryFilter.value) {
                            params.append('category', documentCategoryFilter.value);
                        }
                        if (documentStatusFilter.value) {
                            params.append('status', documentStatusFilter.value);
                        }

                        const response = await authenticatedFetch(`/api/admin/documents?${params}`);
                        if (response.ok) {
                            const data = await response.json();
                            documents.value = data.data || [];
                            documentPagination.value = data.pagination || {
                                page: 1,
                                limit: 20,
                                total: 0
                            };
                        } else {
                            ElementPlus.ElMessage.error('加载文档列表失败');
                        }
                    } catch (error) {
                        console.error('加载文档列表错误:', error);
                        ElementPlus.ElMessage.error('网络错误');
                    } finally {
                        documentsLoading.value = false;
                    }
                };

                // 显示创建文档对话框
                const showCreateDocumentDialog = () => {
                    documentDialogMode.value = 'create';
                    documentForm.value = {
                        uuid: '',
                        title: '',
                        slug: '',
                        content: '',
                        category: 'general',
                        status: 'published',
                        sort_order: 0
                    };
                    documentDialogVisible.value = true;
                };

                // 编辑文档
                const editDocument = (document) => {
                    documentDialogMode.value = 'edit';
                    documentForm.value = {
                        uuid: document.uuid,
                        title: document.title,
                        slug: document.slug,
                        content: document.content,
                        category: document.category,
                        status: document.status,
                        sort_order: document.sort_order
                    };
                    documentDialogVisible.value = true;
                };

                // 预览文档
                const previewDocument = (document) => {
                    window.open(`/docs/${document.slug}`, '_blank');
                };

                // 保存文档
                const saveDocument = async () => {
                    if (!documentForm.value.title.trim() || !documentForm.value.content.trim()) {
                        ElementPlus.ElMessage.warning('标题和内容不能为空');
                        return;
                    }

                    documentSaving.value = true;
                    try {
                        const url = documentDialogMode.value === 'create'
                            ? '/api/admin/documents'
                            : `/api/admin/documents/${documentForm.value.uuid}`;

                        const method = documentDialogMode.value === 'create' ? 'POST' : 'PUT';

                        const response = await authenticatedFetch(url, {
                            method: method,
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                title: documentForm.value.title,
                                slug: documentForm.value.slug,
                                content: documentForm.value.content,
                                category: documentForm.value.category,
                                status: documentForm.value.status,
                                sort_order: documentForm.value.sort_order
                            })
                        });

                        if (response.ok) {
                            const data = await response.json();
                            ElementPlus.ElMessage.success(data.message);
                            documentDialogVisible.value = false;
                            await loadDocuments();
                        } else {
                            const errorData = await response.json();
                            ElementPlus.ElMessage.error(errorData.error || '保存失败');
                        }
                    } catch (error) {
                        console.error('保存文档错误:', error);
                        ElementPlus.ElMessage.error('网络错误');
                    } finally {
                        documentSaving.value = false;
                    }
                };

                // 删除文档
                const deleteDocument = async (document) => {
                    try {
                        await ElementPlus.ElMessageBox.confirm(
                            `确定要删除文档"${document.title}"吗？`,
                            '确认删除',
                            {
                                confirmButtonText: '确定',
                                cancelButtonText: '取消',
                                type: 'warning'
                            }
                        );

                        const response = await authenticatedFetch(`/api/admin/documents/${document.uuid}`, {
                            method: 'DELETE'
                        });

                        if (response.ok) {
                            ElementPlus.ElMessage.success('文档删除成功');
                            await loadDocuments();
                        } else {
                            ElementPlus.ElMessage.error('删除失败');
                        }
                    } catch (error) {
                        if (error !== 'cancel') {
                            console.error('删除文档错误:', error);
                            ElementPlus.ElMessage.error('网络错误');
                        }
                    }
                };



                // 搜索文档
                const handleDocumentSearch = debounce(() => {
                    documentPagination.value.page = 1;
                    loadDocuments();
                }, 500);

                // 分类筛选
                const handleDocumentCategoryFilter = () => {
                    documentPagination.value.page = 1;
                    loadDocuments();
                };

                // 状态筛选
                const handleDocumentStatusFilter = () => {
                    documentPagination.value.page = 1;
                    loadDocuments();
                };

                // 重置搜索
                const resetDocumentSearch = () => {
                    documentSearch.value = '';
                    documentCategoryFilter.value = '';
                    documentStatusFilter.value = '';
                    documentPagination.value.page = 1;
                    loadDocuments();
                };

                // 分页处理
                const handleDocumentPageChange = (page) => {
                    documentPagination.value.page = page;
                    loadDocuments();
                };

                const handleDocumentPageSizeChange = (size) => {
                    documentPagination.value.limit = size;
                    documentPagination.value.page = 1;
                    loadDocuments();
                };

                // 重置文档表单
                const resetDocumentForm = () => {
                    documentForm.value = {
                        uuid: '',
                        title: '',
                        slug: '',
                        content: '',
                        category: 'general',
                        status: 'published',
                        sort_order: 0
                    };
                };

                // 获取文档分类颜色
                const getDocumentCategoryColor = (category) => {
                    const colors = {
                        guide: 'success',
                        api: 'primary',
                        faq: 'warning',
                        general: ''
                    };
                    return colors[category] || '';
                };

                // 获取文档分类文本
                const getDocumentCategoryText = (category) => {
                    const texts = {
                        guide: '用户指南',
                        api: 'API文档',
                        faq: '常见问题',
                        general: '其他'
                    };
                    return texts[category] || category;
                };

                // 获取文档状态颜色
                const getDocumentStatusColor = (status) => {
                    const colors = {
                        published: 'success',
                        draft: 'warning',
                        archived: 'info'
                    };
                    return colors[status] || '';
                };

                // 获取文档状态文本
                const getDocumentStatusText = (status) => {
                    const texts = {
                        published: '已发布',
                        draft: '草稿',
                        archived: '已归档'
                    };
                    return texts[status] || status;
                };

                // 修复文档slug
                const fixDocumentSlugs = async () => {
                    try {
                        await ElementPlus.ElMessageBox.confirm(
                            '确定要修复所有文档的URL标识符吗？这将为缺少URL标识符的文档自动生成。',
                            '确认修复',
                            {
                                confirmButtonText: '确定',
                                cancelButtonText: '取消',
                                type: 'warning'
                            }
                        );

                        const response = await authenticatedFetch('/api/admin/fix-document-slugs', {
                            method: 'POST'
                        });

                        if (response.ok) {
                            const data = await response.json();
                            ElementPlus.ElMessage.success(data.message);
                            await loadDocuments();
                        } else {
                            const errorData = await response.json();
                            ElementPlus.ElMessage.error(errorData.error || '修复失败');
                        }
                    } catch (error) {
                        if (error !== 'cancel') {
                            console.error('修复文档slug失败:', error);
                            ElementPlus.ElMessage.error('修复失败');
                        }
                    }
                };

                // 迁移帮助文档
                const migrateHelpDoc = async () => {
                    try {
                        await ElementPlus.ElMessageBox.confirm(
                            '确定要将旧的帮助文档迁移到文档中心吗？这将创建一个新的帮助文档。',
                            '确认迁移',
                            {
                                confirmButtonText: '确定',
                                cancelButtonText: '取消',
                                type: 'warning'
                            }
                        );

                        const response = await authenticatedFetch('/api/admin/migrate-help', {
                            method: 'POST'
                        });

                        if (response.ok) {
                            const data = await response.json();
                            ElementPlus.ElMessage.success(data.message);
                            await loadDocuments();
                        } else {
                            const errorData = await response.json();
                            ElementPlus.ElMessage.error(errorData.error || '迁移失败');
                        }
                    } catch (error) {
                        if (error !== 'cancel') {
                            console.error('迁移帮助文档错误:', error);
                            ElementPlus.ElMessage.error('网络错误');
                        }
                    }
                };

                // 监听标签页切换
                const handleTabChange = async (tabName) => {
                    if (tabName === 'users') {
                        await loadUsers();
                    } else if (tabName === 'tokens') {
                        await loadTokens();
                    } else if (tabName === 'announcements') {
                        await loadAnnouncements();
                    } else if (tabName === 'documents') {
                        await loadDocuments();
                    }
                };

                // 公告通知相关方法
                const showAnnouncementDialog = () => {
                    if (notificationAnnouncements.value.length > 0) {
                        currentNotificationIndex.value = 0;
                        currentNotificationAnnouncement.value = notificationAnnouncements.value[0];
                        notificationDialogVisible.value = true;
                        hasUnreadAnnouncements.value = false;
                    } else {
                        ElementPlus.ElMessage.info('暂无公告');
                    }
                };

                const loadNotificationAnnouncements = async () => {
                    try {
                        const response = await fetch('/api/announcements/active');
                        if (response.ok) {
                            const data = await response.json();
                            notificationAnnouncements.value = data.data || [];
                            hasUnreadAnnouncements.value = notificationAnnouncements.value.length > 0;
                        }
                    } catch (error) {
                        console.error('加载公告失败:', error);
                    }
                };

                // 生命周期
                onMounted(async () => {
                    await checkLoginStatus();
                    await loadNotificationAnnouncements();

                    // 检查管理员权限
                    const hasAccess = await checkAdminAccess();
                    if (hasAccess) {
                        // 加载初始数据
                        await loadStats();
                        await loadUsers();
                        await loadTokens(); // 也加载Token数据
                    }

                    // 监听窗口大小变化
                    window.addEventListener('resize', () => {
                        if (!isMobile.value && mobileMenuOpen.value) {
                            mobileMenuOpen.value = false;
                        }
                    });
                });

                return {
                    isCollapsed,
                    mobileMenuOpen,
                    currentUser,
                    activeMenu,
                    activeTab,
                    loading,
                    error,
                    success,
                    adminInfo,
                    stats,
                    // 通用函数（来自AuthUtils）
                    // getAuthToken, authenticatedFetch, clearAuthToken 已通过 window.AuthUtils 全局可用
                    users,
                    tokens,
                    selectedUsers,
                    // 公告管理
                    announcements,
                    announcementsLoading,
                    announcementPagination,
                    announcementDialogVisible,
                    announcementDialogMode,
                    announcementSaving,
                    announcementForm,
                    userSearch,
                    userFilters,
                    userPagination,
                    jumpToPage,
                    tokenSearch,
                    tokenStatusFilter,
                    tokenPagination,
                    jumpToTokenPage,
                    isMobile,
                    toggleSidebar,
                    toggleMobileSidebar,
                    closeMobileSidebar,
                    navigateTo,
                    refreshPage,
                    handleLogout,
                    getTrustLevelName,
                    getTrustLevelType,
                    checkLoginStatus,
                    showMessage,
                    checkAdminAccess,
                    loadStats,
                    loadUsers,
                    loadTokens,
                    refreshData,
                    deleteUser,
                    deleteToken,
                    handleTabChange,
                    copyToClipboard,
                    handleUserSearch,
                    resetUserSearch,
                    handleUserFilterChange,
                    resetUserFilters,
                    handleUserPageChange,
                    handleUserPageSizeChange,
                    handleTokenSearch,
                    resetTokenSearch,
                    handleTokenPageChange,
                    handleTokenPageSizeChange,
                    handleUserSelectionChange,
                    assignUserToken,
                    revokeUserToken,
                    batchAssignTokens,
                    batchRevokeTokens,
                    // 公告管理方法
                    loadAnnouncements,
                    showCreateAnnouncementDialog,
                    editAnnouncement,
                    resetAnnouncementForm,
                    saveAnnouncement,
                    deleteAnnouncement,
                    toggleAnnouncementStatus,
                    toggleAnnouncementPin,
                    handleAnnouncementPageChange,
                    handleAnnouncementPageSizeChange,
                    getAnnouncementTypeColor,
                    getAnnouncementTypeText,
                    formatDateTime,
                    // 多文档管理
                    documents,
                    documentsLoading,
                    documentPagination,
                    documentSearch,
                    documentCategoryFilter,
                    documentStatusFilter,
                    documentDialogVisible,
                    documentDialogMode,
                    documentSaving,
                    documentForm,
                    loadDocuments,
                    showCreateDocumentDialog,
                    editDocument,
                    previewDocument,
                    saveDocument,
                    deleteDocument,
                    handleDocumentSearch,
                    handleDocumentCategoryFilter,
                    handleDocumentStatusFilter,
                    resetDocumentSearch,
                    handleDocumentPageChange,
                    handleDocumentPageSizeChange,
                    resetDocumentForm,
                    getDocumentCategoryColor,
                    getDocumentCategoryText,
                    getDocumentStatusColor,
                    getDocumentStatusText,
                    fixDocumentSlugs,
                    migrateHelpDoc,
                    // 公告通知相关
                    notificationAnnouncements,
                    notificationDialogVisible,
                    currentNotificationAnnouncement,
                    currentNotificationIndex,
                    hasUnreadAnnouncements,
                    showAnnouncementDialog,
                    loadNotificationAnnouncements,
                    getAnnouncementTypeColor: (type) => {
                        const colors = { info: '', warning: 'warning', success: 'success', error: 'danger' };
                        return colors[type] || '';
                    },
                    getAnnouncementTypeText: (type) => {
                        const texts = { info: '信息', warning: '警告', success: '成功', error: '错误' };
                        return texts[type] || type;
                    },
                    formatDate: (dateString) => new Date(dateString).toLocaleDateString('zh-CN')
                };
            }
        }).use(ElementPlus).mount('#app');
    </script>
</body>
</html>
