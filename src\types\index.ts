// 用户相关类型
export interface User {
  id: string
  email: string
  linuxdo_username?: string
  linuxdo_avatar?: string
  linuxdo_trust_level?: number
  isAdmin?: boolean
  created_at: string
  updated_at: string
}

// 认证相关类型
export interface AuthState {
  user: User | null
  token: string | null
  isAuthenticated: boolean
}

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

// 统计数据类型
export interface SiteStats {
  onlineUsers: number
  users: {
    total: number
    todayNew: number
    yesterdayNew: number
  }
}

// 公告类型
export interface Announcement {
  id: string
  title: string
  content: string
  type: 'info' | 'warning' | 'success' | 'error'
  is_pinned: boolean
  author_name?: string
  created_at: string
  updated_at: string
}

// 赞助记录类型
export interface SponsorRecord {
  id: string
  user_uuid: string
  username: string
  avatar: string
  amount: number
  rank: number
  sponsor_count: number
  created_at: string
}

// 聊天消息类型
export interface ChatMessage {
  id: string
  user_id: string
  username: string
  avatar?: string
  content: string
  type: 'text' | 'image' | 'system'
  created_at: string
}

// 下载文件类型
export interface DownloadFile {
  id: string
  name: string
  description: string
  exists: boolean
  size?: number
  url?: string
}

// 下载权限信息
export interface DownloadInfo {
  checked: boolean
  hasPermission: boolean
  permissionReason?: string
  files: DownloadFile[]
}

// 路由类型
export interface RouteConfig {
  path: string
  element: React.ComponentType
  title: string
  requireAuth?: boolean
  adminOnly?: boolean
}

// API文档相关类型
export interface ApiEndpoint {
  id: string
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'
  path: string
  summary: string
  description: string
  tags: string[]
  parameters?: ApiParameter[]
  requestBody?: ApiRequestBody
  responses: ApiResponse[]
  examples?: ApiExample[]
  deprecated?: boolean
  requiresAuth?: boolean
}

export interface ApiParameter {
  name: string
  in: 'query' | 'path' | 'header' | 'cookie'
  required: boolean
  type: string
  description: string
  example?: any
  enum?: string[]
}

export interface ApiRequestBody {
  contentType: string
  schema: any
  example?: any
  description?: string
}

export interface ApiResponse {
  statusCode: number
  description: string
  schema?: any
  example?: any
}

export interface ApiExample {
  name: string
  description?: string
  request?: any
  response?: any
}

export interface ApiCategory {
  id: string
  name: string
  description: string
  endpoints: ApiEndpoint[]
}

// API文档相关类型
export interface ApiEndpoint {
  id: string
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'
  path: string
  summary: string
  description: string
  tags: string[]
  parameters?: ApiParameter[]
  requestBody?: ApiRequestBody
  responses: ApiResponse[]
  examples?: ApiExample[]
  deprecated?: boolean
  requiresAuth?: boolean
}

export interface ApiParameter {
  name: string
  in: 'query' | 'path' | 'header' | 'cookie'
  required: boolean
  type: string
  description: string
  example?: any
  enum?: string[]
}

export interface ApiRequestBody {
  contentType: string
  schema: any
  example?: any
  description?: string
}

export interface ApiResponse {
  statusCode: number
  description: string
  schema?: any
  example?: any
}

export interface ApiExample {
  name: string
  description?: string
  request?: any
  response?: any
}

export interface ApiCategory {
  id: string
  name: string
  description: string
  endpoints: ApiEndpoint[]
}
