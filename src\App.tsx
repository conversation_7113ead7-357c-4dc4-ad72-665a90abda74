import React, { Suspense } from 'react'
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import { Loader2 } from 'lucide-react'
import AuthProvider from '@/components/AuthProvider'
import ProtectedRoute from '@/components/ProtectedRoute'
import Layout from '@/components/Layout'
import LoginPage from '@/pages/LoginPage'


// 懒加载页面组件
const HomePage = React.lazy(() => import('@/pages/HomePage'))

// 加载中组件
const PageLoading: React.FC = () => (
  <div className="flex items-center justify-center h-64">
    <div className="text-center">
      <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-blue-500" />
      <p className="text-gray-600">加载中...</p>
    </div>
  </div>
)

// 懒加载其他页面组件
const ChatPage = React.lazy(() => import('@/pages/ChatPage'))
const ProfilePage = React.lazy(() => import('@/pages/ProfilePage'))
const AdminPage = React.lazy(() => import('@/pages/AdminPage'))
const ApiDocsPage = React.lazy(() => import('@/pages/ApiDocsPage'))
const ExternalTestPage = React.lazy(() => import('@/pages/ExternalTestPage'))
const DocsPage = React.lazy(() => import('@/pages/DocsPage'))
const DownloadsPage = React.lazy(() => import('@/pages/DownloadsPage'))



function App() {
  return (
    <AuthProvider>
      <Router>
        <Routes>
          <Route path="/login" element={<LoginPage />} />
          <Route
            path="/*"
            element={
              <ProtectedRoute>
                <Layout>
                  <Suspense fallback={<PageLoading />}>
                    <Routes>
                      <Route path="/" element={<HomePage />} />
                      <Route path="/chat" element={<ChatPage />} />
                      <Route path="/api-docs" element={<ApiDocsPage />} />
                      <Route path="/external-test" element={<ExternalTestPage />} />
                      <Route path="/docs" element={<DocsPage />} />
                      <Route path="/downloads" element={<DownloadsPage />} />
                      <Route path="/profile" element={<ProfilePage />} />
                      <Route
                        path="/admin"
                        element={
                          <ProtectedRoute adminOnly>
                            <AdminPage />
                          </ProtectedRoute>
                        }
                      />
                      {/* 更多路由将在后续任务中添加 */}
                    </Routes>
                  </Suspense>
                </Layout>
              </ProtectedRoute>
            }
          />
        </Routes>
      </Router>
    </AuthProvider>
  )
}

export default App
