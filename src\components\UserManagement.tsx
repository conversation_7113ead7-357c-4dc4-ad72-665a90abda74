import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { 
  Users, 
  Search, 
  Filter, 
  MoreHorizontal,
  Edit,
  Trash2,
  Shield,
  ShieldOff,
  Eye,
  Ban,
  CheckCircle,
  XCircle,
  Calendar
} from 'lucide-react'
import dayjs from 'dayjs'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { getTrustLevelName } from '@/lib/auth'
import type { User } from '@/types'

interface UserWithStats extends User {
  messagesCount: number
  apiCallsCount: number
  lastActiveAt: string
  status: 'active' | 'banned' | 'inactive'
}

const UserManagement: React.FC = () => {
  const [users, setUsers] = useState<UserWithStats[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [trustLevelFilter, setTrustLevelFilter] = useState<string>('all')
  const [selectedUser, setSelectedUser] = useState<UserWithStats | null>(null)
  const [actionType, setActionType] = useState<'ban' | 'unban' | 'delete' | 'promote' | 'demote' | null>(null)
  const [showConfirmDialog, setShowConfirmDialog] = useState(false)

  // 模拟用户数据
  const mockUsers: UserWithStats[] = [
    {
      id: '1',
      email: '<EMAIL>',
      linuxdo_username: 'admin',
      linuxdo_avatar: '',
      linuxdo_trust_level: 4,
      isAdmin: true,
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-15T00:00:00Z',
      messagesCount: 156,
      apiCallsCount: 2340,
      lastActiveAt: new Date().toISOString(),
      status: 'active'
    },
    {
      id: '2',
      email: '<EMAIL>',
      linuxdo_username: 'user123',
      linuxdo_avatar: '',
      linuxdo_trust_level: 2,
      isAdmin: false,
      created_at: '2024-01-05T00:00:00Z',
      updated_at: '2024-01-14T00:00:00Z',
      messagesCount: 45,
      apiCallsCount: 890,
      lastActiveAt: new Date(Date.now() - 3600000).toISOString(),
      status: 'active'
    },
    {
      id: '3',
      email: '<EMAIL>',
      linuxdo_username: 'developer',
      linuxdo_avatar: '',
      linuxdo_trust_level: 3,
      isAdmin: false,
      created_at: '2024-01-10T00:00:00Z',
      updated_at: '2024-01-12T00:00:00Z',
      messagesCount: 23,
      apiCallsCount: 456,
      lastActiveAt: new Date(Date.now() - 86400000).toISOString(),
      status: 'banned'
    }
  ]

  // 加载用户数据
  useEffect(() => {
    const loadUsers = async () => {
      setLoading(true)
      try {
        // TODO: 调用API获取用户列表
        await new Promise(resolve => setTimeout(resolve, 1000))
        setUsers(mockUsers)
      } catch (error) {
        console.error('加载用户列表失败:', error)
      } finally {
        setLoading(false)
      }
    }

    loadUsers()
  }, [])

  // 过滤用户
  const filteredUsers = users.filter(user => {
    const matchesSearch = 
      user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.linuxdo_username?.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesStatus = statusFilter === 'all' || user.status === statusFilter
    
    const matchesTrustLevel = 
      trustLevelFilter === 'all' || 
      user.linuxdo_trust_level?.toString() === trustLevelFilter

    return matchesSearch && matchesStatus && matchesTrustLevel
  })

  // 获取状态标签
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-100 text-green-700">活跃</Badge>
      case 'banned':
        return <Badge className="bg-red-100 text-red-700">已封禁</Badge>
      case 'inactive':
        return <Badge className="bg-gray-100 text-gray-700">不活跃</Badge>
      default:
        return <Badge variant="secondary">未知</Badge>
    }
  }

  // 处理用户操作
  const handleUserAction = async () => {
    if (!selectedUser || !actionType) return

    try {
      // TODO: 调用相应的API
      switch (actionType) {
        case 'ban':
          setUsers(prev => prev.map(u => 
            u.id === selectedUser.id ? { ...u, status: 'banned' as const } : u
          ))
          break
        case 'unban':
          setUsers(prev => prev.map(u => 
            u.id === selectedUser.id ? { ...u, status: 'active' as const } : u
          ))
          break
        case 'delete':
          setUsers(prev => prev.filter(u => u.id !== selectedUser.id))
          break
        case 'promote':
          setUsers(prev => prev.map(u => 
            u.id === selectedUser.id ? { ...u, isAdmin: true } : u
          ))
          break
        case 'demote':
          setUsers(prev => prev.map(u => 
            u.id === selectedUser.id ? { ...u, isAdmin: false } : u
          ))
          break
      }
      
      alert('操作成功！')
    } catch (error) {
      console.error('操作失败:', error)
      alert('操作失败，请重试')
    } finally {
      setShowConfirmDialog(false)
      setSelectedUser(null)
      setActionType(null)
    }
  }

  // 确认操作
  const confirmAction = (user: UserWithStats, action: typeof actionType) => {
    setSelectedUser(user)
    setActionType(action)
    setShowConfirmDialog(true)
  }

  // 获取操作确认文本
  const getConfirmText = () => {
    if (!selectedUser || !actionType) return ''
    
    const actions = {
      ban: '封禁',
      unban: '解封',
      delete: '删除',
      promote: '提升为管理员',
      demote: '取消管理员权限'
    }
    
    return `确定要${actions[actionType]}用户 "${selectedUser.linuxdo_username || selectedUser.email}" 吗？`
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Users className="w-5 h-5" />
          <span>用户管理</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* 搜索和过滤 */}
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="搜索用户名或邮箱..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
          
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-32">
              <SelectValue placeholder="状态" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部状态</SelectItem>
              <SelectItem value="active">活跃</SelectItem>
              <SelectItem value="banned">已封禁</SelectItem>
              <SelectItem value="inactive">不活跃</SelectItem>
            </SelectContent>
          </Select>

          <Select value={trustLevelFilter} onValueChange={setTrustLevelFilter}>
            <SelectTrigger className="w-32">
              <SelectValue placeholder="信任等级" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部等级</SelectItem>
              <SelectItem value="0">新用户</SelectItem>
              <SelectItem value="1">基础</SelectItem>
              <SelectItem value="2">成员</SelectItem>
              <SelectItem value="3">常规</SelectItem>
              <SelectItem value="4">领导</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* 用户表格 */}
        <div className="border rounded-lg">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>用户</TableHead>
                <TableHead>状态</TableHead>
                <TableHead>信任等级</TableHead>
                <TableHead>消息数</TableHead>
                <TableHead>API调用</TableHead>
                <TableHead>最后活跃</TableHead>
                <TableHead>注册时间</TableHead>
                <TableHead className="text-right">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                Array.from({ length: 5 }).map((_, i) => (
                  <TableRow key={i}>
                    <TableCell colSpan={8}>
                      <div className="flex items-center space-x-3 animate-pulse">
                        <div className="w-8 h-8 bg-gray-200 rounded-full" />
                        <div className="space-y-2">
                          <div className="h-4 bg-gray-200 rounded w-24" />
                          <div className="h-3 bg-gray-200 rounded w-32" />
                        </div>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              ) : filteredUsers.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={8} className="text-center py-8">
                    <Users className="w-12 h-12 text-gray-300 mx-auto mb-3" />
                    <p className="text-gray-500">没有找到匹配的用户</p>
                  </TableCell>
                </TableRow>
              ) : (
                filteredUsers.map((user) => (
                  <TableRow key={user.id}>
                    <TableCell>
                      <div className="flex items-center space-x-3">
                        <Avatar className="w-8 h-8">
                          <AvatarImage src={user.linuxdo_avatar} />
                          <AvatarFallback>
                            {user.linuxdo_username?.[0]?.toUpperCase() || user.email[0].toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="flex items-center space-x-2">
                            <span className="font-medium">
                              {user.linuxdo_username || user.email}
                            </span>
                            {user.isAdmin && (
                              <Shield className="w-4 h-4 text-red-500" />
                            )}
                          </div>
                          <p className="text-sm text-gray-500">{user.email}</p>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>{getStatusBadge(user.status)}</TableCell>
                    <TableCell>
                      <Badge variant="secondary">
                        {user.linuxdo_trust_level !== undefined 
                          ? getTrustLevelName(user.linuxdo_trust_level)
                          : '未知'
                        }
                      </Badge>
                    </TableCell>
                    <TableCell>{user.messagesCount}</TableCell>
                    <TableCell>{user.apiCallsCount.toLocaleString()}</TableCell>
                    <TableCell>
                      <span className="text-sm text-gray-500">
                        {dayjs(user.lastActiveAt).fromNow()}
                      </span>
                    </TableCell>
                    <TableCell>
                      <span className="text-sm text-gray-500">
                        {dayjs(user.created_at).format('YYYY-MM-DD')}
                      </span>
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="w-4 h-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>用户操作</DropdownMenuLabel>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem>
                            <Eye className="w-4 h-4 mr-2" />
                            查看详情
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Edit className="w-4 h-4 mr-2" />
                            编辑用户
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          {user.status === 'active' ? (
                            <DropdownMenuItem 
                              onClick={() => confirmAction(user, 'ban')}
                              className="text-red-600"
                            >
                              <Ban className="w-4 h-4 mr-2" />
                              封禁用户
                            </DropdownMenuItem>
                          ) : (
                            <DropdownMenuItem 
                              onClick={() => confirmAction(user, 'unban')}
                              className="text-green-600"
                            >
                              <CheckCircle className="w-4 h-4 mr-2" />
                              解封用户
                            </DropdownMenuItem>
                          )}
                          {user.isAdmin ? (
                            <DropdownMenuItem 
                              onClick={() => confirmAction(user, 'demote')}
                              className="text-orange-600"
                            >
                              <ShieldOff className="w-4 h-4 mr-2" />
                              取消管理员
                            </DropdownMenuItem>
                          ) : (
                            <DropdownMenuItem 
                              onClick={() => confirmAction(user, 'promote')}
                              className="text-blue-600"
                            >
                              <Shield className="w-4 h-4 mr-2" />
                              设为管理员
                            </DropdownMenuItem>
                          )}
                          <DropdownMenuSeparator />
                          <DropdownMenuItem 
                            onClick={() => confirmAction(user, 'delete')}
                            className="text-red-600"
                          >
                            <Trash2 className="w-4 h-4 mr-2" />
                            删除用户
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>

        {/* 分页信息 */}
        <div className="flex items-center justify-between text-sm text-gray-500">
          <span>共 {filteredUsers.length} 个用户</span>
          <span>显示 1-{Math.min(10, filteredUsers.length)} 条</span>
        </div>
      </CardContent>

      {/* 确认对话框 */}
      <AlertDialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认操作</AlertDialogTitle>
            <AlertDialogDescription>
              {getConfirmText()}
              {actionType === 'delete' && (
                <span className="block mt-2 text-red-600 font-medium">
                  此操作不可恢复！
                </span>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleUserAction}
              className={actionType === 'delete' ? 'bg-red-600 hover:bg-red-700' : ''}
            >
              确认
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Card>
  )
}

export default UserManagement
