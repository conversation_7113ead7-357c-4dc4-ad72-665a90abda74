<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AugmentAPI - 文档中心</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>🧸</text></svg>">
    
    <!-- 引入Element Plus CSS - 本地资源 -->
    <link rel="stylesheet" href="/lib/element-plus/index.min.css">

    <!-- 引入Vue 3 - 本地资源 -->
    <script src="/lib/vue/vue.global.min.js"></script>
    <!-- 引入Element Plus - 本地资源 -->
    <script src="/lib/element-plus/index.full.min.js"></script>
    <!-- 引入Element Plus图标 - 本地资源（与其它页面一致） -->
    <script src="/lib/element-plus/icons.iife.min.js"></script>

    <!-- 静默控制台（生产环境隐藏console输出；加 ?debug=1 显示） -->
    <script src="/js/quiet-console.js"></script>

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: #f5f7fa;
        }
        
        .layout-container {
            height: 100vh;
            display: flex;
        }
        
        .sidebar {
            background: #fff;
            box-shadow: 2px 0 6px rgba(0,21,41,.35);
            transition: width 0.3s;
            overflow: hidden;
            position: relative;
        }
        
        .sidebar.collapsed {
            width: 64px !important;
        }

        .sidebar.collapsed .user-info {
            justify-content: center;
            padding: 0;
        }

        .sidebar.collapsed .user-details {
            display: none;
        }

        .sidebar.collapsed .user-avatar {
            margin-right: 0;
            flex-shrink: 0;
        }

        .sidebar.collapsed .el-menu-item span,
        .sidebar.collapsed .el-sub-menu .el-sub-menu__title span {
            display: none;
        }

        .sidebar.collapsed .el-sub-menu .el-sub-menu__icon-arrow {
            display: none;
        }

        .sidebar.expanded {
            width: 250px !important;
        }
        
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .content-header {
            background: #fff;
            padding: 16px 24px;
            border-bottom: 1px solid #e8eaec;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .content-body {
            flex: 1;
            padding: 24px;
            overflow-y: auto;
            background: #f5f7fa;
        }
        
        .page-card {
            background: #fff;
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .user-info {
            display: flex;
            align-items: center;
            padding: 16px;
            border-bottom: 1px solid #e8eaec;
        }
        
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin-right: 12px;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #409eff;
            color: white;
            font-weight: bold;
        }

        .user-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        /* 当有头像图片时，移除背景色 */
        .user-avatar:has(img) {
            background: transparent;
        }

        /* 公告铃铛按钮样式 */
        .bell-button {
            position: relative;
            padding: 8px 12px !important;
            border-radius: 50% !important;
            font-size: 18px !important;
            transition: all 0.3s ease !important;
            border: none !important;
        }

        .bell-button:hover {
            background-color: rgba(64, 158, 255, 0.1) !important;
            transform: scale(1.1);
        }

        .has-unread {
            position: relative;
            animation: bell-shake 2s infinite;
            color: #409eff !important;
        }

        .has-unread::after {
            content: '';
            position: absolute;
            top: 2px;
            right: 2px;
            width: 8px;
            height: 8px;
            background: #f56c6c;
            border-radius: 50%;
            border: 2px solid #fff;
            z-index: 1;
        }

        @keyframes bell-shake {
            0%, 50%, 100% { transform: rotate(0deg); }
            10%, 30% { transform: rotate(-10deg); }
            20%, 40% { transform: rotate(10deg); }
        }
        
        .user-details {
            flex: 1;
            min-width: 0;
        }
        
        .user-name {
            font-weight: 600;
            color: #303133;
            font-size: 14px;
            margin-bottom: 4px;
            display: flex;
            align-items: center;
        }
        
        .user-email {
            color: #909399;
            font-size: 12px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .trust-level {
            margin-left: 8px;
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 10px;
            font-weight: 600;
        }
        
        .trust-level-0 { background: #f4f4f5; color: #909399; }
        .trust-level-1 { background: #ecf5ff; color: #409eff; }
        .trust-level-2 { background: #f0f9ff; color: #67c23a; }
        .trust-level-3 { background: #fdf6ec; color: #e6a23c; }
        .trust-level-4 { background: #fef0f0; color: #f56c6c; }
        
        .sidebar-toggle {
            position: absolute;
            top: 16px;
            right: -12px;
            width: 24px;
            height: 24px;
            background: #409eff;
            border: none;
            border-radius: 50%;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            z-index: 1000;
        }
        
        .sidebar-toggle:hover {
            background: #337ecc;
        }
        
        /* 文档中心特定样式 */
        .docs-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .doc-card {
            background: #fff;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .doc-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0,0,0,0.15);
        }

        .doc-title {
            font-size: 18px;
            font-weight: 600;
            color: #303133;
            margin-bottom: 8px;
        }

        .doc-description {
            color: #606266;
            font-size: 14px;
            line-height: 1.5;
            margin-bottom: 12px;
        }

        .doc-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
            color: #909399;
        }

        .category-tag {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
        }

        .category-help { background: #e1f3d8; color: #67c23a; }
        .category-api { background: #ecf5ff; color: #409eff; }
        .category-guide { background: #fdf6ec; color: #e6a23c; }

        /* 移动端适配 */
        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                left: 0;
                top: 0;
                height: 100vh;
                z-index: 1000;
                transform: translateX(-100%);
                transition: transform 0.3s;
            }
            
            .sidebar.mobile-open {
                transform: translateX(0);
            }
            
            .main-content {
                width: 100%;
            }
            
            .content-body {
                padding: 16px;
            }
            
            .docs-grid {
                grid-template-columns: 1fr;
                gap: 16px;
            }
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="layout-container">
            <!-- 侧边栏 -->
            <div class="sidebar" :class="{ 
                collapsed: isCollapsed, 
                expanded: !isCollapsed,
                'mobile-open': mobileMenuOpen 
            }">
                <button class="sidebar-toggle" @click="toggleSidebar">
                    {{ isCollapsed ? '→' : '←' }}
                </button>
                
                <!-- 用户信息 -->
                <div class="user-info" v-if="currentUser">
                    <div class="user-avatar">
                        <img v-if="currentUser.linuxdo_avatar" :src="currentUser.linuxdo_avatar" :alt="currentUser.linuxdo_username">
                        <span v-else>{{ currentUser.email ? currentUser.email.charAt(0).toUpperCase() : '?' }}</span>
                    </div>
                    <div class="user-details" v-show="!isCollapsed">
                        <div class="user-name">
                            {{ currentUser.linuxdo_username || currentUser.email.split('@')[0] }}
                            <span v-if="currentUser.linuxdo_trust_level !== null" 
                                  :class="`trust-level trust-level-${currentUser.linuxdo_trust_level}`">
                                {{ getTrustLevelName(currentUser.linuxdo_trust_level) }}
                            </span>
                        </div>
                        <div class="user-email">{{ currentUser.email }}</div>
                    </div>
                </div>
                
                <!-- 导航菜单 -->
                <el-menu 
                    :default-active="activeMenu"
                    :collapse="isCollapsed"
                    :unique-opened="true"
                    background-color="#fff"
                    text-color="#303133"
                    active-text-color="#409eff">
                    
                    <el-menu-item index="home" @click="navigateTo('/')">
                        <el-icon><i class="el-icon-house"></i></el-icon>
                        <span>首页</span>
                    </el-menu-item>
                    
                    <el-menu-item index="profile" @click="navigateTo('/profile')">
                        <el-icon><i class="el-icon-user"></i></el-icon>
                        <span>个人中心</span>
                    </el-menu-item>

                    <el-menu-item index="chat" @click="navigateTo('/chat')">
                        <el-icon><i class="el-icon-chat-dot-round"></i></el-icon>
                        <span>实时聊天</span>
                    </el-menu-item>

                    <el-sub-menu index="api">
                        <template #title>
                            <el-icon><i class="el-icon-document"></i></el-icon>
                            <span>API管理</span>
                        </template>
                        <el-menu-item index="api-docs" @click="navigateTo('/api-docs')">
                            <el-icon><i class="el-icon-reading"></i></el-icon>
                            <span>API文档</span>
                        </el-menu-item>
                        <el-menu-item index="external-test" @click="navigateTo('/external-test')">
                            <el-icon><i class="el-icon-connection"></i></el-icon>
                            <span>外部接口测试</span>
                        </el-menu-item>
                    </el-sub-menu>

                    <el-menu-item index="docs" @click="navigateTo('/docs')">
                        <el-icon><i class="el-icon-folder"></i></el-icon>
                        <span>文档中心</span>
                    </el-menu-item>

                    <!-- 管理后台菜单项（仅管理员可见） -->
                    <el-menu-item v-if="currentUser && currentUser.isAdmin" index="admin" @click="navigateTo('/admin')">
                        <el-icon><i class="el-icon-setting"></i></el-icon>
                        <span>管理后台</span>
                    </el-menu-item>

                    <el-menu-item index="logout" @click="handleLogout">
                        <el-icon><i class="el-icon-switch-button"></i></el-icon>
                        <span>退出登录</span>
                    </el-menu-item>
                </el-menu>
            </div>
            
            <!-- 主内容区 -->
            <div class="main-content">
                <!-- 内容头部 -->
                <div class="content-header">
                    <div>
                        <el-button v-if="isMobile" @click="toggleMobileSidebar" icon="el-icon-menu" circle></el-button>
                        <span style="font-size: 18px; font-weight: 600; margin-left: 8px;">文档中心</span>
                    </div>
                    <div>
                        <el-button @click="showAnnouncementDialog" :class="{ 'has-unread': hasUnreadAnnouncements, 'bell-button': true }" text>
                            🔔
                        </el-button>
                    </div>
                </div>
                
                <!-- 内容主体 -->
                <div class="content-body">
                    <div class="page-card">
                        <h1>📚 文档中心</h1>
                        <p style="color: #606266; margin-bottom: 24px;">
                            欢迎来到 AugmentAPI 文档中心，这里有详细的使用指南和API文档。
                        </p>

                        <!-- 搜索栏 -->
                        <div style="margin-bottom: 20px;">
                            <el-input
                                v-model="searchQuery"
                                placeholder="搜索文档..."
                                prefix-icon="el-icon-search"
                                size="large"
                                style="max-width: 400px;">
                            </el-input>
                        </div>

                        <!-- 分类筛选 -->
                        <div style="margin-bottom: 20px;">
                            <el-radio-group v-model="selectedCategory" size="small">
                                <el-radio-button label="">全部</el-radio-button>
                                <el-radio-button label="help">帮助文档</el-radio-button>
                                <el-radio-button label="api">API文档</el-radio-button>
                                <el-radio-button label="guide">使用指南</el-radio-button>
                            </el-radio-group>
                        </div>

                        <!-- 文档列表 -->
                        <div v-loading="loading">
                            <div v-if="filteredDocuments.length === 0" style="text-align: center; padding: 40px; color: #909399;">
                                <el-icon size="48"><i class="el-icon-document"></i></el-icon>
                                <p style="margin-top: 16px;">暂无文档</p>
                            </div>
                            
                            <div v-else class="docs-grid">
                                <div 
                                    v-for="doc in filteredDocuments" 
                                    :key="doc.id"
                                    class="doc-card"
                                    @click="openDocument(doc)">
                                    <div class="doc-title">{{ doc.title }}</div>
                                    <div class="doc-description">{{ doc.description || '暂无描述' }}</div>
                                    <div class="doc-meta">
                                        <span class="category-tag" :class="`category-${doc.category}`">
                                            {{ getCategoryTitle(doc.category) }}
                                        </span>
                                        <span>{{ formatDate(doc.updated_at) }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 系统公告弹窗 -->
        <el-dialog
            v-model="announcementDialogVisible"
            width="500px"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
            :show-close="true"
            class="announcement-dialog">

            <template #header>
                <span style="font-size: 18px; font-weight: 600;">系统公告</span>
            </template>

            <div v-if="currentAnnouncement" class="announcement-dialog-content">
                <div class="announcement-header">
                    <div class="announcement-title">
                        <el-tag v-if="currentAnnouncement.is_pinned" type="warning" size="small" style="margin-right: 8px;">置顶</el-tag>
                        <el-tag :type="getAnnouncementTypeColor(currentAnnouncement.type)" size="small" style="margin-right: 8px;">
                            {{ getAnnouncementTypeText(currentAnnouncement.type) }}
                        </el-tag>
                        <span style="font-size: 16px; font-weight: 600;">{{ currentAnnouncement.title }}</span>
                    </div>
                    <div class="announcement-meta" style="margin-top: 8px; color: #909399; font-size: 12px;">
                        发布时间：{{ formatDate(currentAnnouncement.created_at) }}
                        <span v-if="currentAnnouncement.author_name" style="margin-left: 16px;">
                            发布人：{{ currentAnnouncement.author_name }}
                        </span>
                    </div>
                    <el-divider />
                </div>
                <div class="announcement-content" style="line-height: 1.8; white-space: pre-wrap;">
                    {{ currentAnnouncement.content }}
                </div>
            </div>

            <template #footer>
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <div style="color: #909399; font-size: 12px;">
                        {{ currentAnnouncementIndex + 1 }} / {{ announcements.length }}
                    </div>
                    <div>
                        <el-button type="primary" @click="announcementDialogVisible = false">
                            知道了
                        </el-button>
                    </div>
                </div>
            </template>
        </el-dialog>

        <!-- 移动端遮罩 -->
        <div v-if="isMobile && mobileMenuOpen"
             style="position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.5); z-index: 999;"
             @click="closeMobileSidebar"></div>
    </div>
    <script>
        const { createApp, ref, reactive, onMounted, computed } = Vue;
        const { ElMessage } = ElementPlus;

        createApp({
            setup() {
                // 响应式数据
                const isCollapsed = ref(false);
                const mobileMenuOpen = ref(false);
                const currentUser = ref(null);
                const activeMenu = ref('docs');
                const documents = ref([]);
                const searchQuery = ref('');
                const selectedCategory = ref('');
                const loading = ref(false);

                // 公告数据
                const announcements = ref([]);
                const announcementDialogVisible = ref(false);
                const currentAnnouncement = ref(null);
                const currentAnnouncementIndex = ref(0);
                const hasUnreadAnnouncements = ref(false);

                // 计算属性
                const isMobile = computed(() => {
                    return window.innerWidth <= 768;
                });

                const filteredDocuments = computed(() => {
                    let filtered = documents.value;

                    // 按分类筛选
                    if (selectedCategory.value) {
                        filtered = filtered.filter(doc => doc.category === selectedCategory.value);
                    }

                    // 按搜索关键词筛选
                    if (searchQuery.value) {
                        const query = searchQuery.value.toLowerCase();
                        filtered = filtered.filter(doc =>
                            doc.title.toLowerCase().includes(query) ||
                            (doc.description && doc.description.toLowerCase().includes(query))
                        );
                    }

                    return filtered;
                });

                // 方法
                const toggleSidebar = () => {
                    isCollapsed.value = !isCollapsed.value;
                };

                const toggleMobileSidebar = () => {
                    mobileMenuOpen.value = !mobileMenuOpen.value;
                };

                const closeMobileSidebar = () => {
                    mobileMenuOpen.value = false;
                };

                const navigateTo = (path) => {
                    window.location.href = path;
                };

                const refreshPage = () => {
                    window.location.reload();
                };

                const handleLogout = async () => {
                    try {
                        const response = await fetch('/api/logout', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            }
                        });

                        if (response.ok) {
                            localStorage.removeItem('auth_token');
                            window.location.href = '/login';
                        } else {
                            ElMessage.error('登出失败，请重试');
                        }
                    } catch (error) {
                        console.error('登出错误:', error);
                        ElMessage.error('网络错误，请重试');
                    }
                };

                const getTrustLevelName = (level) => {
                    const levelNames = {
                        0: '新用户',
                        1: '基础',
                        2: '成员',
                        3: '常规',
                        4: '领导'
                    };
                    return levelNames[level] || '未知';
                };

                const checkLoginStatus = async () => {
                    try {
                        const response = await fetch('/api/user');
                        if (response.ok) {
                            const result = await response.json();
                            currentUser.value = result.user;
                            return true;
                        } else if (response.status === 401) {
                            window.location.href = '/login';
                            return false;
                        }
                    } catch (error) {
                        console.error('检查登录状态失败:', error);
                        return false;
                    }
                };

                const loadDocuments = async () => {
                    try {
                        loading.value = true;
                        const response = await fetch('/api/documents/published');

                        if (response.ok) {
                            const data = await response.json();
                            documents.value = data.data || [];
                        } else {
                            ElMessage.error('加载文档失败');
                        }
                    } catch (error) {
                        console.error('加载文档失败:', error);
                        ElMessage.error('网络错误，无法加载文档');
                    } finally {
                        loading.value = false;
                    }
                };

                const openDocument = (doc) => {
                    if (!doc.slug) {
                        ElMessage.error('文档URL标识符缺失，无法访问');
                        return;
                    }
                    window.location.href = `/docs/${doc.slug}`;
                };

                const getCategoryTitle = (category) => {
                    const titles = {
                        'help': '帮助文档',
                        'api': 'API文档',
                        'guide': '使用指南'
                    };
                    return titles[category] || '其他';
                };

                const formatDate = (dateString) => {
                    if (!dateString) return '';
                    const date = new Date(dateString);
                    return date.toLocaleDateString('zh-CN');
                };

                // 公告相关方法
                const showAnnouncementDialog = () => {
                    if (announcements.value.length > 0) {
                        currentAnnouncementIndex.value = 0;
                        currentAnnouncement.value = announcements.value[0];
                        announcementDialogVisible.value = true;
                        hasUnreadAnnouncements.value = false;
                    } else {
                        ElMessage.info('暂无公告');
                    }
                };

                const loadAnnouncements = async () => {
                    try {
                        const response = await fetch('/api/announcements/active');
                        if (response.ok) {
                            const data = await response.json();
                            announcements.value = data.data || [];
                            hasUnreadAnnouncements.value = announcements.value.length > 0;
                        }
                    } catch (error) {
                        console.error('加载公告失败:', error);
                    }
                };

                // 生命周期
                onMounted(async () => {
                    await checkLoginStatus();
                    await loadAnnouncements();
                    loadDocuments();

                    // 监听窗口大小变化
                    window.addEventListener('resize', () => {
                        if (!isMobile.value && mobileMenuOpen.value) {
                            mobileMenuOpen.value = false;
                        }
                    });
                });

                return {
                    isCollapsed,
                    mobileMenuOpen,
                    currentUser,
                    activeMenu,
                    documents,
                    searchQuery,
                    selectedCategory,
                    loading,
                    isMobile,
                    filteredDocuments,
                    toggleSidebar,
                    toggleMobileSidebar,
                    closeMobileSidebar,
                    navigateTo,
                    refreshPage,
                    handleLogout,
                    getTrustLevelName,
                    loadDocuments,
                    openDocument,
                    getCategoryTitle,
                    formatDate,
                    // 公告相关
                    announcements,
                    announcementDialogVisible,
                    currentAnnouncement,
                    currentAnnouncementIndex,
                    hasUnreadAnnouncements,
                    showAnnouncementDialog,
                    loadAnnouncements,
                    getAnnouncementTypeColor: (type) => {
                        const colors = { info: '', warning: 'warning', success: 'success', error: 'danger' };
                        return colors[type] || '';
                    },
                    getAnnouncementTypeText: (type) => {
                        const texts = { info: '信息', warning: '警告', success: '成功', error: '错误' };
                        return texts[type] || type;
                    }
                };
            }
        }).use(ElementPlus).mount('#app');
    </script>
</body>
</html>
