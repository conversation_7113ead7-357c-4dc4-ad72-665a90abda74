import React from 'react'
import { motion } from 'framer-motion'
import { Pin, Info, AlertTriangle, CheckCircle, XCircle } from 'lucide-react'
import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import 'dayjs/locale/zh-cn'
import { Card, CardContent, CardHeader } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { cn } from '@/lib/utils'
import type { Announcement } from '@/types'

dayjs.extend(relativeTime)
dayjs.locale('zh-cn')

interface AnnouncementCardProps {
  announcement: Announcement
  onMarkAsRead?: (id: string) => void
}

const typeConfig = {
  info: {
    icon: Info,
    color: 'text-blue-600',
    bg: 'bg-blue-50',
    border: 'border-blue-200',
    badge: 'bg-blue-100 text-blue-800'
  },
  warning: {
    icon: AlertTriangle,
    color: 'text-orange-600',
    bg: 'bg-orange-50',
    border: 'border-orange-200',
    badge: 'bg-orange-100 text-orange-800'
  },
  success: {
    icon: CheckCircle,
    color: 'text-green-600',
    bg: 'bg-green-50',
    border: 'border-green-200',
    badge: 'bg-green-100 text-green-800'
  },
  error: {
    icon: XCircle,
    color: 'text-red-600',
    bg: 'bg-red-50',
    border: 'border-red-200',
    badge: 'bg-red-100 text-red-800'
  }
}

const typeLabels = {
  info: '通知',
  warning: '警告',
  success: '成功',
  error: '错误'
}

const AnnouncementCard: React.FC<AnnouncementCardProps> = ({
  announcement,
  onMarkAsRead
}) => {
  const config = typeConfig[announcement.type]
  const Icon = config.icon

  const handleClick = () => {
    if (onMarkAsRead) {
      onMarkAsRead(announcement.id)
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      whileHover={{ scale: 1.01 }}
      transition={{ type: "spring", stiffness: 300 }}
    >
      <Card 
        className={cn(
          "cursor-pointer hover:shadow-md transition-all duration-200 border-l-4",
          config.border
        )}
        onClick={handleClick}
      >
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div className="flex items-center space-x-3">
              <div className={cn("p-2 rounded-lg", config.bg)}>
                <Icon className={cn("w-5 h-5", config.color)} />
              </div>
              <div className="flex-1">
                <div className="flex items-center space-x-2 mb-1">
                  <h3 className="font-semibold text-gray-900 text-sm">
                    {announcement.title}
                  </h3>
                  {announcement.is_pinned && (
                    <Pin className="w-4 h-4 text-orange-500" />
                  )}
                </div>
                <div className="flex items-center space-x-2">
                  <Badge 
                    variant="secondary" 
                    className={cn("text-xs", config.badge)}
                  >
                    {typeLabels[announcement.type]}
                  </Badge>
                  <span className="text-xs text-gray-500">
                    {dayjs(announcement.created_at).fromNow()}
                  </span>
                  {announcement.author_name && (
                    <span className="text-xs text-gray-500">
                      · {announcement.author_name}
                    </span>
                  )}
                </div>
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent className="pt-0">
          <div 
            className="text-sm text-gray-700 leading-relaxed"
            dangerouslySetInnerHTML={{ __html: announcement.content }}
          />
        </CardContent>
      </Card>
    </motion.div>
  )
}

export default AnnouncementCard
