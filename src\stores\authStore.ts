import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import type { User, AuthState } from '@/types'
import { authTokens, getCurrentUser } from '@/lib/auth'

interface AuthStore extends AuthState {
  // Actions
  setUser: (user: User | null) => void
  setToken: (token: string | null) => void
  login: (token: string, user: User) => void
  logout: () => void
  checkAuth: () => Promise<boolean>
  refreshUser: () => Promise<void>
}

export const useAuthStore = create<AuthStore>()(
  devtools(
    (set, get) => ({
      // Initial state - 临时提供模拟用户数据方便查看页面
      user: {
        id: '1',
        email: '<EMAIL>',
        linuxdo_username: 'demo_user',
        linuxdo_avatar: '',
        linuxdo_trust_level: 2,
        isAdmin: true,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-15T00:00:00Z'
      },
      token: 'demo_token',
      isAuthenticated: true,

      // Actions
      setUser: (user) => {
        set({ user, isAuthenticated: !!user }, false, 'setUser')
      },

      setToken: (token) => {
        set({ token }, false, 'setToken')
        if (token) {
          authTokens.set(token)
        } else {
          authTokens.remove()
        }
      },

      login: (token, user) => {
        authTokens.set(token)
        set({ 
          token, 
          user, 
          isAuthenticated: true 
        }, false, 'login')
      },

      logout: () => {
        authTokens.remove()
        set({ 
          token: null, 
          user: null, 
          isAuthenticated: false 
        }, false, 'logout')
      },

      checkAuth: async () => {
        const token = authTokens.get()
        if (!token) {
          set({ isAuthenticated: false, user: null, token: null })
          return false
        }

        try {
          const user = await getCurrentUser()
          if (user) {
            set({ 
              user, 
              token, 
              isAuthenticated: true 
            }, false, 'checkAuth:success')
            return true
          } else {
            // Token无效
            authTokens.remove()
            set({ 
              user: null, 
              token: null, 
              isAuthenticated: false 
            }, false, 'checkAuth:invalid')
            return false
          }
        } catch (error) {
          console.error('检查认证状态失败:', error)
          authTokens.remove()
          set({ 
            user: null, 
            token: null, 
            isAuthenticated: false 
          }, false, 'checkAuth:error')
          return false
        }
      },

      refreshUser: async () => {
        try {
          const user = await getCurrentUser()
          if (user) {
            set({ user }, false, 'refreshUser')
          }
        } catch (error) {
          console.error('刷新用户信息失败:', error)
        }
      }
    }),
    {
      name: 'auth-store'
    }
  )
)
