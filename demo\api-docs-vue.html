<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API文档 - AugmentAPI</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>🧸</text></svg>">

    <!-- 引入Element Plus CSS - 本地资源 -->
    <link rel="stylesheet" href="/lib/element-plus/index.min.css">

    <!-- 引入Vue 3 - 本地资源 -->
    <script src="/lib/vue/vue.global.min.js"></script>
    <!-- 引入Element Plus - 本地资源 -->
    <script src="/lib/element-plus/index.full.min.js"></script>
    <!-- 引入Element Plus图标 - 本地资源 -->
    <script src="/lib/element-plus/icons.iife.min.js"></script>
    <!-- 引入Socket.IO客户端 - 本地资源 -->
    <script src="/lib/socket.io/socket.io.min.js"></script>

    <!-- 认证工具函数 -->
    <script src="/js/auth-utils.js"></script>
    <!-- 静默控制台（生产环境隐藏console输出；加 ?debug=1 显示） -->
    <script src="/js/quiet-console.js"></script>

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: #f5f7fa;
        }

        .layout-container {
            height: 100vh;
            display: flex;
        }

        .sidebar {
            background: #fff;
            box-shadow: 2px 0 6px rgba(0,21,41,.35);
            transition: width 0.3s;
            overflow: hidden;
            position: relative;
        }

        .sidebar.collapsed {
            width: 64px !important;
        }

        .sidebar.collapsed .user-info {
            justify-content: center;
            padding: 0;
        }

        .sidebar.collapsed .user-details {
            display: none;
        }

        .sidebar.collapsed .user-avatar {
            margin-right: 0;
            flex-shrink: 0;
        }

        .sidebar.collapsed .el-menu-item span,
        .sidebar.collapsed .el-sub-menu .el-sub-menu__title span {
            display: none;
        }

        .sidebar.collapsed .el-sub-menu .el-sub-menu__icon-arrow {
            display: none;
        }

        .sidebar.expanded {
            width: 250px !important;
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .content-header {
            background: #fff;
            padding: 16px 24px;
            border-bottom: 1px solid #e8eaec;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .content-body {
            flex: 1;
            padding: 24px;
            overflow-y: auto;
            background: #f5f7fa;
        }

        .user-info {
            display: flex;
            align-items: center;
            padding: 16px;
            border-bottom: 1px solid #e8eaec;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin-right: 12px;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #409eff;
            color: white;
            font-weight: bold;
        }

        .user-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        /* 当有头像图片时，移除背景色（兼容不支持 :has 的浏览器） */
        .user-avatar.has-image {
            background: transparent;
        }

        /* 公告铃铛按钮样式 */
        .bell-button {
            position: relative;
            padding: 8px 12px !important;
            border-radius: 50% !important;
            font-size: 18px !important;
            transition: all 0.3s ease !important;
            border: none !important;
        }

        .bell-button:hover {
            background-color: rgba(64, 158, 255, 0.1) !important;
            transform: scale(1.1);
        }

        .has-unread {
            position: relative;
            animation: bell-shake 2s infinite;
            color: #409eff !important;
        }

        .has-unread::after {
            content: '';
            position: absolute;
            top: 2px;
            right: 2px;
            width: 8px;
            height: 8px;
            background: #f56c6c;
            border-radius: 50%;
            border: 2px solid #fff;
            z-index: 1;
        }

        @keyframes bell-shake {
            0%, 50%, 100% { transform: rotate(0deg); }
            10%, 30% { transform: rotate(-10deg); }
            20%, 40% { transform: rotate(10deg); }
        }


        .user-details {
            flex: 1;
            min-width: 0;
        }

        .user-name {
            font-weight: 600;
            color: #303133;
            font-size: 14px;
            margin-bottom: 4px;
            display: flex;
            align-items: center;
        }

        .user-email {
            color: #909399;
            font-size: 12px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .trust-level {
            margin-left: 8px;
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 10px;
            font-weight: 600;
        }

        .trust-level-0 { background: #f4f4f5; color: #909399; }
        .trust-level-1 { background: #ecf5ff; color: #409eff; }
        .trust-level-2 { background: #f0f9ff; color: #67c23a; }
        .trust-level-3 { background: #fdf6ec; color: #e6a23c; }
        .trust-level-4 { background: #fef0f0; color: #f56c6c; }

        .sidebar-toggle {
            position: absolute;
            top: 16px;
            right: -12px;
            width: 24px;
            height: 24px;
            background: #409eff;
            border: none;
            border-radius: 50%;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            z-index: 1000;
        }

        .sidebar-toggle:hover {
            background: #337ecc;
        }

        .api-card {
            background: #fff;
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 24px;
        }

        .api-title {
            font-size: 1.5em;
            color: #303133;
            margin-bottom: 24px;
            text-align: center;
            font-weight: 600;
        }

        .token-section {
            margin: 24px 0;
            padding: 16px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .token-section h3 {
            color: #303133;
            margin-bottom: 12px;
        }

        .token-display-box {
            margin-top: 8px;
        }

        .auth-section {
            margin: 24px 0;
        }

        .auth-section h2 {
            color: #409eff;
            margin-bottom: 16px;
            font-size: 1.3em;
        }

        .auth-section p {
            color: #606266;
            margin-bottom: 12px;
        }

        .endpoints-section {
            margin: 24px 0;
        }

        .endpoints-section h2 {
            color: #409eff;
            margin-bottom: 24px;
            font-size: 1.3em;
        }

        .endpoint {
            margin-bottom: 32px;
            padding: 20px;
            border: 1px solid #e8eaec;
            border-radius: 8px;
            background: #fafbfc;
        }

        .endpoint h3 {
            color: #303133;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
        }

        .endpoint h4 {
            color: #606266;
            margin: 16px 0 8px 0;
            font-size: 1.1em;
        }

        .endpoint p {
            color: #606266;
            margin-bottom: 12px;
        }

        .endpoint-url {
            background: #f0f9ff;
            padding: 8px 12px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            color: #1e40af;
            margin: 8px 0;
        }

        .method-tag {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            margin-right: 8px;
            color: white;
        }

        .method-tag.get { background: #67c23a; }
        .method-tag.post { background: #409eff; }
        .method-tag.delete { background: #f56c6c; }

        .code-block {
            background: #f5f7fa;
            border: 1px solid #dcdfe6;
            border-radius: 4px;
            padding: 12px;
            margin: 12px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
        }

        .code-block pre {
            margin: 0;
            white-space: pre-wrap;
            word-break: break-all;
        }

        .code-block code {
            background: none;
            padding: 0;
            color: #1e40af;
        }

        .example-section {
            margin-top: 16px;
            padding: 12px;
            background: #f0f9ff;
            border-radius: 4px;
        }

        .example-section strong {
            color: #1e40af;
        }

        .error-section {
            margin: 32px 0;
        }

        .error-section h2 {
            color: #f56c6c;
            margin-bottom: 16px;
            font-size: 1.3em;
        }

        .error-section p {
            color: #606266;
            margin-bottom: 12px;
        }

        .error-codes-section {
            margin: 32px 0;
        }

        .error-codes-section h2 {
            color: #e6a23c;
            margin-bottom: 16px;
            font-size: 1.3em;
        }

        /* 禁止textarea调整大小 */
        .token-display-box .el-textarea__inner {
            resize: none !important;
            min-height: 32px !important;
            height: 32px !important;
            max-height: 32px !important;
        }

        /* 移动端适配 */
        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                left: 0;
                top: 0;
                height: 100vh;
                z-index: 1000;
                transform: translateX(-100%);
                transition: transform 0.3s;
            }

            .sidebar.mobile-open {
                transform: translateX(0);
            }

            .main-content {
                width: 100%;
            }

            .content-body {
                padding: 16px;
            }
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="layout-container">
            <!-- 侧边栏 -->
            <div class="sidebar" :class="{
                collapsed: isCollapsed,
                expanded: !isCollapsed,
                'mobile-open': mobileMenuOpen
            }">
                <button class="sidebar-toggle" @click="toggleSidebar">
                    {{ isCollapsed ? '→' : '←' }}
                </button>

                <!-- 用户信息 -->
                <div class="user-info" v-if="currentUser">
                    <div class="user-avatar" :class="{ 'has-image': !!(currentUser && currentUser.linuxdo_avatar) }">
                        <img v-if="currentUser.linuxdo_avatar" :src="currentUser.linuxdo_avatar" :alt="currentUser.linuxdo_username">
                        <span v-else>{{ currentUser.email ? currentUser.email.charAt(0).toUpperCase() : '?' }}</span>
                    </div>
                    <div class="user-details" v-show="!isCollapsed">
                        <div class="user-name">
                            {{ currentUser.linuxdo_username || currentUser.email.split('@')[0] }}
                            <span v-if="currentUser.linuxdo_trust_level !== null"
                                  :class="`trust-level trust-level-${currentUser.linuxdo_trust_level}`">
                                {{ getTrustLevelName(currentUser.linuxdo_trust_level) }}
                            </span>
                        </div>
                        <div class="user-email">{{ currentUser.email }}</div>
                    </div>
                </div>

                <!-- 导航菜单 -->
                <el-menu
                    :default-active="activeMenu"
                    :collapse="isCollapsed"
                    :unique-opened="true"
                    background-color="#fff"
                    text-color="#303133"
                    active-text-color="#409eff">

                    <el-menu-item index="home" @click="navigateTo('/')">
                        <el-icon><i class="el-icon-house"></i></el-icon>
                        <span>首页</span>
                    </el-menu-item>

                    <el-menu-item index="profile" @click="navigateTo('/profile')">
                        <el-icon><i class="el-icon-user"></i></el-icon>
                        <span>个人中心</span>
                    </el-menu-item>

                    <el-menu-item index="chat" @click="navigateTo('/chat')">
                        <el-icon><i class="el-icon-chat-dot-round"></i></el-icon>
                        <span>实时聊天</span>
                    </el-menu-item>

                    <el-sub-menu index="api">
                        <template #title>
                            <el-icon><i class="el-icon-document"></i></el-icon>
                            <span>API管理</span>
                        </template>
                        <el-menu-item index="api-docs" @click="navigateTo('/api-docs')">
                            <el-icon><i class="el-icon-reading"></i></el-icon>
                            <span>API文档</span>
                        </el-menu-item>
                        <el-menu-item index="external-test" @click="navigateTo('/external-test')">
                            <el-icon><i class="el-icon-connection"></i></el-icon>
                            <span>外部接口测试</span>
                        </el-menu-item>
                    </el-sub-menu>

                        <el-menu-item index="docs" @click="navigateTo('/docs')">
                            <el-icon><i class="el-icon-folder"></i></el-icon>
                            <span>文档中心</span>
                        </el-menu-item>


                    <!-- 管理后台菜单项（仅管理员可见） -->
                    <el-menu-item v-if="currentUser && currentUser.isAdmin" index="admin" @click="navigateTo('/admin')">
                        <el-icon><i class="el-icon-setting"></i></el-icon>
                        <span>管理后台</span>
                    </el-menu-item>

                    <el-menu-item index="logout" @click="handleLogout">
                        <el-icon><i class="el-icon-switch-button"></i></el-icon>
                        <span>退出登录</span>
                    </el-menu-item>
                </el-menu>
            </div>

            <!-- 主内容区 -->
            <div class="main-content">
                <!-- 内容头部 -->
                <div class="content-header">
                    <div>
                        <el-button v-if="isMobile" @click="toggleMobileSidebar" icon="el-icon-menu" circle></el-button>
                        <span style="font-size: 18px; font-weight: 600; margin-left: 8px;">API文档</span>
                    </div>
                    <div>
                        <el-button @click="showAnnouncementDialog" :class="{ 'has-unread': hasUnreadAnnouncements, 'bell-button': true }" text>
                            🔔
                        </el-button>
                    </div>
                </div>

                <!-- 内容主体 -->
                <div class="content-body">
                    <!-- API文档内容 -->
                    <div class="api-card">
                        <div class="api-title">
                            📚API 外部接口文档
                        </div>

                        <!-- 重要提示 -->
                        <el-alert
                            title="⚠️ 重要提示"
                            type="warning"
                            :closable="false"
                            show-icon>
                            <p>所有外部API接口都需要在请求头中携带您的用户Token进行身份验证。</p>
                        </el-alert>

                        <!-- 用户Token显示 -->
                        <div v-if="currentUser && currentUser.auth_token" class="token-section">
                            <h3>🔑 您的用户Token：</h3>
                            <div class="token-display-box">
                                <el-input
                                    v-model="currentUser.auth_token"
                                    readonly
                                    type="textarea"
                                    :rows="1"
                                    style="min-height: 28px; height: 28px;">
                                </el-input>
                                <el-button type="primary" @click="copyToken(currentUser.auth_token)" style="margin-top: 8px;">
                                    复制Token
                                </el-button>
                            </div>
                        </div>

                        <!-- 认证方式 -->
                        <div class="auth-section">
                            <h2>🔐 认证方式</h2>
                            <p>所有API请求都需要在请求头中包含您的用户Token：</p>
                            <div class="code-block">
                                <code>Authorization: Bearer YOUR_USER_TOKEN</code>
                            </div>
                        </div>

                        <!-- 接口列表 -->
                        <div class="endpoints-section">
                            <h2>� 接口列表</h2>

                            <!-- 获取OAuth授权链接 -->
                            <div class="endpoint">
                                <h3>
                                    <span class="method-tag get">GET</span>
                                    获取OAuth授权链接
                                </h3>
                                <div class="endpoint-url">/api/external/auth-url</div>

                                <h4>请求参数</h4>
                                <p>无需额外参数，直接请求即可获取标准授权链接。</p>

                                <h4>响应示例</h4>
                                <div class="code-block">
                                    <pre>{
  "authorize_url": "https://auth.augmentcode.com/authorize?response_type=code&code_challenge=xxx&client_id=v&state=xxx&prompt=login"
}</pre>
                                </div>

                                <div class="example-section">
                                    <strong>📝 使用示例：</strong>
                                    <div class="code-block">
                                        <pre>curl -X GET "http://localhost:3000/api/external/auth-url" \
  -H "Authorization: Bearer YOUR_USER_TOKEN"</pre>
                                    </div>
                                </div>
                            </div>

                            <!-- 完成OAuth授权 -->
                            <div class="endpoint">
                                <h3>
                                    <span class="method-tag post">POST</span>
                                    完成OAuth授权
                                </h3>
                                <div class="endpoint-url">/api/external/complete-auth</div>

                                <h4>请求体参数</h4>
                                <el-table :data="authParams" style="width: 100%; margin: 16px 0;">
                                    <el-table-column prop="name" label="参数名" width="120"></el-table-column>
                                    <el-table-column prop="type" label="类型" width="80"></el-table-column>
                                    <el-table-column prop="required" label="必需" width="80"></el-table-column>
                                    <el-table-column prop="description" label="说明"></el-table-column>
                                </el-table>

                                <h4>响应示例</h4>
                                <div class="code-block">
                                    <pre>{
  "success": true,
  "token": "access_token_value",
  "tenant_url": "https://tenant.augmentcode.com",
  "token_info": {
    "id": 123,
    "created_at": "2025-01-01T00:00:00Z"
  },
  "user": {
    "uuid": "user_uuid",
    "email": "<EMAIL>"
  }
}</pre>
                                </div>

                                <div class="example-section">
                                    <strong>📝 使用示例：</strong>
                                    <div class="code-block">
                                        <pre>curl -X POST "http://localhost:3000/api/external/complete-auth" \
  -H "Authorization: Bearer YOUR_USER_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "code": "authorization_code_from_callback",
    "state": "state_value",
    "tenant_url": "https://your-tenant.augmentcode.com"
  }'</pre>
                                    </div>
                                </div>
                            </div>

                        </div>

                        <!-- 错误响应 -->
                        <div class="error-section">
                            <h2>❌ 错误响应</h2>
                            <p>当请求失败时，API会返回以下格式的错误响应：</p>
                            <div class="code-block">
                                <pre>{
  "success": false,
  "error": "错误描述",
  "details": "详细错误信息"
}</pre>
                            </div>
                        </div>

                        <!-- 常见错误码 -->
                        <div class="error-codes-section">
                            <h2>🔧 常见错误码</h2>
                            <el-table :data="errorCodes" style="width: 100%; margin: 16px 0;">
                                <el-table-column prop="code" label="状态码" width="120"></el-table-column>
                                <el-table-column prop="description" label="说明"></el-table-column>
                            </el-table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 系统公告弹窗 -->
        <el-dialog
            v-model="announcementDialogVisible"
            width="500px"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
            :show-close="true"
            class="announcement-dialog">

            <template #header>
                <span style="font-size: 18px; font-weight: 600;">系统公告</span>
            </template>

            <div v-if="currentAnnouncement" class="announcement-dialog-content">
                <div class="announcement-header">
                    <div class="announcement-title">
                        <el-tag v-if="currentAnnouncement.is_pinned" type="warning" size="small" style="margin-right: 8px;">置顶</el-tag>
                        <el-tag :type="getAnnouncementTypeColor(currentAnnouncement.type)" size="small" style="margin-right: 8px;">
                            {{ getAnnouncementTypeText(currentAnnouncement.type) }}
                        </el-tag>
                        <span style="font-size: 16px; font-weight: 600;">{{ currentAnnouncement.title }}</span>
                    </div>
                    <div class="announcement-meta" style="margin-top: 8px; color: #909399; font-size: 12px;">
                        发布时间：{{ formatDate(currentAnnouncement.created_at) }}
                        <span v-if="currentAnnouncement.author_name" style="margin-left: 16px;">
                            发布人：{{ currentAnnouncement.author_name }}
                        </span>
                    </div>
                    <el-divider />
                </div>
                <div class="announcement-content" style="line-height: 1.8; white-space: pre-wrap;">
                    {{ currentAnnouncement.content }}
                </div>
            </div>

            <template #footer>
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <div style="color: #909399; font-size: 12px;">
                        {{ currentAnnouncementIndex + 1 }} / {{ announcements.length }}
                    </div>
                    <div>
                        <el-button type="primary" @click="announcementDialogVisible = false">
                            知道了
                        </el-button>
                    </div>
                </div>
            </template>
        </el-dialog>

        <!-- 移动端遮罩 -->
        <div v-if="isMobile && mobileMenuOpen"
             style="position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.5); z-index: 999;"
             @click="closeMobileSidebar"></div>
    </div>

    <!-- CDN资源已由服务端自动注入到head部分 -->

    <script>
        const { createApp, ref, reactive, onMounted, computed } = Vue;

        createApp({
            setup() {
                // 响应式数据
                const isCollapsed = ref(false);
                const mobileMenuOpen = ref(false);
                const currentUser = ref(null);
                const activeMenu = ref('api-docs');

                // 公告数据
                const announcements = ref([]);
                const announcementDialogVisible = ref(false);
                const currentAnnouncement = ref(null);
                const currentAnnouncementIndex = ref(0);
                const hasUnreadAnnouncements = ref(false);

                // API文档数据
                const authParams = ref([
                    { name: 'code', type: 'string', required: '是', description: 'OAuth授权码' },
                    { name: 'state', type: 'string', required: '否', description: '状态参数验证' },
                    { name: 'tenant_url', type: 'string', required: '是', description: '租户URL（必填）' }
                ]);

                const errorCodes = ref([
                    { code: '401', description: '未提供Token或Token无效' },
                    { code: '400', description: '请求参数错误' },
                    { code: '500', description: '服务器内部错误' }
                ]);

                // 计算属性
                const isMobile = computed(() => {
                    return window.innerWidth <= 768;
                });

                // 方法
                const toggleSidebar = () => {
                    isCollapsed.value = !isCollapsed.value;
                };

                const toggleMobileSidebar = () => {
                    mobileMenuOpen.value = !mobileMenuOpen.value;
                };

                const closeMobileSidebar = () => {
                    mobileMenuOpen.value = false;
                };

                const navigateTo = (path) => {
                    window.location.href = path;
                };

                const refreshPage = () => {
                    window.location.reload();
                };

                const handleLogout = async () => {
                    try {
                        const response = await fetch('/api/logout', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            }
                        });

                        if (response.ok) {
                            localStorage.removeItem('auth_token');
                            window.location.href = '/login';
                        } else {
                            ElementPlus.ElMessage.error('登出失败，请重试');
                        }
                    } catch (error) {
                        console.error('登出错误:', error);
                        ElementPlus.ElMessage.error('网络错误，请重试');
                    }
                };

                const getTrustLevelName = (level) => {
                    const levelNames = {
                        0: '新用户',
                        1: '基础',
                        2: '成员',
                        3: '常规',
                        4: '领导'
                    };
                    return levelNames[level] || '未知';
                };

                const copyToken = async (token) => {
                    try {
                        await navigator.clipboard.writeText(token);
                        ElementPlus.ElMessage.success('Token已复制到剪贴板');
                    } catch (error) {
                        console.error('复制失败:', error);
                        ElementPlus.ElMessage.error('复制失败');
                    }
                };

                const checkLoginStatus = async () => {
                    try {
                        const response = await fetch('/api/user');
                        if (response.ok) {
                            const result = await response.json();
                            currentUser.value = result.user;
                            return true;
                        } else if (response.status === 401) {
                            window.location.href = '/login';
                            return false;
                        }
                    } catch (error) {
                        console.error('检查登录状态失败:', error);
                        return false;
                    }
                };

                // 初始化Socket.IO连接（用于在线人数统计）
                const initSocket = () => {
                    if (typeof io === 'undefined') {
                        console.warn('Socket.IO未加载');
                        return;
                    }

                    const socket = io();

                    socket.on('connect', () => {
                        console.log('Socket.IO连接成功 - 已被统计为在线用户');

                        // 如果用户已登录，发送认证信息（可选）
                        if (currentUser.value) {
                            socket.emit('authenticate', {
                                userEmail: currentUser.value.email
                            });
                        }
                    });

                    socket.on('disconnect', () => {
                        console.log('Socket.IO连接断开');
                    });

                    // 定期发送心跳（保持连接活跃）
                    setInterval(() => {
                        if (socket && socket.connected) {
                            socket.emit('heartbeat');
                        }
                    }, 30000); // 每30秒发送一次心跳
                };

                // 公告相关方法
                const showAnnouncementDialog = () => {
                    if (announcements.value.length > 0) {
                        const unreadAnnouncements = announcements.value.filter(announcement =>
                            !isAnnouncementRead(announcement.uuid)
                        );

                        if (unreadAnnouncements.length > 0) {
                            const firstUnreadIndex = announcements.value.findIndex(announcement =>
                                announcement.uuid === unreadAnnouncements[0].uuid
                            );
                            currentAnnouncementIndex.value = firstUnreadIndex;
                            currentAnnouncement.value = announcements.value[firstUnreadIndex];
                        } else {
                            currentAnnouncementIndex.value = 0;
                            currentAnnouncement.value = announcements.value[0];
                        }

                        announcementDialogVisible.value = true;
                        hasUnreadAnnouncements.value = false;
                    } else {
                        ElementPlus.ElMessage.info('暂无公告');
                    }
                };

                const loadAnnouncements = async () => {
                    try {
                        const response = await fetch('/api/announcements/active');
                        if (response.ok) {
                            const data = await response.json();
                            announcements.value = data.data || [];

                            const unreadAnnouncements = announcements.value.filter(announcement =>
                                !isAnnouncementRead(announcement.uuid)
                            );

                            if (unreadAnnouncements.length > 0) {
                                hasUnreadAnnouncements.value = true;
                            } else {
                                hasUnreadAnnouncements.value = false;
                            }
                        }
                    } catch (error) {
                        console.error('加载公告失败:', error);
                    }
                };

                const isAnnouncementRead = (announcementUuid) => {
                    try {
                        const reads = localStorage.getItem('announcement_reads');
                        if (!reads) return false;
                        const readData = JSON.parse(reads);
                        const readTime = readData[announcementUuid];
                        if (!readTime) return false;
                        const now = Date.now();
                        return (now - readTime) < (30 * 60 * 1000); // 30分钟
                    } catch (error) {
                        return false;
                    }
                };

                // 生命周期
                onMounted(async () => {
                    await checkLoginStatus();
                    await loadAnnouncements();

                    // 初始化Socket.IO连接
                    initSocket();

                    // 监听窗口大小变化
                    window.addEventListener('resize', () => {
                        if (!isMobile.value && mobileMenuOpen.value) {
                            mobileMenuOpen.value = false;
                        }
                    });
                });

                return {
                    isCollapsed,
                    mobileMenuOpen,
                    currentUser,
                    activeMenu,
                    isMobile,
                    authParams,
                    errorCodes,
                    toggleSidebar,
                    toggleMobileSidebar,
                    closeMobileSidebar,
                    navigateTo,
                    refreshPage,
                    handleLogout,
                    getTrustLevelName,
                    copyToken,
                    // 公告相关
                    announcements,
                    announcementDialogVisible,
                    currentAnnouncement,
                    currentAnnouncementIndex,
                    hasUnreadAnnouncements,
                    showAnnouncementDialog,
                    loadAnnouncements,
                    getAnnouncementTypeColor: (type) => {
                        const colors = { info: '', warning: 'warning', success: 'success', error: 'danger' };
                        return colors[type] || '';
                    },
                    getAnnouncementTypeText: (type) => {
                        const texts = { info: '信息', warning: '警告', success: '成功', error: '错误' };
                        return texts[type] || type;
                    },
                    formatDate: (dateString) => new Date(dateString).toLocaleDateString('zh-CN')
                };
            }
        }).use(ElementPlus).mount('#app');
    </script>
</body>
</html>
