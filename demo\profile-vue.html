<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人中心 - AugmentAPI</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>🧸</text></svg>">

    <!-- 引入Element Plus CSS - 本地资源 -->
    <link rel="stylesheet" href="/lib/element-plus/index.min.css">

    <!-- 引入Vue 3 - 本地资源 -->
    <script src="/lib/vue/vue.global.min.js"></script>
    <!-- 引入Element Plus - 本地资源 -->
    <script src="/lib/element-plus/index.full.min.js"></script>
    <!-- 引入Element Plus图标 - 本地资源 -->
    <script src="/lib/element-plus/icons.iife.min.js"></script>
    <!-- 引入Socket.IO客户端 - 本地资源 -->
    <script src="/lib/socket.io/socket.io.min.js"></script>

    <!-- 认证工具函数 -->
    <script src="/js/auth-utils.js"></script>
    <!-- 静默控制台（生产环境隐藏console输出；加 ?debug=1 显示） -->
    <script src="/js/quiet-console.js"></script>

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: #f5f7fa;
        }

        .layout-container {
            height: 100vh;
            display: flex;
        }

        .sidebar {
            background: #fff;
            box-shadow: 2px 0 6px rgba(0,21,41,.35);
            transition: width 0.3s;
            overflow: hidden;
            position: relative;
        }

        .sidebar.collapsed {
            width: 64px !important;
        }

        .sidebar.collapsed .user-info {
            justify-content: center;
            padding: 0;
        }

        .sidebar.collapsed .user-details {
            display: none;
        }

        .sidebar.collapsed .user-avatar {
            margin-right: 0;
            flex-shrink: 0;
        }

        .sidebar.collapsed .el-menu-item span,
        .sidebar.collapsed .el-sub-menu .el-sub-menu__title span {
            display: none;
        }

        .sidebar.collapsed .el-sub-menu .el-sub-menu__icon-arrow {
            display: none;
        }

        .sidebar.expanded {
            width: 250px !important;
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .content-header {
            background: #fff;
            padding: 16px 24px;
            border-bottom: 1px solid #e8eaec;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .content-body {
            flex: 1;
            padding: 24px;
            overflow-y: auto;
            background: #f5f7fa;
        }

        .user-info {
            display: flex;
            align-items: center;
            padding: 16px;
            border-bottom: 1px solid #e8eaec;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin-right: 12px;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #409eff;
            color: white;
            font-weight: bold;
        }

        .user-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        /* 当有头像图片时，移除背景色 */
        .user-avatar:has(img) {
            background: transparent;
        }

        .user-details {
            flex: 1;
            min-width: 0;
        }

        .user-name {
            font-weight: 600;
            color: #303133;
            font-size: 14px;
            margin-bottom: 4px;
            display: flex;
            align-items: center;
        }

        .user-email {
            color: #909399;
            font-size: 12px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .trust-level {
            margin-left: 8px;
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 10px;
            font-weight: 600;
        }

        .trust-level-0 { background: #f4f4f5; color: #909399; }
        .trust-level-1 { background: #ecf5ff; color: #409eff; }
        .trust-level-2 { background: #f0f9ff; color: #67c23a; }
        .trust-level-3 { background: #fdf6ec; color: #e6a23c; }
        .trust-level-4 { background: #fef0f0; color: #f56c6c; }

        .sidebar-toggle {
            position: absolute;
            top: 16px;
            right: -12px;
            width: 24px;
            height: 24px;
            background: #409eff;
            border: none;
            border-radius: 50%;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            z-index: 1000;
        }

        .sidebar-toggle:hover {
            background: #337ecc;
        }

        .profile-card {
            background: #fff;
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 24px;
        }

        .profile-header {
            display: flex;
            align-items: center;
            margin-bottom: 24px;
            padding-bottom: 20px;
            border-bottom: 1px solid #e8eaec;
        }

        .profile-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            margin-right: 20px;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-size: 2em;
            font-weight: bold;
        }

        .profile-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        /* 当有头像图片时，移除背景色 */
        .profile-avatar:has(img) {
            background: transparent;
        }

        .profile-info h1 {
            color: #2d3748;
            margin-bottom: 8px;
            font-size: 24px;
        }

        .profile-info p {
            color: #718096;
            font-size: 16px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 24px;
        }

        .info-item {
            background: linear-gradient(135deg, #ffffff 0%, #f8fbff 80%, #e6f3ff 100%);
            border-radius: 12px;
            padding: 16px;
            border: 1px solid #e3e6ea;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }

        .info-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            border-color: #409eff;
        }

        .info-label {
            font-weight: 600;
            color: #409eff;
            font-size: 14px;
            margin-bottom: 8px;
            display: block;
        }

        .token-tip {
            padding: 4px 8px;
            background: #f8f9fa;
            border-radius: 12px;
            border: 1px solid #e4e7ed;
            display: flex;
            align-items: center;
            font-size: 12px;
        }

        .token-tip-inline {
            padding: 4px 12px;
            background: linear-gradient(135deg, #ffffff 0%, #f8fbff 80%, #e6f3ff 100%);
            color: #6c757d;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 400;
            display: flex;
            align-items: center;
            border: 1px solid #dee2e6;
            transition: all 0.3s ease;
        }

        .token-tip-inline:hover {
            background: linear-gradient(135deg, #f8fbff 0%, #e6f3ff 80%, #d4edff 100%);
            color: #495057;
        }

        .info-value {
            color: #303133;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            word-break: break-all;
            background: #ffffff;
            padding: 8px 12px;
            border-radius: 6px;
            border: 1px solid #dcdfe6;
            display: block;
        }

        /* Token特殊样式 - 根据信任等级变色 */
        .token-value {
            cursor: pointer !important;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .token-value:hover {
            transform: scale(1.02);
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }

        /* 无Token提示样式 */
        .no-token-message {
            background: linear-gradient(135deg, #fef0f0 0%, #fde2e2 100%) !important;
            color: #f56c6c !important;
            border-color: #f56c6c !important;
            font-style: italic !important;
            text-align: center !important;
            position: relative;
        }

        .no-token-message::before {
            content: "⚠️ ";
            margin-right: 4px;
        }

        /* 信任等级对应的Token颜色 */
        .token-trust-0 {
            background: linear-gradient(135deg, #f4f4f5 0%, #e4e7ed 100%);
            color: #909399;
            border-color: #dcdfe6;
        }
        .token-trust-1 {
            background: linear-gradient(135deg, #ecf5ff 0%, #d9ecff 100%);
            color: #409eff;
            border-color: #b3d8ff;
        }
        .token-trust-2 {
            background: linear-gradient(135deg, #f0f9ff 0%, #e1f3d8 100%);
            color: #67c23a;
            border-color: #b3e19d;
        }
        .token-trust-3 {
            background: linear-gradient(135deg, #fdf6ec 0%, #faecd8 100%);
            color: #e6a23c;
            border-color: #f0c78a;
        }
        .token-trust-4 {
            background: linear-gradient(135deg, #fef0f0 0%, #fde2e2 100%);
            color: #f56c6c;
            border-color: #fab6b6;
        }

        /* 移动端适配 */
        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                left: 0;
                top: 0;
                height: 100vh;
                z-index: 1000;
                transform: translateX(-100%);
                transition: transform 0.3s;
            }

            .sidebar.mobile-open {
                transform: translateX(0);
            }

            .main-content {
                width: 100%;
            }

            .content-body {
                padding: 16px;
            }

            .profile-header {
                flex-direction: column;
                text-align: center;
            }

            .profile-avatar {
                margin-right: 0;
                margin-bottom: 16px;
            }

            .info-grid {
                grid-template-columns: 1fr;
                gap: 16px;
            }

            .info-item {
                padding: 14px;
            }

            .info-value {
                font-size: 12px;
            }
        }

        /* 账户绑定样式 */
        .binding-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 16px;
            margin-bottom: 20px;
        }

        .binding-item {
            background: linear-gradient(135deg, #ffffff 0%, #f8fbff 80%, #e6f3ff 100%);
            border-radius: 12px;
            padding: 20px;
            border: 1px solid #e3e6ea;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .binding-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            border-color: #409eff;
        }

        .binding-info {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .binding-icon {
            font-size: 32px;
            width: 48px;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(64, 158, 255, 0.1);
            border-radius: 50%;
        }

        .binding-details {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .binding-title {
            font-weight: 600;
            color: #303133;
            font-size: 16px;
        }

        .binding-status {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .status-connected {
            color: #67c23a;
            font-weight: 500;
            font-size: 14px;
        }

        .status-disconnected {
            color: #f56c6c;
            font-weight: 500;
            font-size: 14px;
        }

        .binding-username {
            color: #909399;
            font-size: 13px;
        }

        .binding-actions {
            display: flex;
            gap: 8px;
        }

        .binding-tips {
            background: linear-gradient(135deg, #fff9e6 0%, #fef7e0 100%);
            border: 1px solid #f0e68c;
            border-radius: 8px;
            padding: 16px;
            margin-top: 16px;
        }

        .tips-title {
            font-weight: 600;
            color: #e6a23c;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .tips-list {
            margin: 0;
            padding-left: 16px;
            color: #606266;
            font-size: 13px;
            line-height: 1.6;
        }

        .tips-list li {
            margin-bottom: 4px;
        }

        /* 移动端适配 */
        @media (max-width: 768px) {
            .binding-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 16px;
                padding: 16px;
            }

            .binding-actions {
                width: 100%;
                justify-content: flex-end;
            }
        }

        /* 公告铃铛按钮样式（与首页一致的精简版） */
        .bell-button { position: relative; padding: 8px 12px !important; border-radius: 50% !important; font-size: 18px !important; transition: all 0.3s ease !important; border: none !important; }
        .bell-button:hover { background-color: rgba(64, 158, 255, 0.1) !important; transform: scale(1.1); }
        .has-unread { position: relative; color: #409eff !important; }
        .has-unread::after { content: ''; position: absolute; top: 2px; right: 2px; width: 8px; height: 8px; background: #f56c6c; border-radius: 50%; border: 2px solid #fff; z-index: 1; }

    </style>
</head>
<body>
    <div id="app">
        <div class="layout-container">
            <!-- 侧边栏 -->
            <div class="sidebar" :class="{
                collapsed: isCollapsed,
                expanded: !isCollapsed,
                'mobile-open': mobileMenuOpen
            }">
                <button class="sidebar-toggle" @click="toggleSidebar">
                    {{ isCollapsed ? '→' : '←' }}
                </button>

                <!-- 用户信息 -->
                <div class="user-info" v-if="currentUser">
                    <div class="user-avatar">
                        <img v-if="currentUser.linuxdo_avatar" :src="currentUser.linuxdo_avatar" :alt="currentUser.linuxdo_username">
                        <span v-else>{{ currentUser.email ? currentUser.email.charAt(0).toUpperCase() : '?' }}</span>
                    </div>
                    <div class="user-details" v-show="!isCollapsed">
                        <div class="user-name">
                            {{ currentUser.linuxdo_username || currentUser.email.split('@')[0] }}
                            <span v-if="currentUser.linuxdo_trust_level !== null"
                                  :class="`trust-level trust-level-${currentUser.linuxdo_trust_level}`">
                                {{ getTrustLevelName(currentUser.linuxdo_trust_level) }}
                            </span>
                        </div>
                        <div class="user-email">{{ currentUser.email }}</div>
                    </div>
                </div>

                <!-- 导航菜单 -->
                <el-menu
                    :default-active="activeMenu"
                    :collapse="isCollapsed"
                    :unique-opened="true"
                    background-color="#fff"
                    text-color="#303133"
                    active-text-color="#409eff">

                    <el-menu-item index="home" @click="navigateTo('/')">
                        <el-icon><i class="el-icon-house"></i></el-icon>
                        <span>首页</span>
                    </el-menu-item>

                    <el-menu-item index="profile" @click="navigateTo('/profile')">
                        <el-icon><i class="el-icon-user"></i></el-icon>
                        <span>个人中心</span>
                    </el-menu-item>

                    <el-menu-item index="chat" @click="navigateTo('/chat')">
                        <el-icon><i class="el-icon-chat-dot-round"></i></el-icon>
                        <span>实时聊天</span>
                    </el-menu-item>

                    <el-sub-menu index="api">
                        <template #title>
                            <el-icon><i class="el-icon-document"></i></el-icon>
                            <span>API管理</span>
                        </template>
                        <el-menu-item index="api-docs" @click="navigateTo('/api-docs')">
                            <el-icon><i class="el-icon-reading"></i></el-icon>
                            <span>API文档</span>
                        </el-menu-item>
                        <el-menu-item index="external-test" @click="navigateTo('/external-test')">
                            <el-icon><i class="el-icon-connection"></i></el-icon>
                            <span>外部接口测试</span>
                        </el-menu-item>
                    </el-sub-menu>

                    <el-menu-item index="docs" @click="navigateTo('/docs')">
                        <el-icon><i class="el-icon-folder"></i></el-icon>
                        <span>文档中心</span>
                    </el-menu-item>


                    <!-- 管理后台入口 - 只对指定用户显示 -->
                    <el-menu-item v-if="isAdmin" index="admin" @click="navigateTo('/admin/')">
                        <el-icon><i class="el-icon-setting"></i></el-icon>
                        <span>管理后台</span>
                    </el-menu-item>

                    <el-menu-item index="logout" @click="handleLogout">
                        <el-icon><i class="el-icon-switch-button"></i></el-icon>
                        <span>退出登录</span>
                    </el-menu-item>
                </el-menu>
            </div>

            <!-- 主内容区 -->
            <div class="main-content">
                <!-- 内容头部 -->
                <div class="content-header">
                    <div>
                        <el-button v-if="isMobile" @click="toggleMobileSidebar" icon="el-icon-menu" circle></el-button>
                        <span style="font-size: 18px; font-weight: 600; margin-left: 8px;">个人中心</span>
                    </div>
                    <div>
                        <el-button @click="showAnnouncementDialog" :class="{ 'has-unread': hasUnreadAnnouncements, 'bell-button': true }" text>
                            🔔
                        </el-button>
                    </div>
                </div>

                <!-- 内容主体 -->
                <div class="content-body">
                    <!-- 用户信息卡片 -->
                    <div class="profile-card" v-if="currentUser">
                        <div class="profile-header">
                            <div class="profile-avatar">
                                <img v-if="currentUser.linuxdo_avatar" :src="currentUser.linuxdo_avatar" :alt="currentUser.linuxdo_username">
                                <span v-else>{{ currentUser.email ? currentUser.email.charAt(0).toUpperCase() : '?' }}</span>
                            </div>
                            <div class="profile-info">
                                <h1>{{ currentUser.linuxdo_username || currentUser.email.split('@')[0] }}</h1>
                                <p>
                                    <span>{{ currentUser.email }}</span>
                                    <span v-if="currentUser.linuxdo_trust_level !== null"
                                          :class="`trust-level trust-level-${currentUser.linuxdo_trust_level}`">
                                        {{ getTrustLevelName(currentUser.linuxdo_trust_level) }} {{ currentUser.linuxdo_trust_level }}
                                    </span>
                                </p>
                            </div>
                        </div>

                        <!-- 账户信息 -->
                        <div style="display: flex; align-items: center; margin-bottom: 20px;">
                            <h3 style="margin: 0; color: #303133; font-size: 18px;">📊 账户信息</h3>
                            <div class="token-tip-inline" style="margin-left: 445px;">
                                <i class="el-icon-info" style="margin-right: 6px;"></i>
                                <span>Token认证用于插件登录和接口认证</span>
                            </div>
                        </div>
                        <div class="info-grid">
                            <div class="info-item">
                                <span class="info-label">👤 用户ID</span>
                                <span class="info-value">{{ currentUser.uuid }}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">🔑 认证Token</span>
                                <span v-if="currentUser.auth_token"
                                      class="info-value token-value"
                                      :class="`token-trust-${currentUser.linuxdo_trust_level || 0}`"
                                      @click="copyToken(currentUser.auth_token)"
                                      title="点击复制Token">
                                    {{ currentUser.auth_token }}
                                </span>
                                <span v-else
                                      class="info-value no-token-message"
                                      style="color: #f56c6c; font-style: italic;">
                                    暂无令牌🔑-请联系管理员分发授权令牌
                                </span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">📅 注册时间</span>
                                <span class="info-value">{{ formatDate(currentUser.created_at) }}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">🔄 最后更新</span>
                                <span class="info-value">{{ formatDate(currentUser.updated_at) }}</span>
                            </div>
                        </div>

                        <!-- 账户绑定 -->
                        <div style="margin-top: 24px; padding-top: 24px; border-top: 1px solid #e8eaec;">
                            <h3 style="margin: 0 0 16px 0; color: #303133; font-size: 18px;">🔗 账户绑定</h3>
                            <div class="binding-grid">
                                <!-- LinuxDo 绑定状态 -->
                                <div class="binding-item">
                                    <div class="binding-info">
                                        <div class="binding-icon">🐧</div>
                                        <div class="binding-details">
                                            <div class="binding-title">LinuxDo</div>
                                            <div class="binding-status" v-if="currentUser.linuxdo_id">
                                                <span class="status-connected">已绑定</span>
                                                <span class="binding-username">{{ currentUser.linuxdo_username }}</span>
                                            </div>
                                            <div class="binding-status" v-else>
                                                <span class="status-disconnected">未绑定</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="binding-actions">
                                        <el-button
                                            v-if="currentUser.linuxdo_id"
                                            type="info"
                                            size="small"
                                            disabled>
                                            已绑定
                                        </el-button>
                                        <el-button
                                            v-else
                                            type="primary"
                                            size="small"
                                            :loading="bindingLoading.linuxdo"
                                            @click="bindLinuxDoAccount">
                                            绑定账户
                                        </el-button>
                                    </div>
                                </div>

                                <!-- GitHub 绑定状态 -->
                                <div class="binding-item">
                                    <div class="binding-info">
                                        <div class="binding-icon">🐙</div>
                                        <div class="binding-details">
                                            <div class="binding-title">GitHub</div>
                                            <div class="binding-status" v-if="currentUser.github_id">
                                                <span class="status-connected">已绑定</span>
                                                <span class="binding-username">{{ currentUser.github_username }}</span>
                                            </div>
                                            <div class="binding-status" v-else>
                                                <span class="status-disconnected">未绑定</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="binding-actions">
                                        <el-button
                                            v-if="currentUser.github_id"
                                            type="danger"
                                            size="small"
                                            :loading="bindingLoading.github"
                                            @click="unbindGitHubAccount">
                                            解除绑定
                                        </el-button>
                                        <el-button
                                            v-else
                                            type="success"
                                            size="small"
                                            :loading="bindingLoading.github"
                                            @click="bindGitHubAccount">
                                            绑定账户
                                        </el-button>
                                    </div>
                                </div>
                            </div>

                            <!-- 绑定说明 -->
                            <div class="binding-tips">
                                <div class="tips-title">💡 绑定说明</div>
                                <ul class="tips-list">
                                    <li>绑定多个账户可以使用不同方式登录同一个账户</li>
                                    <li>绑定后会同步头像和用户名信息</li>
                                    <li>解除绑定不会影响已有的Token和数据</li>
                                    <li>建议至少保留一种登录方式</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- API Token管理 -->
                    <div class="profile-card">
                        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
                            <div>
                                <h3 style="margin: 0; color: #303133;">🔑 我的API Token</h3>
                                <p style="color: #909399; margin: 4px 0 0 0;">总计: {{ tokens.length }} 个API Token</p>
                            </div>
                            <div style="display: flex; gap: 12px;">
                                <el-button type="primary" @click="showImportDialog">
                                    📥 导入Token
                                </el-button>
                                <el-button type="success" @click="exportTokens" :disabled="tokens.length === 0">
                                    📤 导出Token
                                </el-button>
                            </div>
                        </div>

                        <!-- 空数据提示 -->
                        <div v-if="tokens.length === 0" style="text-align: center; padding: 40px 20px;">
                            <div style="font-size: 64px; color: #c0c4cc; margin-bottom: 16px;">🔑</div>
                            <p style="color: #909399; margin-bottom: 16px; font-size: 16px;">暂无API Token</p>
                            <p style="color: #c0c4cc; margin-bottom: 24px; font-size: 14px;">
                                您可以通过OAuth授权获取Token，或手动导入已有Token
                            </p>
                            <div style="display: flex; gap: 12px; justify-content: center; flex-wrap: wrap;">
                                <el-button type="primary" @click="navigateTo('/')">
                                    🚀 前往首页授权
                                </el-button>
                                <el-button type="success" @click="showImportDialog">
                                    📥 导入Token
                                </el-button>
                                <el-button type="info" @click="exportTokens" disabled>
                                    📤 导出Token
                                </el-button>
                            </div>
                        </div>

                        <!-- Token列表 -->
                        <div v-else>
                            <el-table :data="paginatedTokens" style="width: 100%">
                                <el-table-column prop="id" label="ID" width="80"></el-table-column>
                                <el-table-column prop="token" label="Token" min-width="200">
                                    <template #default="scope">
                                        <span style="font-family: monospace; cursor: pointer;" @click="copyToken(scope.row.token)">
                                            {{ scope.row.token }}
                                        </span>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="tenant_url" label="租户URL" min-width="150"></el-table-column>
                                <el-table-column prop="user_ck" label="使用者邮箱" min-width="150">
                                    <template #default="scope">
                                        <span v-if="scope.row.user_ck" style="color: #409eff;">{{ scope.row.user_ck }}</span>
                                        <span v-else style="color: #909399;">未被使用</span>
                                    </template>
                                </el-table-column>
                                <el-table-column prop="created_at" label="创建时间" width="180">
                                    <template #default="scope">
                                        {{ formatDate(scope.row.created_at) }}
                                    </template>
                                </el-table-column>
                                <el-table-column label="操作" width="220">
                                    <template #default="scope">
                                        <div v-if="scope.row.is_active" style="display: flex; gap: 8px; flex-wrap: wrap;">
                                            <el-button type="primary" size="small" @click="editToken(scope.row)">
                                                编辑
                                            </el-button>
                                            <el-button
                                                v-if="!scope.row.use_time && !scope.row.user_ck"
                                                type="warning"
                                                size="small"
                                                @click="transferToken(scope.row)"
                                                :disabled="transferring">
                                                转让
                                            </el-button>
                                            <el-button type="danger" size="small" @click="deleteToken(scope.row.id)">
                                                删除
                                            </el-button>
                                        </div>
                                        <span v-else style="color: #909399;">已删除</span>
                                    </template>
                                </el-table-column>
                            </el-table>

                            <!-- 分页组件 -->
                            <div style="margin-top: 20px; display: flex; justify-content: center;">
                                <el-pagination
                                    v-model:current-page="currentPage"
                                    v-model:page-size="pageSize"
                                    :page-sizes="[10, 20, 50, 100]"
                                    :total="tokens.length"
                                    layout="total, sizes, prev, pager, next, jumper"
                                    background
                                    @size-change="handleSizeChange"
                                    @current-change="handleCurrentChange">
                                </el-pagination>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 系统公告弹窗 -->
        <el-dialog
            v-model="announcementDialogVisible"
            width="500px"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
            :show-close="true"
            class="announcement-dialog">

            <template #header>
                <span style="font-size: 18px; font-weight: 600;">系统公告</span>
            </template>

            <div v-if="currentAnnouncement" class="announcement-dialog-content">
                <div class="announcement-header">
                    <div class="announcement-title">
                        <el-tag v-if="currentAnnouncement.is_pinned" type="warning" size="small" style="margin-right: 8px;">置顶</el-tag>
                        <el-tag :type="getAnnouncementTypeColor(currentAnnouncement.type)" size="small" style="margin-right: 8px;">
                            {{ getAnnouncementTypeText(currentAnnouncement.type) }}
                        </el-tag>
                        <span style="font-size: 16px; font-weight: 600;">{{ currentAnnouncement.title }}</span>
                    </div>
                    <div class="announcement-meta" style="margin-top: 8px; color: #909399; font-size: 12px;">
                        发布时间：{{ formatDate(currentAnnouncement.created_at) }}
                        <span v-if="currentAnnouncement.author_name" style="margin-left: 16px;">
                            发布人：{{ currentAnnouncement.author_name }}
                        </span>
                    </div>
                    <el-divider />
                </div>
                <div class="announcement-content" style="line-height: 1.8; white-space: pre-wrap;">
                    {{ currentAnnouncement.content }}
                </div>
            </div>

            <template #footer>
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <div style="color: #909399; font-size: 12px;">
                        {{ currentAnnouncementIndex + 1 }} / {{ announcements.length }}
                    </div>
                    <div>
                        <el-button type="primary" @click="announcementDialogVisible = false">
                            知道了
                        </el-button>
                    </div>
                </div>
            </template>
        </el-dialog>

        <!-- 移动端遮罩 -->
        <div v-if="isMobile && mobileMenuOpen"
             style="position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.5); z-index: 999;"
             @click="closeMobileSidebar"></div>

        <!-- Token转让对话框 -->
        <el-dialog v-model="transferDialogVisible" title="🔄 转让Token" width="500px">
            <div v-if="currentTransferToken">
                <div style="margin-bottom: 20px; padding: 15px; background-color: #f5f7fa; border-radius: 4px;">
                    <h4 style="margin: 0 0 10px 0; color: #303133;">转让的Token信息</h4>
                    <p style="margin: 5px 0; color: #606266;"><strong>Token ID:</strong> {{ currentTransferToken.id }}</p>
                    <p style="margin: 5px 0; color: #606266; font-family: monospace; word-break: break-all;">
                        <strong>Token:</strong> {{ currentTransferToken.token.substring(0, 20) }}...
                    </p>
                    <p style="margin: 5px 0; color: #606266;"><strong>租户URL:</strong> {{ currentTransferToken.tenant_url }}</p>
                </div>

                <div style="margin-bottom: 20px;">
                    <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #303133;">
                        搜索接收方用户
                    </label>
                    <el-input
                        v-model="searchQuery"
                        placeholder="输入邮箱或用户名搜索..."
                        @input="searchUsers"
                        :loading="searching"
                        clearable>
                        <template #prefix>
                            <el-icon><Search /></el-icon>
                        </template>
                    </el-input>
                </div>

                <!-- 搜索结果 -->
                <div v-if="searchResults.length > 0" style="margin-bottom: 20px;">
                    <label style="display: block; margin-bottom: 8px; font-weight: bold; color: #303133;">
                        搜索结果
                    </label>
                    <div style="max-height: 200px; overflow-y: auto; border: 1px solid #dcdfe6; border-radius: 4px;">
                        <div
                            v-for="user in searchResults"
                            :key="user.email"
                            @click="selectRecipient(user)"
                            style="padding: 12px; border-bottom: 1px solid #ebeef5; cursor: pointer; display: flex; align-items: center; gap: 12px;"
                            :style="{ backgroundColor: selectedRecipient?.email === user.email ? '#f0f9ff' : 'transparent' }"
                            @mouseover="$event.target.style.backgroundColor = '#f5f7fa'"
                            @mouseleave="$event.target.style.backgroundColor = selectedRecipient?.email === user.email ? '#f0f9ff' : 'transparent'">
                            <img
                                :src="user.linuxdo_avatar"
                                :alt="user.linuxdo_username"
                                style="width: 32px; height: 32px; border-radius: 50%; object-fit: cover;"
                                @error="$event.target.src = '/default-avatar.png'">
                            <div>
                                <div style="font-weight: bold; color: #303133;">{{ user.linuxdo_username }}</div>
                                <div style="font-size: 12px; color: #909399;">{{ user.email }}</div>
                            </div>
                            <el-icon v-if="selectedRecipient?.email === user.email" style="margin-left: auto; color: #409eff;">
                                <Check />
                            </el-icon>
                        </div>
                    </div>
                </div>

                <!-- 已选择的接收方 -->
                <div v-if="selectedRecipient" style="margin-bottom: 20px; padding: 15px; background-color: #f0f9ff; border: 1px solid #b3d8ff; border-radius: 4px;">
                    <h4 style="margin: 0 0 10px 0; color: #303133;">已选择的接收方</h4>
                    <div style="display: flex; align-items: center; gap: 12px;">
                        <img
                            :src="selectedRecipient.linuxdo_avatar"
                            :alt="selectedRecipient.linuxdo_username"
                            style="width: 40px; height: 40px; border-radius: 50%; object-fit: cover;"
                            @error="$event.target.src = '/default-avatar.png'">
                        <div>
                            <div style="font-weight: bold; color: #303133;">{{ selectedRecipient.linuxdo_username }}</div>
                            <div style="color: #606266;">{{ selectedRecipient.email }}</div>
                        </div>
                    </div>
                </div>
            </div>

            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="transferDialogVisible = false">取消</el-button>
                    <el-button
                        type="primary"
                        @click="confirmTransfer"
                        :loading="transferring"
                        :disabled="!selectedRecipient">
                        确认转让
                    </el-button>
                </span>
            </template>
        </el-dialog>

        <!-- 导入Token对话框 -->
        <el-dialog v-model="importDialogVisible" title="📥 导入Token" width="600px">
            <!-- 导入方式选择 -->
            <el-radio-group v-model="importMode" style="margin-bottom: 20px;">
                <el-radio-button label="manual">手动输入</el-radio-button>
                <el-radio-button label="json">JSON文件</el-radio-button>
            </el-radio-group>

            <!-- 手动输入模式 -->
            <div v-if="importMode === 'manual'">
                <el-form :model="importForm" :rules="importRules" ref="importFormRef" label-width="100px">
                    <el-form-item label="Token" prop="token">
                        <el-input
                            v-model="importForm.token"
                            type="textarea"
                            :rows="3"
                            placeholder="请输入完整的Access Token">
                        </el-input>
                    </el-form-item>
                    <el-form-item label="租户URL" prop="tenant_url">
                        <el-input
                            v-model="importForm.tenant_url"
                            placeholder="https://your-tenant.augmentcode.com">
                        </el-input>
                    </el-form-item>
                </el-form>
            </div>

            <!-- JSON文件上传模式 -->
            <div v-if="importMode === 'json'">
                <el-upload
                    ref="uploadRef"
                    :auto-upload="false"
                    :show-file-list="true"
                    :limit="1"
                    accept=".json"
                    :on-change="handleFileChange"
                    :on-remove="handleFileRemove"
                    drag>
                    <el-icon class="el-icon--upload"><upload-filled /></el-icon>
                    <div class="el-upload__text">
                        将JSON文件拖到此处，或<em>点击上传</em>
                    </div>
                    <template #tip>
                        <div class="el-upload__tip">
                            只能上传JSON文件，文件大小不超过2MB
                        </div>
                    </template>
                </el-upload>

                <!-- JSON格式说明 -->
                <el-collapse style="margin-top: 20px;">
                    <el-collapse-item title="📋 JSON格式说明" name="format">
                        <pre style="background: #f5f5f5; padding: 15px; border-radius: 4px; font-size: 12px; overflow-x: auto;">{{jsonExample}}</pre>
                    </el-collapse-item>
                </el-collapse>

                <!-- 预览解析结果 -->
                <div v-if="parsedTokens.length > 0" style="margin-top: 20px;">
                    <h4>📊 解析结果预览 (共{{parsedTokens.length}}个Token)</h4>
                    <el-table :data="parsedTokens" style="width: 100%" max-height="200">
                        <el-table-column prop="tenant_url" label="租户URL" width="200" show-overflow-tooltip></el-table-column>
                        <el-table-column prop="access_token" label="Token" show-overflow-tooltip>
                            <template #default="scope">
                                {{scope.row.access_token.substring(0, 20)}}...
                            </template>
                        </el-table-column>
                        <el-table-column prop="created_at" label="创建时间" width="120"></el-table-column>
                    </el-table>
                </div>
            </div>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="importDialogVisible = false">取消</el-button>
                    <el-button
                        v-if="importMode === 'manual'"
                        type="primary"
                        @click="submitImport"
                        :loading="importLoading">
                        {{ importLoading ? '导入中...' : '确认导入' }}
                    </el-button>
                    <el-button
                        v-if="importMode === 'json'"
                        type="primary"
                        @click="submitJsonImport"
                        :loading="importLoading"
                        :disabled="parsedTokens.length === 0">
                        {{ importLoading ? '批量导入中...' : `批量导入(${parsedTokens.length}个)` }}
                    </el-button>
                </span>
            </template>
        </el-dialog>

        <!-- 编辑Token对话框 -->
        <el-dialog v-model="editDialogVisible" title="✏️ 编辑Token" width="500px">
            <el-form :model="editForm" :rules="editRules" ref="editFormRef" label-width="100px">
                <el-form-item label="Token ID">
                    <el-input v-model="editForm.id" disabled></el-input>
                </el-form-item>
                <el-form-item label="Token" prop="token">
                    <el-input
                        v-model="editForm.token"
                        placeholder="请输入完整的Access Token">
                    </el-input>
                </el-form-item>
                <el-form-item label="租户URL" prop="tenant_url">
                    <el-input
                        v-model="editForm.tenant_url"
                        placeholder="https://your-tenant.augmentcode.com">
                    </el-input>
                </el-form-item>

            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="editDialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="submitEdit" :loading="editLoading">
                        {{ editLoading ? '更新中...' : '确认更新' }}
                    </el-button>
                </span>
            </template>
        </el-dialog>
    </div>

    <!-- CDN资源已由服务端自动注入到head部分 -->

    <script>
        const { createApp, ref, reactive, onMounted, computed } = Vue;

        createApp({
            setup() {
                // 响应式数据
                const isCollapsed = ref(false);
                const mobileMenuOpen = ref(false);
                const currentUser = ref(null);
                const tokens = ref([]);
                const activeMenu = ref('profile');

                // 公告数据
                const announcements = ref([]);
                const announcementDialogVisible = ref(false);
                const currentAnnouncement = ref(null);
                const currentAnnouncementIndex = ref(0);
                const hasUnreadAnnouncements = ref(false);
                const currentPage = ref(1);
                const pageSize = ref(10);
                const isAdmin = ref(false);

                // Token转让相关数据
                const transferDialogVisible = ref(false);
                const transferring = ref(false);
                const currentTransferToken = ref(null);
                const searchQuery = ref('');
                const searchResults = ref([]);
                const selectedRecipient = ref(null);
                const searching = ref(false);

                // 导入Token相关数据
                const importDialogVisible = ref(false);
                const importLoading = ref(false);
                const importMode = ref('manual'); // 'manual' 或 'json'
                const importForm = reactive({
                    token: '',
                    tenant_url: '',
                    user_ck: ''
                });
                const importFormRef = ref(null);

                // JSON导入相关数据
                const uploadRef = ref(null);
                const parsedTokens = ref([]);
                const jsonExample = `{
  "tokens": [
    {
      "tenant_url": "https://d8.api.augmentcode.com/",
      "access_token": "e58f1e676ceb79ab1c70fd65490950f42abf10ce0d8b354dd791fd2e59a8f0a2",
      "created_at": "2025-08-11T06:11:53.905298Z"
    },
    {
      "tenant_url": "https://d10.api.augmentcode.com/",
      "access_token": "4cd6011bacdba6ec0b1b4d8951462002a68399dfbadcc421e6c1e7eb5462fd7d",
      "created_at": "2025-08-11T06:13:00.640908300Z"
    }
  ]
}`;

                // 编辑Token相关数据
                const editDialogVisible = ref(false);
                const editLoading = ref(false);
                const editForm = reactive({
                    id: '',
                    token: '',
                    tenant_url: '',
                    user_ck: ''
                });
                const editFormRef = ref(null);

                // 账户绑定相关数据
                const bindingLoading = reactive({
                    linuxdo: false,
                    github: false
                });

                // 表单验证规则
                const importRules = {
                    token: [
                        { required: true, message: '请输入Token', trigger: 'blur' },
                        { min: 10, message: 'Token长度不能少于10个字符', trigger: 'blur' }
                    ],
                    tenant_url: [
                        { required: true, message: '请输入租户URL', trigger: 'blur' },
                        { type: 'url', message: '请输入有效的URL', trigger: 'blur' }
                    ]
                };

                const editRules = {
                    token: [
                        { required: true, message: '请输入Token', trigger: 'blur' },
                        { min: 10, message: 'Token长度不能少于10个字符', trigger: 'blur' }
                    ],
                    tenant_url: [
                        { type: 'url', message: '请输入有效的URL', trigger: 'blur' }
                    ]
                };

                // 计算属性
                const isMobile = computed(() => {
                    return window.innerWidth <= 768;
                });

                const paginatedTokens = computed(() => {
                    const start = (currentPage.value - 1) * pageSize.value;
                    const end = start + pageSize.value;
                    return tokens.value.slice(start, end);
                });

                // 方法
                const toggleSidebar = () => {
                    isCollapsed.value = !isCollapsed.value;
                };

                const toggleMobileSidebar = () => {
                    mobileMenuOpen.value = !mobileMenuOpen.value;
                };

                const closeMobileSidebar = () => {
                    mobileMenuOpen.value = false;
                };

                const navigateTo = (path) => {
                    window.location.href = path;
                };

                const refreshPage = () => {
                    window.location.reload();
                };

                const handleSizeChange = (val) => {
                    pageSize.value = val;
                    currentPage.value = 1; // 重置到第一页
                };

                const handleCurrentChange = (val) => {
                    currentPage.value = val;
                };

                const handleLogout = async () => {
                    try {
                        const response = await fetch('/api/logout', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            }
                        });

                        if (response.ok) {
                            localStorage.removeItem('auth_token');
                            window.location.href = '/login';
                        } else {
                            ElementPlus.ElMessage.error('登出失败，请重试');
                        }
                    } catch (error) {
                        console.error('登出错误:', error);
                        ElementPlus.ElMessage.error('网络错误，请重试');
                    }
                };

                const getTrustLevelName = (level) => {
                    const levelNames = {
                        0: '新用户',
                        1: '基础',
                        2: '成员',
                        3: '常规',
                        4: '领导'
                    };
                    return levelNames[level] || '未知';
                };

                const formatDate = (dateString) => {
                    if (!dateString) return '-';
                    return new Date(dateString).toLocaleString('zh-CN');
                };

                const copyToken = async (token) => {
                    try {
                        await navigator.clipboard.writeText(token);
                        ElementPlus.ElMessage.success('Token已复制到剪贴板');
                    } catch (error) {
                        console.error('复制失败:', error);
                        ElementPlus.ElMessage.error('复制失败');
                    }
                };

                const deleteToken = async (tokenId) => {
                    try {
                        const confirmed = await ElementPlus.ElMessageBox.confirm(
                            '确定要删除这个Token吗？删除后无法恢复。',
                            '确认删除',
                            {
                                confirmButtonText: '确定',
                                cancelButtonText: '取消',
                                type: 'warning',
                            }
                        );

                        if (confirmed) {
                            const response = await fetch(`/api/user/tokens/${tokenId}`, {
                                method: 'DELETE'
                            });

                            if (response.ok) {
                                ElementPlus.ElMessage.success('Token删除成功');
                                await loadUserTokens();
                            } else {
                                ElementPlus.ElMessage.error('Token删除失败');
                            }
                        }
                    } catch (error) {
                        if (error !== 'cancel') {
                            console.error('删除Token失败:', error);
                            ElementPlus.ElMessage.error('删除Token失败');
                        }
                    }
                };

                // Token转让相关方法
                const transferToken = (token) => {
                    currentTransferToken.value = token;
                    searchQuery.value = '';
                    searchResults.value = [];
                    selectedRecipient.value = null;
                    transferDialogVisible.value = true;
                };

                // 搜索用户
                const searchUsers = async () => {
                    if (!searchQuery.value.trim()) {
                        searchResults.value = [];
                        return;
                    }

                    searching.value = true;
                    try {
                        const response = await fetch(`/api/users/search?q=${encodeURIComponent(searchQuery.value.trim())}&limit=10`);
                        if (response.ok) {
                            const result = await response.json();
                            searchResults.value = result.users || [];
                        } else {
                            searchResults.value = [];
                            ElementPlus.ElMessage.error('搜索用户失败');
                        }
                    } catch (error) {
                        console.error('搜索用户失败:', error);
                        searchResults.value = [];
                        ElementPlus.ElMessage.error('搜索用户失败');
                    } finally {
                        searching.value = false;
                    }
                };

                // 选择接收方
                const selectRecipient = (user) => {
                    selectedRecipient.value = user;
                    searchQuery.value = user.email;
                    searchResults.value = [];
                };

                // 确认转让
                const confirmTransfer = async () => {
                    if (!selectedRecipient.value) {
                        ElementPlus.ElMessage.error('请选择接收方');
                        return;
                    }

                    try {
                        const confirmed = await ElementPlus.ElMessageBox.confirm(
                            `确定要将Token转让给 ${selectedRecipient.value.email} (${selectedRecipient.value.linuxdo_username}) 吗？转让后您将失去此Token的所有权。`,
                            '确认转让',
                            {
                                confirmButtonText: '确定转让',
                                cancelButtonText: '取消',
                                type: 'warning',
                            }
                        );

                        if (confirmed) {
                            transferring.value = true;
                            const response = await fetch(`/api/user/tokens/${currentTransferToken.value.id}/transfer`, {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json'
                                },
                                body: JSON.stringify({
                                    recipientEmail: selectedRecipient.value.email
                                })
                            });

                            if (response.ok) {
                                ElementPlus.ElMessage.success('Token转让成功');
                                transferDialogVisible.value = false;
                                await loadUserTokens();
                            } else {
                                const errorData = await response.json();
                                ElementPlus.ElMessage.error(errorData.error || 'Token转让失败');
                            }
                        }
                    } catch (error) {
                        if (error !== 'cancel') {
                            console.error('Token转让失败:', error);
                            ElementPlus.ElMessage.error('Token转让失败');
                        }
                    } finally {
                        transferring.value = false;
                    }
                };

                // 显示导入对话框
                const showImportDialog = () => {
                    // 重置表单
                    importForm.token = '';
                    importForm.tenant_url = '';
                    importForm.user_ck = '';
                    // 重置JSON导入相关数据
                    importMode.value = 'manual';
                    parsedTokens.value = [];
                    if (uploadRef.value) {
                        uploadRef.value.clearFiles();
                    }
                    importDialogVisible.value = true;
                };

                // 提交导入
                const submitImport = async () => {
                    try {
                        // 验证表单
                        await importFormRef.value.validate();

                        importLoading.value = true;

                        const response = await fetch('/api/user/tokens', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify({
                                token: importForm.token.trim(),
                                tenant_url: importForm.tenant_url.trim(),
                                user_ck: importForm.user_ck.trim() || null
                            })
                        });

                        const result = await response.json();

                        if (response.ok && result.success) {
                            ElementPlus.ElMessage.success('Token导入成功');
                            importDialogVisible.value = false;
                            await loadUserTokens();
                        } else {
                            ElementPlus.ElMessage.error(result.error || 'Token导入失败');
                        }

                    } catch (error) {
                        console.error('导入Token失败:', error);
                        ElementPlus.ElMessage.error('导入Token失败');
                    } finally {
                        importLoading.value = false;
                    }
                };

                // 处理文件变化
                const handleFileChange = (file) => {
                    if (file.raw) {
                        // 检查文件大小（2MB限制）
                        if (file.raw.size > 2 * 1024 * 1024) {
                            ElementPlus.ElMessage.error('文件大小不能超过2MB');
                            uploadRef.value.clearFiles();
                            return;
                        }

                        // 读取文件内容
                        const reader = new FileReader();
                        reader.onload = (e) => {
                            try {
                                const jsonData = JSON.parse(e.target.result);

                                // 验证JSON格式
                                if (!jsonData.tokens || !Array.isArray(jsonData.tokens)) {
                                    throw new Error('JSON格式错误：缺少tokens数组');
                                }

                                // 验证每个token的格式
                                const validTokens = [];
                                for (let i = 0; i < jsonData.tokens.length; i++) {
                                    const token = jsonData.tokens[i];
                                    if (!token.tenant_url || !token.access_token) {
                                        ElementPlus.ElMessage.warning(`第${i+1}个Token缺少必要字段，已跳过`);
                                        continue;
                                    }
                                    validTokens.push({
                                        tenant_url: token.tenant_url,
                                        access_token: token.access_token,
                                        created_at: token.created_at || '未知'
                                    });
                                }

                                if (validTokens.length === 0) {
                                    throw new Error('没有找到有效的Token');
                                }

                                parsedTokens.value = validTokens;
                                ElementPlus.ElMessage.success(`成功解析${validTokens.length}个Token`);

                            } catch (error) {
                                console.error('解析JSON失败:', error);
                                ElementPlus.ElMessage.error(`解析JSON失败: ${error.message}`);
                                parsedTokens.value = [];
                                uploadRef.value.clearFiles();
                            }
                        };
                        reader.readAsText(file.raw);
                    }
                };

                // 处理文件移除
                const handleFileRemove = () => {
                    parsedTokens.value = [];
                };

                // 提交JSON批量导入
                const submitJsonImport = async () => {
                    if (parsedTokens.value.length === 0) {
                        ElementPlus.ElMessage.error('请先上传并解析JSON文件');
                        return;
                    }

                    try {
                        importLoading.value = true;

                        let successCount = 0;
                        let failCount = 0;
                        const errors = [];

                        // 逐个导入Token
                        for (const tokenData of parsedTokens.value) {
                            try {
                                const response = await fetch('/api/user/tokens', {
                                    method: 'POST',
                                    headers: {
                                        'Content-Type': 'application/json'
                                    },
                                    body: JSON.stringify({
                                        token: tokenData.access_token.trim(),
                                        tenant_url: tokenData.tenant_url.trim(),
                                        user_ck: null
                                    })
                                });

                                const result = await response.json();

                                if (response.ok && result.success) {
                                    successCount++;
                                } else {
                                    failCount++;
                                    errors.push(`${tokenData.tenant_url}: ${result.error || '导入失败'}`);
                                }
                            } catch (error) {
                                failCount++;
                                errors.push(`${tokenData.tenant_url}: ${error.message}`);
                            }
                        }

                        // 显示结果
                        if (successCount > 0) {
                            ElementPlus.ElMessage.success(`成功导入${successCount}个Token`);
                            await loadUserTokens();
                        }

                        if (failCount > 0) {
                            const errorMsg = `${failCount}个Token导入失败:\n${errors.slice(0, 3).join('\n')}${errors.length > 3 ? '\n...' : ''}`;
                            ElementPlus.ElMessage.error(errorMsg);
                        }

                        if (successCount > 0) {
                            importDialogVisible.value = false;
                        }

                    } catch (error) {
                        console.error('批量导入失败:', error);
                        ElementPlus.ElMessage.error('批量导入失败');
                    } finally {
                        importLoading.value = false;
                    }
                };

                // 显示编辑对话框
                const editToken = (token) => {
                    // 填充表单数据
                    editForm.id = token.id;
                    editForm.token = token.token;
                    editForm.tenant_url = token.tenant_url || '';
                    editForm.user_ck = '';
                    editDialogVisible.value = true;
                };

                // 提交编辑
                const submitEdit = async () => {
                    try {
                        // 验证表单
                        await editFormRef.value.validate();

                        editLoading.value = true;

                        const updateData = {};

                        // Token字段是必填的
                        if (editForm.token.trim()) {
                            updateData.token = editForm.token.trim();
                        }

                        if (editForm.tenant_url.trim()) {
                            updateData.tenant_url = editForm.tenant_url.trim();
                        }

                        // user_ck可以为空，所以需要特殊处理
                        updateData.user_ck = editForm.user_ck.trim() || null;

                        const response = await fetch(`/api/user/tokens/${editForm.id}`, {
                            method: 'PUT',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify(updateData)
                        });

                        const result = await response.json();

                        if (response.ok && result.success) {
                            ElementPlus.ElMessage.success('Token更新成功');
                            editDialogVisible.value = false;
                            await loadUserTokens();
                        } else {
                            ElementPlus.ElMessage.error(result.error || 'Token更新失败');
                        }

                    } catch (error) {
                        console.error('更新Token失败:', error);
                        ElementPlus.ElMessage.error('更新Token失败');
                    } finally {
                        editLoading.value = false;
                    }
                };

                // 导出Token为JSON文件
                const exportTokens = () => {
                    try {
                        if (tokens.value.length === 0) {
                            ElementPlus.ElMessage.warning('没有Token可以导出');
                            return;
                        }

                        // 准备导出数据，只包含必要字段
                        const exportData = tokens.value
                            .filter(token => token.is_active) // 只导出未删除的Token
                            .map(token => ({
                                id: token.id,
                                token: token.token,
                                tenant_url: token.tenant_url,
                                user_ck: token.user_ck,
                                created_at: token.created_at,
                                updated_at: token.updated_at
                            }));

                        if (exportData.length === 0) {
                            ElementPlus.ElMessage.warning('没有可用的Token可以导出');
                            return;
                        }

                        // 创建JSON文件
                        const jsonData = JSON.stringify(exportData, null, 2);
                        const blob = new Blob([jsonData], { type: 'application/json' });

                        // 创建下载链接
                        const url = URL.createObjectURL(blob);
                        const link = document.createElement('a');
                        link.href = url;

                        // 生成文件名（包含当前时间）
                        const now = new Date();
                        const timestamp = now.toISOString().slice(0, 19).replace(/[:-]/g, '');
                        link.download = `tokens_export_${timestamp}.json`;

                        // 触发下载
                        document.body.appendChild(link);
                        link.click();
                        document.body.removeChild(link);

                        // 清理URL对象
                        URL.revokeObjectURL(url);

                        ElementPlus.ElMessage.success(`成功导出 ${exportData.length} 个Token`);

                    } catch (error) {
                        console.error('导出Token失败:', error);
                        ElementPlus.ElMessage.error('导出Token失败');
                    }
                };

                // 账户绑定方法
                const bindLinuxDoAccount = async () => {
                    try {
                        bindingLoading.linuxdo = true;

                        const response = await fetch('/api/auth/linuxdo?bind=true', {
                            method: 'GET',
                            headers: {
                                'Content-Type': 'application/json',
                            }
                        });

                        if (response.ok) {
                            const data = await response.json();
                            if (data.auth_url) {
                                window.location.href = data.auth_url;
                            } else {
                                throw new Error('未获取到LinuxDo授权链接');
                            }
                        } else {
                            const errorData = await response.json();
                            throw new Error(errorData.error || 'LinuxDo绑定请求失败');
                        }
                    } catch (error) {
                        console.error('LinuxDo绑定错误:', error);
                        ElementPlus.ElMessage.error(error.message || '绑定失败，请重试');
                    } finally {
                        bindingLoading.linuxdo = false;
                    }
                };

                const bindGitHubAccount = async () => {
                    try {
                        bindingLoading.github = true;

                        const response = await fetch('/api/auth/github?bind=true', {
                            method: 'GET',
                            headers: {
                                'Content-Type': 'application/json',
                            }
                        });

                        if (response.ok) {
                            const data = await response.json();
                            if (data.auth_url) {
                                window.location.href = data.auth_url;
                            } else {
                                throw new Error('未获取到GitHub授权链接');
                            }
                        } else {
                            const errorData = await response.json();
                            throw new Error(errorData.error || 'GitHub绑定请求失败');
                        }
                    } catch (error) {
                        console.error('GitHub绑定错误:', error);
                        ElementPlus.ElMessage.error(error.message || '绑定失败，请重试');
                    } finally {
                        bindingLoading.github = false;
                    }
                };

                const unbindGitHubAccount = async () => {
                    try {
                        const confirmed = await ElementPlus.ElMessageBox.confirm(
                            '确定要解除GitHub账户绑定吗？解除后将无法使用GitHub账户登录。',
                            '确认解除绑定',
                            {
                                confirmButtonText: '确定',
                                cancelButtonText: '取消',
                                type: 'warning'
                            }
                        );

                        if (confirmed) {
                            bindingLoading.github = true;

                            const response = await fetch('/api/user/unbind/github', {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json',
                                }
                            });

                            if (response.ok) {
                                ElementPlus.ElMessage.success('GitHub账户解除绑定成功');
                                // 重新加载用户信息
                                await checkLoginStatus();
                            } else {
                                const errorData = await response.json();
                                throw new Error(errorData.error || '解除绑定失败');
                            }
                        }
                    } catch (error) {
                        if (error !== 'cancel') {
                            console.error('解除GitHub绑定错误:', error);
                            ElementPlus.ElMessage.error(error.message || '解除绑定失败，请重试');
                        }
                    } finally {
                        bindingLoading.github = false;
                    }
                };

                const checkLoginStatus = async () => {
                    try {
                        const response = await fetch('/api/user');
                        if (response.ok) {
                            const result = await response.json();
                            currentUser.value = result.user;
                            // 检查是否为管理员
                            await checkAdminStatus();
                            return true;
                        } else if (response.status === 401) {
                            window.location.href = '/login';
                            return false;
                        }
                    } catch (error) {
                        console.error('检查登录状态失败:', error);
                        return false;
                    }
                };

                const checkAdminStatus = async () => {
                    try {
                        const response = await fetch('/api/admin/me');
                        if (response.ok) {
                            const result = await response.json();
                            if (result.success) {
                                isAdmin.value = true;
                                console.log('管理员权限验证成功');
                            }
                        } else {
                            isAdmin.value = false;
                        }
                    } catch (error) {
                        isAdmin.value = false;
                        console.log('非管理员用户');
                    }
                };

                const loadUserTokens = async () => {
                    try {
                        const response = await fetch('/api/user/tokens');

                    // 公告精简逻辑
                    const announcements = ref([]);
                    const announcementDialogVisible = ref(false);
                    const hasUnreadAnnouncements = ref(false);

                    const loadAnnouncements = async () => {
                        try {
                            const resp = await fetch('/api/announcements/active');
                            if (resp.ok) {
                                const data = await resp.json();
                                announcements.value = data.data || [];
                                hasUnreadAnnouncements.value = announcements.value.length > 0;
                            }
                        } catch (e) { console.warn('加载公告失败(忽略):', e); }
                    };

                    const showAnnouncementDialog = () => {
                        if (announcements.value.length === 0) {
                            ElementPlus.ElMessage.info('暂无公告');
                        } else {
                            announcementDialogVisible.value = true;
                            hasUnreadAnnouncements.value = false;
                        }
                    };

                        if (response.ok) {
                            const result = await response.json();
                            tokens.value = result.tokens || [];
                        }
                    } catch (error) {
                        console.error('加载Token列表失败:', error);
                    }
                };

                // 公告相关方法
                const showAnnouncementDialog = () => {
                    if (announcements.value.length > 0) {
                        currentAnnouncementIndex.value = 0;
                        currentAnnouncement.value = announcements.value[0];
                        announcementDialogVisible.value = true;
                        hasUnreadAnnouncements.value = false;
                    } else {
                        ElementPlus.ElMessage.info('暂无公告');
                    }
                };

                const loadAnnouncements = async () => {
                    try {
                        const response = await fetch('/api/announcements/active');
                        if (response.ok) {
                            const data = await response.json();
                            announcements.value = data.data || [];
                            hasUnreadAnnouncements.value = announcements.value.length > 0;
                        }
                    } catch (error) {
                        console.error('加载公告失败:', error);
                    }
                };

                // 生命周期
                onMounted(async () => {
                    await checkLoginStatus();
                    await loadAnnouncements();
                    await loadUserTokens();

                    // 监听窗口大小变化
                    window.addEventListener('resize', () => {
                        if (!isMobile.value && mobileMenuOpen.value) {
                            mobileMenuOpen.value = false;
                        }
                    });
                });

                return {
                    isCollapsed,
                    mobileMenuOpen,
                    currentUser,
                    tokens,
                    activeMenu,
                    isMobile,
                    currentPage,
                    pageSize,
                    paginatedTokens,
                    isAdmin,
                    toggleSidebar,
                    toggleMobileSidebar,
                    closeMobileSidebar,
                    navigateTo,
                    refreshPage,
                    handleSizeChange,
                    handleCurrentChange,
                    handleLogout,
                    getTrustLevelName,
                    // 公告相关
                    announcements,
                    announcementDialogVisible,
                    currentAnnouncement,
                    currentAnnouncementIndex,
                    hasUnreadAnnouncements,
                    showAnnouncementDialog,
                    loadAnnouncements,
                    getAnnouncementTypeColor: (type) => {
                        const colors = { info: '', warning: 'warning', success: 'success', error: 'danger' };
                        return colors[type] || '';
                    },
                    getAnnouncementTypeText: (type) => {
                        const texts = { info: '信息', warning: '警告', success: '成功', error: '错误' };
                        return texts[type] || type;
                    },
                    formatDate: (dateString) => new Date(dateString).toLocaleDateString('zh-CN'),
                    formatDate,
                    copyToken,
                    deleteToken,
                    exportTokens,
                    // Token转让相关
                    transferDialogVisible,
                    transferring,
                    currentTransferToken,
                    searchQuery,
                    searchResults,
                    selectedRecipient,
                    searching,
                    transferToken,
                    searchUsers,
                    selectRecipient,
                    confirmTransfer,
                    // 导入Token相关
                    importDialogVisible,
                    importLoading,
                    importMode,
                    importForm,
                    importFormRef,
                    importRules,
                    showImportDialog,
                    submitImport,
                    // JSON导入相关
                    uploadRef,
                    parsedTokens,
                    jsonExample,
                    handleFileChange,
                    handleFileRemove,
                    submitJsonImport,
                    // 编辑Token相关
                    editDialogVisible,
                    editLoading,
                    editForm,
                    editFormRef,
                    editRules,
                    editToken,
                    submitEdit,
                    // 账户绑定相关
                    bindingLoading,
                    bindLinuxDoAccount,
                    bindGitHubAccount,
                    unbindGitHubAccount
                };
            }
        }).use(ElementPlus).use(ElementPlusIconsVue).mount('#app');
    </script>
</body>
</html>
