import React, { useEffect } from 'react'
import { motion } from 'framer-motion'
import Sidebar from './Sidebar'
import MobileSidebar from './MobileSidebar'
import Header from './Header'
import MobileOptimization from './MobileOptimization'
import { useAppStore } from '@/stores/appStore'

interface LayoutProps {
  children: React.ReactNode
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const { sidebarCollapsed, mobileMenuOpen, setMobileMenuOpen } = useAppStore()

  // 监听窗口大小变化，自动关闭移动端菜单
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth >= 768 && mobileMenuOpen) {
        setMobileMenuOpen(false)
      }
    }

    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [mobileMenuOpen, setMobileMenuOpen])

  // 点击遮罩层关闭移动端菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (mobileMenuOpen && window.innerWidth < 768) {
        const target = event.target as Element
        if (!target.closest('[data-sidebar]') && !target.closest('[data-menu-button]')) {
          setMobileMenuOpen(false)
        }
      }
    }

    document.addEventListener('click', handleClickOutside)
    return () => document.removeEventListener('click', handleClickOutside)
  }, [mobileMenuOpen, setMobileMenuOpen])

  return (
    <div className="h-screen flex bg-gray-50">
      {/* 桌面端侧边栏 */}
      <div className="hidden md:flex">
        <Sidebar />
      </div>

      {/* 移动端侧边栏 */}
      <MobileSidebar />

      {/* 主内容区域 */}
      <div className="flex-1 flex flex-col min-w-0">
        {/* 顶部导航栏 */}
        <Header />

        {/* 页面内容 */}
        <main className="flex-1 overflow-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
            className="h-full"
          >
            {children}
          </motion.div>
        </main>
      </div>

      {/* 移动端遮罩层 */}
      {mobileMenuOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden"
          onClick={() => setMobileMenuOpen(false)}
        />
      )}

      {/* 移动端优化组件 */}
      <MobileOptimization />
    </div>
  )
}

export default Layout
