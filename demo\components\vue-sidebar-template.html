<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vue侧边栏模板 - AugmentAPI</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>🧸</text></svg>">

    <!-- 引入Element Plus CSS - 使用BootCDN -->
    <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/element-plus/2.4.4/index.min.css">

    <!-- 引入Vue 3 - 使用BootCDN -->
    <script src="https://cdn.bootcdn.net/ajax/libs/vue/3.3.8/vue.global.min.js"></script>
    <!-- 引入Element Plus - 使用BootCDN -->
    <script src="https://cdn.bootcdn.net/ajax/libs/element-plus/2.4.4/index.full.min.js"></script>
    <!-- 引入Element Plus图标 - 使用BootCDN -->
    <script src="https://cdn.bootcdn.net/ajax/libs/element-plus-icons-vue/2.1.0/index.iife.min.js"></script>

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: #f5f7fa;
        }

        .layout-container {
            height: 100vh;
            display: flex;
        }

        .sidebar {
            background: #fff;
            box-shadow: 2px 0 6px rgba(0,21,41,.35);
            transition: width 0.3s;
            overflow: hidden;
            position: relative;
        }

        .sidebar.collapsed {
            width: 64px !important;
        }

        .sidebar.expanded {
            width: 250px !important;
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .content-header {
            background: #fff;
            padding: 16px 24px;
            border-bottom: 1px solid #e8eaec;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .content-body {
            flex: 1;
            padding: 24px;
            overflow-y: auto;
            background: #f5f7fa;
        }

        .page-card {
            background: #fff;
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .user-info {
            display: flex;
            align-items: center;
            padding: 16px;
            border-bottom: 1px solid #e8eaec;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin-right: 12px;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #409eff;
            color: white;
            font-weight: bold;
        }

        .user-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        /* 当有头像图片时，移除背景色 */
        .user-avatar:has(img) {
            background: transparent;
        }

        .user-details {
            flex: 1;
            min-width: 0;
        }

        .user-name {
            font-weight: 600;
            color: #303133;
            font-size: 14px;
            margin-bottom: 4px;
            display: flex;
            align-items: center;
        }

        .user-email {
            color: #909399;
            font-size: 12px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .trust-level {
            margin-left: 8px;
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 10px;
            font-weight: 600;
        }

        .trust-level-0 { background: #f4f4f5; color: #909399; }
        .trust-level-1 { background: #ecf5ff; color: #409eff; }
        .trust-level-2 { background: #f0f9ff; color: #67c23a; }
        .trust-level-3 { background: #fdf6ec; color: #e6a23c; }
        .trust-level-4 { background: #fef0f0; color: #f56c6c; }

        .sidebar-toggle {
            position: absolute;
            top: 16px;
            right: -12px;
            width: 24px;
            height: 24px;
            background: #409eff;
            border: none;
            border-radius: 50%;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            z-index: 1000;
        }

        .sidebar-toggle:hover {
            background: #337ecc;
        }

        /* 移动端适配 */
        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                left: 0;
                top: 0;
                height: 100vh;
                z-index: 1000;
                transform: translateX(-100%);
                transition: transform 0.3s;
            }

            .sidebar.mobile-open {
                transform: translateX(0);
            }

            .main-content {
                width: 100%;
            }

            .content-body {
                padding: 16px;
            }
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="layout-container">
            <!-- 侧边栏 -->
            <div class="sidebar" :class="{
                collapsed: isCollapsed,
                expanded: !isCollapsed,
                'mobile-open': mobileMenuOpen
            }">
                <button class="sidebar-toggle" @click="toggleSidebar">
                    {{ isCollapsed ? '→' : '←' }}
                </button>

                <!-- 用户信息 -->
                <div class="user-info" v-if="currentUser">
                    <div class="user-avatar">
                        <img v-if="currentUser.linuxdo_avatar" :src="currentUser.linuxdo_avatar" :alt="currentUser.linuxdo_username">
                        <span v-else>{{ currentUser.email ? currentUser.email.charAt(0).toUpperCase() : '?' }}</span>
                    </div>
                    <div class="user-details" v-show="!isCollapsed">
                        <div class="user-name">
                            {{ currentUser.linuxdo_username || currentUser.email.split('@')[0] }}
                            <span v-if="currentUser.linuxdo_trust_level !== null"
                                  :class="`trust-level trust-level-${currentUser.linuxdo_trust_level}`">
                                {{ getTrustLevelName(currentUser.linuxdo_trust_level) }}
                            </span>
                        </div>
                        <div class="user-email">{{ currentUser.email }}</div>
                    </div>
                </div>

                <!-- 导航菜单 -->
                <el-menu
                    :default-active="activeMenu"
                    :collapse="isCollapsed"
                    :unique-opened="true"
                    background-color="#fff"
                    text-color="#303133"
                    active-text-color="#409eff">

                    <el-menu-item index="home" @click="navigateTo('/')">
                        <el-icon><i class="el-icon-house"></i></el-icon>
                        <span>首页</span>
                    </el-menu-item>

                    <el-menu-item index="profile" @click="navigateTo('/profile')">
                        <el-icon><i class="el-icon-user"></i></el-icon>
                        <span>个人中心</span>
                    </el-menu-item>

                    <el-sub-menu index="api">
                        <template #title>
                            <el-icon><i class="el-icon-document"></i></el-icon>
                            <span>API管理</span>
                        </template>
                        <el-menu-item index="api-docs" @click="navigateTo('/api-docs')">
                            <el-icon><i class="el-icon-reading"></i></el-icon>
                            <span>API文档</span>
                        </el-menu-item>
                        <el-menu-item index="external-test" @click="navigateTo('/external-test')">
                            <el-icon><i class="el-icon-connection"></i></el-icon>
                            <span>外部接口测试</span>
                        </el-menu-item>
                    </el-sub-menu>

                    <el-menu-item index="docs" @click="navigateTo('/docs')">
                        <el-icon><i class="el-icon-folder"></i></el-icon>
                        <span>文档中心</span>
                    </el-menu-item>

                    <el-menu-item index="logout" @click="handleLogout">
                        <el-icon><i class="el-icon-switch-button"></i></el-icon>
                        <span>退出登录</span>
                    </el-menu-item>
                </el-menu>
            </div>

            <!-- 主内容区 -->
            <div class="main-content">
                <!-- 内容头部 -->
                <div class="content-header">
                    <div>
                        <el-button v-if="isMobile" @click="toggleMobileSidebar" icon="el-icon-menu" circle></el-button>
                        <span style="font-size: 18px; font-weight: 600; margin-left: 8px;">{{ pageTitle }}</span>
                    </div>
                    <div>
                        <el-button type="primary" icon="el-icon-refresh" @click="refreshPage" circle></el-button>
                    </div>
                </div>

                <!-- 内容主体 -->
                <div class="content-body">
                    <div class="page-card">
                        <h1>{{ pageTitle }}</h1>
                        <div class="content">
                            <p>这里是页面内容...</p>
                            <p>侧边栏可以通过点击箭头按钮进行折叠和展开。</p>
                            <p>在移动端会自动适配为抽屉式侧边栏。</p>
                            <p>当前用户: {{ currentUser ? currentUser.email : '未登录' }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 移动端遮罩 -->
        <div v-if="isMobile && mobileMenuOpen"
             style="position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.5); z-index: 999;"
             @click="closeMobileSidebar"></div>
    </div>

    <!-- CDN资源已由服务端自动注入到head部分 -->

    <script>
        const { createApp, ref, reactive, onMounted, computed } = Vue;

        createApp({
            setup() {
                // 响应式数据
                const isCollapsed = ref(false);
                const mobileMenuOpen = ref(false);
                const currentUser = ref(null);
                const pageTitle = ref('页面模板');
                const activeMenu = ref('home');

                // 计算属性
                const isMobile = computed(() => {
                    return window.innerWidth <= 768;
                });

                // 方法
                const toggleSidebar = () => {
                    isCollapsed.value = !isCollapsed.value;
                };

                const toggleMobileSidebar = () => {
                    mobileMenuOpen.value = !mobileMenuOpen.value;
                };

                const closeMobileSidebar = () => {
                    mobileMenuOpen.value = false;
                };

                const navigateTo = (path) => {
                    window.location.href = path;
                };

                const refreshPage = () => {
                    window.location.reload();
                };

                const handleLogout = async () => {
                    try {
                        const response = await fetch('/api/logout', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            }
                        });

                        if (response.ok) {
                            localStorage.removeItem('auth_token');
                            window.location.href = '/login';
                        } else {
                            ElementPlus.ElMessage.error('登出失败，请重试');
                        }
                    } catch (error) {
                        console.error('登出错误:', error);
                        ElementPlus.ElMessage.error('网络错误，请重试');
                    }
                };

                const getTrustLevelName = (level) => {
                    const levelNames = {
                        0: '新用户',
                        1: '基础',
                        2: '成员',
                        3: '常规',
                        4: '领导'
                    };
                    return levelNames[level] || '未知';
                };

                const checkLoginStatus = async () => {
                    try {
                        const response = await fetch('/api/user');
                        if (response.ok) {
                            const result = await response.json();
                            currentUser.value = result.user;
                            return true;
                        } else if (response.status === 401) {
                            window.location.href = '/login';
                            return false;
                        }
                    } catch (error) {
                        console.error('检查登录状态失败:', error);
                        return false;
                    }
                };

                // 设置当前激活菜单
                const setActiveMenu = () => {
                    const path = window.location.pathname;
                    if (path === '/') {
                        activeMenu.value = 'home';
                    } else if (path === '/profile') {
                        activeMenu.value = 'profile';
                    } else if (path === '/api-docs') {
                        activeMenu.value = 'api-docs';
                    } else if (path === '/external-test') {
                        activeMenu.value = 'external-test';
                    } else if (path === '/docs' || path.startsWith('/docs/')) {
                        activeMenu.value = 'docs';
                    } else if (path === '/admin') {
                        activeMenu.value = 'admin';
                    }
                };

                // 生命周期
                onMounted(async () => {
                    await checkLoginStatus();
                    setActiveMenu();

                    // 监听窗口大小变化
                    window.addEventListener('resize', () => {
                        if (!isMobile.value && mobileMenuOpen.value) {
                            mobileMenuOpen.value = false;
                        }
                    });
                });

                return {
                    isCollapsed,
                    mobileMenuOpen,
                    currentUser,
                    pageTitle,
                    activeMenu,
                    isMobile,
                    toggleSidebar,
                    toggleMobileSidebar,
                    closeMobileSidebar,
                    navigateTo,
                    refreshPage,
                    handleLogout,
                    getTrustLevelName
                };
            }
        }).use(ElementPlus).mount('#app');
    </script>
</body>
</html>
