import React, { useEffect, useState } from 'react'
import { useNavigate, useSearchParams } from 'react-router-dom'
import { motion } from 'framer-motion'
import { Github, MessageCircle, Loader2 } from 'lucide-react'
import { useAuthStore } from '@/stores/authStore'
import { authTokens } from '@/lib/auth'

const LoginPage: React.FC = () => {
  const navigate = useNavigate()
  const [searchParams] = useSearchParams()
  const { isAuthenticated, checkAuth } = useAuthStore()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // 检查是否已登录
  useEffect(() => {
    const checkAuthStatus = async () => {
      const isAuth = await checkAuth()
      if (isAuth) {
        const redirect = searchParams.get('redirect') || '/'
        navigate(redirect, { replace: true })
      }
    }
    checkAuthStatus()
  }, [checkAuth, navigate, searchParams])

  // 处理登录回调
  useEffect(() => {
    const token = searchParams.get('token')
    if (token) {
      setLoading(true)
      authTokens.set(token)
      checkAuth().then((success) => {
        if (success) {
          const redirect = searchParams.get('redirect') || '/'
          navigate(redirect, { replace: true })
        } else {
          setError('登录验证失败，请重试')
        }
        setLoading(false)
      })
    }
  }, [searchParams, checkAuth, navigate])

  const handleLinuxDoLogin = () => {
    setLoading(true)
    setError(null)
    const redirect = searchParams.get('redirect') || '/'
    window.location.href = `/api/auth/linuxdo?redirect=${encodeURIComponent(redirect)}`
  }

  const handleGithubLogin = () => {
    setLoading(true)
    setError(null)
    const redirect = searchParams.get('redirect') || '/'
    window.location.href = `/api/auth/github?redirect=${encodeURIComponent(redirect)}`
  }

  if (isAuthenticated) {
    return null // 已登录，等待重定向
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="bg-white rounded-2xl shadow-xl p-8 w-full max-w-md"
      >
        {/* Logo和标题 */}
        <div className="text-center mb-8">
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
            className="w-16 h-16 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full mx-auto mb-4 flex items-center justify-center"
          >
            <span className="text-white text-2xl font-bold">A</span>
          </motion.div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            欢迎使用 AugmentAPI
          </h1>
          <p className="text-gray-600">
            智能编程助手API管理平台
          </p>
        </div>

        {/* 错误提示 */}
        {error && (
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6"
          >
            {error}
          </motion.div>
        )}

        {/* 登录按钮 */}
        <div className="space-y-4">
          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={handleLinuxDoLogin}
            disabled={loading}
            className="w-full bg-gradient-to-r from-orange-500 to-red-500 text-white py-3 px-4 rounded-lg font-medium flex items-center justify-center space-x-2 hover:from-orange-600 hover:to-red-600 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? (
              <Loader2 className="w-5 h-5 animate-spin" />
            ) : (
              <>
                <MessageCircle className="w-5 h-5" />
                <span>使用 LinuxDo 登录</span>
              </>
            )}
          </motion.button>

          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={handleGithubLogin}
            disabled={loading}
            className="w-full bg-gray-900 text-white py-3 px-4 rounded-lg font-medium flex items-center justify-center space-x-2 hover:bg-gray-800 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? (
              <Loader2 className="w-5 h-5 animate-spin" />
            ) : (
              <>
                <Github className="w-5 h-5" />
                <span>使用 GitHub 登录</span>
              </>
            )}
          </motion.button>
        </div>

        {/* 说明文字 */}
        <div className="mt-8 text-center">
          <p className="text-sm text-gray-500 mb-4">
            登录即表示您同意我们的服务条款和隐私政策
          </p>
          
          {/* 社区信息 */}
          <div className="border-t pt-6">
            <p className="text-sm text-gray-600 mb-3">
              加入我们的技术交流群
            </p>
            <div className="flex justify-center space-x-4">
              <div className="text-center">
                <div className="w-16 h-16 bg-gray-100 rounded-lg mx-auto mb-2 flex items-center justify-center">
                  <MessageCircle className="w-8 h-8 text-gray-400" />
                </div>
                <p className="text-xs text-gray-500">技术交流群</p>
              </div>
              <div className="text-center">
                <div className="w-16 h-16 bg-gray-100 rounded-lg mx-auto mb-2 flex items-center justify-center">
                  <span className="text-gray-400 text-xs">赞助</span>
                </div>
                <p className="text-xs text-gray-500">支持项目</p>
              </div>
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  )
}

export default LoginPage
