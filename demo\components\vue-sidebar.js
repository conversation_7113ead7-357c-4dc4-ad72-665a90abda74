/**
 * Vue侧边栏组件
 * 使用方法：
 * 1. 在HTML中引入Element Plus CSS和Vue 3
 * 2. 引入此组件文件
 * 3. 调用 createSidebarApp(pageTitle, activeMenu, customContent)
 */

// 侧边栏样式
const sidebarStyles = `
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }
    
    body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
        background: #f5f7fa;
    }
    
    .layout-container {
        height: 100vh;
        display: flex;
    }
    
    .sidebar {
        background: #fff;
        box-shadow: 2px 0 6px rgba(0,21,41,.35);
        transition: width 0.3s;
        overflow: hidden;
        position: relative;
    }
    
    .sidebar.collapsed {
        width: 64px !important;
    }
    
    .sidebar.expanded {
        width: 250px !important;
    }
    
    .main-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;
    }
    
    .content-header {
        background: #fff;
        padding: 16px 24px;
        border-bottom: 1px solid #e8eaec;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
    
    .content-body {
        flex: 1;
        padding: 24px;
        overflow-y: auto;
        background: #f5f7fa;
    }
    
    .user-info {
        display: flex;
        align-items: center;
        padding: 16px;
        border-bottom: 1px solid #e8eaec;
    }
    
    .user-avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        margin-right: 12px;
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #409eff;
        color: white;
        font-weight: bold;
    }

    .user-avatar img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    /* 当有头像图片时，移除背景色 */
    .user-avatar:has(img) {
        background: transparent;
    }
    
    .user-details {
        flex: 1;
        min-width: 0;
    }
    
    .user-name {
        font-weight: 600;
        color: #303133;
        font-size: 14px;
        margin-bottom: 4px;
        display: flex;
        align-items: center;
    }
    
    .user-email {
        color: #909399;
        font-size: 12px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    
    .trust-level {
        margin-left: 8px;
        padding: 2px 6px;
        border-radius: 10px;
        font-size: 10px;
        font-weight: 600;
    }
    
    .trust-level-0 { background: #f4f4f5; color: #909399; }
    .trust-level-1 { background: #ecf5ff; color: #409eff; }
    .trust-level-2 { background: #f0f9ff; color: #67c23a; }
    .trust-level-3 { background: #fdf6ec; color: #e6a23c; }
    .trust-level-4 { background: #fef0f0; color: #f56c6c; }
    
    .sidebar-toggle {
        position: absolute;
        top: 16px;
        right: -12px;
        width: 24px;
        height: 24px;
        background: #409eff;
        border: none;
        border-radius: 50%;
        color: white;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        z-index: 1000;
    }
    
    .sidebar-toggle:hover {
        background: #337ecc;
    }
    
    /* 移动端适配 */
    @media (max-width: 768px) {
        .sidebar {
            position: fixed;
            left: 0;
            top: 0;
            height: 100vh;
            z-index: 1000;
            transform: translateX(-100%);
            transition: transform 0.3s;
        }
        
        .sidebar.mobile-open {
            transform: translateX(0);
        }
        
        .main-content {
            width: 100%;
        }
        
        .content-body {
            padding: 16px;
        }
    }
`;

// 侧边栏HTML模板
const sidebarTemplate = `
    <div class="layout-container">
        <!-- 侧边栏 -->
        <div class="sidebar" :class="{ 
            collapsed: isCollapsed, 
            expanded: !isCollapsed,
            'mobile-open': mobileMenuOpen 
        }">
            <button class="sidebar-toggle" @click="toggleSidebar">
                {{ isCollapsed ? '→' : '←' }}
            </button>
            
            <!-- 用户信息 -->
            <div class="user-info" v-if="currentUser">
                <div class="user-avatar">
                    <img v-if="currentUser.linuxdo_avatar" :src="currentUser.linuxdo_avatar" :alt="currentUser.linuxdo_username">
                    <span v-else>{{ currentUser.email ? currentUser.email.charAt(0).toUpperCase() : '?' }}</span>
                </div>
                <div class="user-details" v-show="!isCollapsed">
                    <div class="user-name">
                        {{ currentUser.linuxdo_username || currentUser.email.split('@')[0] }}
                        <span v-if="currentUser.linuxdo_trust_level !== null" 
                              :class="\`trust-level trust-level-\${currentUser.linuxdo_trust_level}\`">
                            {{ getTrustLevelName(currentUser.linuxdo_trust_level) }}
                        </span>
                    </div>
                    <div class="user-email">{{ currentUser.email }}</div>
                </div>
            </div>
            
            <!-- 导航菜单 -->
            <el-menu 
                :default-active="activeMenu"
                :collapse="isCollapsed"
                :unique-opened="true"
                background-color="#fff"
                text-color="#303133"
                active-text-color="#409eff">
                
                <el-menu-item index="home" @click="navigateTo('/')">
                    <el-icon><i class="el-icon-house"></i></el-icon>
                    <span>首页</span>
                </el-menu-item>
                
                <el-menu-item index="profile" @click="navigateTo('/profile')">
                    <el-icon><i class="el-icon-user"></i></el-icon>
                    <span>个人中心</span>
                </el-menu-item>

                <el-menu-item index="chat" @click="navigateTo('/chat')">
                    <el-icon><i class="el-icon-chat-dot-round"></i></el-icon>
                    <span>实时聊天</span>
                </el-menu-item>

                <el-sub-menu index="api">
                    <template #title>
                        <el-icon><i class="el-icon-document"></i></el-icon>
                        <span>API管理</span>
                    </template>
                    <el-menu-item index="api-docs" @click="navigateTo('/api-docs')">
                        <el-icon><i class="el-icon-reading"></i></el-icon>
                        <span>API文档</span>
                    </el-menu-item>
                    <el-menu-item index="external-test" @click="navigateTo('/external-test')">
                        <el-icon><i class="el-icon-connection"></i></el-icon>
                        <span>外部接口测试</span>
                    </el-menu-item>
                </el-sub-menu>

                <el-menu-item index="docs" @click="navigateTo('/docs')">
                    <el-icon><i class="el-icon-folder"></i></el-icon>
                    <span>文档中心</span>
                </el-menu-item>

                <!-- 管理后台菜单项（仅管理员可见） -->
                <el-menu-item v-if="currentUser && currentUser.isAdmin" index="admin" @click="navigateTo('/admin')">
                    <el-icon><i class="el-icon-setting"></i></el-icon>
                    <span>管理后台</span>
                </el-menu-item>

                <el-menu-item index="logout" @click="handleLogout">
                    <el-icon><i class="el-icon-switch-button"></i></el-icon>
                    <span>退出登录</span>
                </el-menu-item>
            </el-menu>
        </div>
        
        <!-- 主内容区 -->
        <div class="main-content">
            <!-- 内容头部 -->
            <div class="content-header">
                <div>
                    <el-button v-if="isMobile" @click="toggleMobileSidebar" icon="el-icon-menu" circle></el-button>
                    <span style="font-size: 18px; font-weight: 600; margin-left: 8px;">{{ pageTitle }}</span>
                </div>
                <div>
                    <el-button type="primary" icon="el-icon-refresh" @click="refreshPage" circle></el-button>
                </div>
            </div>
            
            <!-- 内容主体 -->
            <div class="content-body">
                <slot></slot>
            </div>
        </div>
    </div>
    
    <!-- 移动端遮罩 -->
    <div v-if="isMobile && mobileMenuOpen" 
         style="position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.5); z-index: 999;"
         @click="closeMobileSidebar"></div>
`;

// 创建侧边栏应用的函数
function createSidebarApp(pageTitle, activeMenu, customSetup = {}) {
    // 添加样式到页面
    if (!document.getElementById('sidebar-styles')) {
        const style = document.createElement('style');
        style.id = 'sidebar-styles';
        style.textContent = sidebarStyles;
        document.head.appendChild(style);
    }
    
    const { createApp, ref, computed, onMounted } = Vue;
    
    return createApp({
        template: sidebarTemplate,
        setup() {
            // 基础响应式数据
            const isCollapsed = ref(false);
            const mobileMenuOpen = ref(false);
            const currentUser = ref(null);
            const activeMenu = ref('home');
            
            // 计算属性
            const isMobile = computed(() => {
                return window.innerWidth <= 768;
            });
            
            // 基础方法
            const toggleSidebar = () => {
                isCollapsed.value = !isCollapsed.value;
            };
            
            const toggleMobileSidebar = () => {
                mobileMenuOpen.value = !mobileMenuOpen.value;
            };
            
            const closeMobileSidebar = () => {
                mobileMenuOpen.value = false;
            };
            
            const navigateTo = (path) => {
                window.location.href = path;
            };
            
            const refreshPage = () => {
                window.location.reload();
            };
            
            const handleLogout = async () => {
                try {
                    const response = await fetch('/api/logout', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        }
                    });
                    
                    if (response.ok) {
                        localStorage.removeItem('auth_token');
                        window.location.href = '/login';
                    } else {
                        ElementPlus.ElMessage.error('登出失败，请重试');
                    }
                } catch (error) {
                    console.error('登出错误:', error);
                    ElementPlus.ElMessage.error('网络错误，请重试');
                }
            };
            
            const getTrustLevelName = (level) => {
                const levelNames = {
                    0: '新用户',
                    1: '基础',
                    2: '成员',
                    3: '常规',
                    4: '领导'
                };
                return levelNames[level] || '未知';
            };
            
            const checkLoginStatus = async () => {
                try {
                    const response = await fetch('/api/user');
                    if (response.ok) {
                        const result = await response.json();
                        currentUser.value = result.user;
                        return true;
                    } else if (response.status === 401) {
                        window.location.href = '/login';
                        return false;
                    }
                } catch (error) {
                    console.error('检查登录状态失败:', error);
                    return false;
                }
            };

            // 设置当前激活菜单
            const setActiveMenu = () => {
                const path = window.location.pathname;
                if (path === '/') {
                    activeMenu.value = 'home';
                } else if (path === '/profile') {
                    activeMenu.value = 'profile';
                } else if (path === '/chat' || path === '/chat.html') {
                    activeMenu.value = 'chat';
                } else if (path === '/api-docs') {
                    activeMenu.value = 'api-docs';
                } else if (path === '/external-test') {
                    activeMenu.value = 'external-test';
                } else if (path === '/docs' || path.startsWith('/docs/')) {
                    activeMenu.value = 'docs';
                } else if (path === '/admin') {
                    activeMenu.value = 'admin';
                }
            };
            
            // 生命周期
            onMounted(async () => {
                await checkLoginStatus();
                setActiveMenu();

                // 监听窗口大小变化
                window.addEventListener('resize', () => {
                    if (!isMobile.value && mobileMenuOpen.value) {
                        mobileMenuOpen.value = false;
                    }
                });

                // 执行自定义初始化
                if (customSetup.onMounted) {
                    customSetup.onMounted();
                }
            });
            
            // 返回基础数据和方法，以及自定义数据
            return {
                pageTitle,
                activeMenu,
                isCollapsed,
                mobileMenuOpen,
                currentUser,
                isMobile,
                toggleSidebar,
                toggleMobileSidebar,
                closeMobileSidebar,
                navigateTo,
                refreshPage,
                handleLogout,
                getTrustLevelName,
                setActiveMenu,
                ...customSetup.data
            };
        }
    });
}

// 导出到全局
window.createSidebarApp = createSidebarApp;
