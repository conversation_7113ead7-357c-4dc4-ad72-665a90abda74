import React, { useState } from 'react'
import { motion } from 'framer-motion'
import { 
  Shield, 
  Users, 
  Megaphone, 
  BarChart3, 
  Settings,
  Database,
  FileText,
  Activity
} from 'lucide-react'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import UserManagement from '@/components/UserManagement'
import AnnouncementManagement from '@/components/AnnouncementManagement'
import SystemStats from '@/components/SystemStats'
import { useAuthStore } from '@/stores/authStore'

const AdminPage: React.FC = () => {
  const { user } = useAuthStore()
  const [activeTab, setActiveTab] = useState('overview')

  // 检查管理员权限
  if (!user?.isAdmin) {
    return (
      <div className="p-6">
        <div className="max-w-md mx-auto text-center">
          <div className="w-16 h-16 bg-red-100 rounded-full mx-auto mb-4 flex items-center justify-center">
            <Shield className="w-8 h-8 text-red-500" />
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            访问被拒绝
          </h2>
          <p className="text-gray-600 mb-4">
            您没有权限访问管理后台，需要管理员权限。
          </p>
          <button
            onClick={() => window.history.back()}
            className="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors"
          >
            返回上一页
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6 max-w-7xl mx-auto">
      {/* 页面标题 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="mb-8"
      >
        <div className="flex items-center space-x-3 mb-2">
          <div className="w-10 h-10 bg-gradient-to-r from-red-500 to-pink-600 rounded-lg flex items-center justify-center">
            <Shield className="w-5 h-5 text-white" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">管理后台</h1>
            <p className="text-gray-600">系统管理和监控中心</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Badge variant="destructive" className="flex items-center space-x-1">
            <Shield className="w-3 h-3" />
            <span>管理员</span>
          </Badge>
          <span className="text-sm text-gray-500">
            欢迎，{user.linuxdo_username || user.email}
          </span>
        </div>
      </motion.div>

      {/* 主要内容区域 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
      >
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview" className="flex items-center space-x-2">
              <BarChart3 className="w-4 h-4" />
              <span>概览</span>
            </TabsTrigger>
            <TabsTrigger value="users" className="flex items-center space-x-2">
              <Users className="w-4 h-4" />
              <span>用户管理</span>
            </TabsTrigger>
            <TabsTrigger value="announcements" className="flex items-center space-x-2">
              <Megaphone className="w-4 h-4" />
              <span>公告管理</span>
            </TabsTrigger>
            <TabsTrigger value="system" className="flex items-center space-x-2">
              <Settings className="w-4 h-4" />
              <span>系统设置</span>
            </TabsTrigger>
          </TabsList>

          {/* 概览页面 */}
          <TabsContent value="overview">
            <div className="space-y-6">
              {/* 快速统计 */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-sm font-medium text-gray-600 flex items-center space-x-2">
                      <Users className="w-4 h-4" />
                      <span>总用户数</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-blue-600">1,247</div>
                    <p className="text-xs text-gray-500 mt-1">+12 今日新增</p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-sm font-medium text-gray-600 flex items-center space-x-2">
                      <Activity className="w-4 h-4" />
                      <span>在线用户</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-green-600">89</div>
                    <p className="text-xs text-gray-500 mt-1">活跃用户</p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-sm font-medium text-gray-600 flex items-center space-x-2">
                      <Megaphone className="w-4 h-4" />
                      <span>系统公告</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-purple-600">3</div>
                    <p className="text-xs text-gray-500 mt-1">1 条置顶</p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-sm font-medium text-gray-600 flex items-center space-x-2">
                      <Database className="w-4 h-4" />
                      <span>API调用</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-orange-600">89.4K</div>
                    <p className="text-xs text-gray-500 mt-1">+1.2K 今日</p>
                  </CardContent>
                </Card>
              </div>

              {/* 系统状态 */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <Activity className="w-5 h-5" />
                      <span>系统状态</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">服务状态</span>
                      <Badge className="bg-green-100 text-green-700">正常运行</Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">数据库连接</span>
                      <Badge className="bg-green-100 text-green-700">已连接</Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">缓存服务</span>
                      <Badge className="bg-green-100 text-green-700">正常</Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">消息队列</span>
                      <Badge className="bg-green-100 text-green-700">正常</Badge>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <FileText className="w-5 h-5" />
                      <span>最近活动</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex items-center space-x-3 text-sm">
                      <div className="w-2 h-2 bg-blue-500 rounded-full" />
                      <span className="text-gray-600">用户 user123 注册了账户</span>
                      <span className="text-gray-400">2分钟前</span>
                    </div>
                    <div className="flex items-center space-x-3 text-sm">
                      <div className="w-2 h-2 bg-green-500 rounded-full" />
                      <span className="text-gray-600">发布了新的系统公告</span>
                      <span className="text-gray-400">1小时前</span>
                    </div>
                    <div className="flex items-center space-x-3 text-sm">
                      <div className="w-2 h-2 bg-orange-500 rounded-full" />
                      <span className="text-gray-600">API调用量达到新高</span>
                      <span className="text-gray-400">3小时前</span>
                    </div>
                    <div className="flex items-center space-x-3 text-sm">
                      <div className="w-2 h-2 bg-purple-500 rounded-full" />
                      <span className="text-gray-600">系统维护完成</span>
                      <span className="text-gray-400">1天前</span>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* 快速操作 */}
              <Card>
                <CardHeader>
                  <CardTitle>快速操作</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <button
                      onClick={() => setActiveTab('users')}
                      className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left"
                    >
                      <Users className="w-6 h-6 text-blue-500 mb-2" />
                      <div className="text-sm font-medium">用户管理</div>
                      <div className="text-xs text-gray-500">管理用户账户</div>
                    </button>
                    <button
                      onClick={() => setActiveTab('announcements')}
                      className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left"
                    >
                      <Megaphone className="w-6 h-6 text-green-500 mb-2" />
                      <div className="text-sm font-medium">发布公告</div>
                      <div className="text-xs text-gray-500">发布系统通知</div>
                    </button>
                    <button className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left">
                      <BarChart3 className="w-6 h-6 text-purple-500 mb-2" />
                      <div className="text-sm font-medium">查看报告</div>
                      <div className="text-xs text-gray-500">系统使用报告</div>
                    </button>
                    <button
                      onClick={() => setActiveTab('system')}
                      className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left"
                    >
                      <Settings className="w-6 h-6 text-orange-500 mb-2" />
                      <div className="text-sm font-medium">系统设置</div>
                      <div className="text-xs text-gray-500">配置系统参数</div>
                    </button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* 用户管理 */}
          <TabsContent value="users">
            <UserManagement />
          </TabsContent>

          {/* 公告管理 */}
          <TabsContent value="announcements">
            <AnnouncementManagement />
          </TabsContent>

          {/* 系统设置 */}
          <TabsContent value="system">
            <SystemStats />
          </TabsContent>
        </Tabs>
      </motion.div>
    </div>
  )
}

export default AdminPage
